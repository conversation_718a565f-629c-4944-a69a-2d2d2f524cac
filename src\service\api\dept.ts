import { request } from '../request';

/**
 * 部门列表
 */
export function fetchDeptList(params: Api.List.Table & { deptName: string }) {
  return request({
    url: '/system/dept/list',
    method: 'get',
    params
  });
}

/**
 * 新增部门
 */
export function addDept(data: Api.Dept.DeptItem) {
  return request({
    url: '/system/dept',
    method: 'post',
    data
  });
}

/**
 * 编辑部门
 */
export function editDept(data: Api.Dept.DeptItem) {
  return request({
    url: '/system/dept',
    method: 'put',
    data
  });
}

/**
 * 部门详情
 */
export function deptMessage(deptId: number) {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'get'
  });
}

/**
 * 删除部门
 */
export function delDept(deptId: number) {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'delete'
  });
}
