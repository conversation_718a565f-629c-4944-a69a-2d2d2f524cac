<script setup lang="ts">
import type { Ref } from 'vue';
import { inject, onBeforeUnmount, onMounted, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { useMessage } from 'naive-ui';
import { useAirportStore } from '@/store/modules/airport';
import { fetchAIList } from '@/service/api/ai';
import { useDeviceStore } from '@/store/modules/device';

const deviceStore = useDeviceStore();
const AIList = ref<Api.AI.AIItem[]>([]);
const props = defineProps({
  targetPointVlaue: {
    type: Object,
    required: true
  }
})
defineOptions({
  name: 'FlyInfo'
});
const emits = defineEmits(['focus-airport']);
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});
// 定义组件接受的属性
// const props = defineProps<{
//   airportFlyInfo: Api.Airport.AirportDeviceInfo[];
// }>();

function onClickAirPort(index: number) {
  console.log('onClickAirPort ~ index:', index);
  // airportStore.addVideoBox(props.airportFlyInfo[index]);
}

function onFocusAirport(index: number) {
  console.log('onFocusAirport ~ index:', index);
  emits('focus-airport', index);
  // airportStore.addVideoBox(props.airportFlyInfo[index]);
}

async function getAIList() {
  const { data } = await fetchAIList();
  console.log('getAIList ~ data:', data.rows);
  // data.rows.forEach((item:Api.AI.AIItem) => { item.status = Boolean(item.status) });
  AIList.value = data.rows;
}


// 发送直播关闭信号

const isMinimized = ref(false); // 是否最小化
const toggleMinimize = () => {
  console.log('toggleMinimize: ', isMinimized.value);
  isMinimized.value = !isMinimized.value;
};

onMounted(() => {
});

onBeforeUnmount(() => {
});


</script>

<template>
  <div class="absolute right-[calc(50vw+5vw-45px)] bottom-1 h-max w-max  text-light zindex-1 rd-t">

    <n-flex justify="end">
      <div class="">
        <!-- 机场数据 -->
        <div class="absolute top-0 right-450px w-max">
          <!-- 经纬度 -->
          <div class="flex items-center p-[0.47vh]">
            <SvgIcon icon="mdi:crosshairs-gps" class="mr-[0.27vw] text-lg text-gray" size="24" />
            <NText class="pr-[0.82vw] color-light">
              纬度：{{ Number(deviceStore.dockInfo?.latitude || airportDockItem.host?.latitude)?.toFixed(6) }}
            </NText>
            <NText class="color-light">
              经度：{{ Number(deviceStore.dockInfo?.longitude || airportDockItem.host?.longitude)?.toFixed(6) }}
            </NText>
          </div>

          <!-- 详细信息栏 -->
          <n-flex class="p-[0.47vh] justify-between">
            <!-- GNSS -->
            <div class="flex items-center w-40% text-left">
              <SvgIcon icon="mdi:satellite-uplink" class="mr-[0.27vw] text-lg text-gray" size="24" />
              <NText class="text-14px text-gray">GNSS：</NText>
              <NText class="text-12px color-light font-black">{{ airportDockItem.host?.gpsNumber || 0 }}</NText>
            </div>
            <!-- RTK -->
            <div class="flex items-center text-left">
              <SvgIcon icon="mdi:alpha-r-box" class="mr-[0.27vw] text-lg text-gray" size="24" />
              <NText class="text-14px text-gray font-700 font-mono">RTK：</NText>
              <NText class="text-12px color-light font-black">{{ airportDockItem.host?.rtkNumber || '--' }} FIX
              </NText>
            </div>
          </n-flex>

          <n-flex class="p-[0.47vh] justify-between">
            <!-- 图传 -->
            <div class="flex items-center w-40% text-left">
              <SvgIcon icon="mdi:cloud-upload" class="mr-[0.27vw] text-lg text-gray" size="24" />
              <NText class="text-14px text-gray">图传：</NText>
              <NText class="text-12px color-light font-black">4G</NText>
            </div>
            <!-- 海拔 -->
            <div class="flex items-center w-54% text-left whitespace-nowrap">
              <SvgIcon icon="mdi:image-filter-hdr" class="mr-[0.27vw] text-lg text-gray" size="24" />
              <NText class="text-14px text-gray">海拔高度：</NText>
              <NText class="text-12px color-light font-black">{{ (Number(airportDockItem.host?.height || 0) ??
                0).toFixed(1) }} m</NText>
            </div>
          </n-flex>

          <n-flex class="p-[0.47vh]  justify-between">
            <!-- 电池 -->
            <div class="flex items-center flex-1 text-left">
              <SvgIcon
                :icon="Number(props.targetPointVlaue.batteryCapacityPercent) >= 40 ? 'mdi:battery-charging' : 'mdi:battery-80'"
                class="mr-[0.27vw] text-lg"
                :class="[Number(props.targetPointVlaue.batteryCapacityPercent) >= 40 ? 'text-green-500' : 'text-gray']"
                size="24" />
              <NText class="text-14px text-gray" style="cursor: pointer">电池：</NText>
              <NText class="text-12px text-#ffffff font-black pr-5px"> {{ props.targetPointVlaue.batteryCapacityPercent
                || 0 }}%
              </NText>
              <span
                v-if="airportDroneItem.host?.battery?.batteries && airportDroneItem.host.battery.batteries.length > 0">
                <span v-for="item in airportDroneItem.host.battery.batteries" :key="item.temperature">
                  <NText class="text-12px text-gray">
                    {{ item.voltage ? (item.voltage / 1000).toFixed(1) : '--' }}V
                    {{ item.temperature ? item.temperature : '--' }}℃
                  </NText>
                </span>
              </span>
            </div>
          </n-flex>
        </div>
      </div>


      <div class="relative h-234px w-416px">
        <!-- 机场视频窗口 -->
        <div v-show="!isMinimized" class="h-234px w-416px bg-dark-theme relative ">
          <div class="text-light absolute top-0 left-0 lh-234px h-234px w-416px text-center bg-dark-theme zindex-1">
            设备暂未开始直播</div>
        </div>

        <div v-show="isMinimized" class="h-234px w-416px">
          <div @click="toggleMinimize"
            class="absolute bottom-0 right-0 text-center cursor-pointer p-5px bg-dark-theme rounded-lg shadow-lg">
            <SvgIcon icon="mdi:train-car-box-full" size="34"
              class="text-gray-300 hover:text-gray-400 transition-colors duration-200 h-12 w-12" />
            <n-text>机场</n-text>
          </div>
        </div>
      </div>
    </n-flex>
  </div>
</template>

<style>
/* .myscrollbar {
  max-height: calc(100vh - 610px);
  overflow: hidden;
} */
</style>

<style scoped>
#player_dock>div:not(:last-child) {
  display: none;
}
</style>
