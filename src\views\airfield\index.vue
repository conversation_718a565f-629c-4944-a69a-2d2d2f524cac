<script setup lang="ts">
import * as Cesium from 'cesium';
import { onMounted, ref } from 'vue';
import { useMessage, darkTheme } from 'naive-ui';
import marker from "@/assets/imgs/airport.png";
import { cesiumConfig, josiahProvider, labelProvider, position } from '@/config/cesium_config';
import { fetchDockList } from '@/service/api';

let viewer: Cesium.Viewer;
const loaded = ref(false);

const points = ref<Api.Airport.MachineNestInfo[]>([]);

// 初始化地图
const initCesium = () => {
  viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; // 修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  loaded.value = true;

  // 修改图层
  // viewer.imageryLayers.removeAll();
  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider);

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置相机位置为当前定位点
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(position.coords.longitude, position.coords.latitude, 60000), // 设置目的地高度/米
    orientation: {
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });
  loadJSONAndDrawNoFlyZone();
};

// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
  try {
    const response = await fetch('/defaultNoFlyZone.json');
    const data = await response.json();
    // 绘制
    addPolygons(data.features);
  } catch (error) {
    console.error('Error loading JSON data:', error);
  }
}

// 绘制禁飞区
function addPolygons(features: any[]) {
  features.forEach(feature => {
    const polygon = feature.geometry.coordinates[0];
    const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    viewer.entities.add({
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString('red').withAlpha(0.35)
      }
    });
  });
}

// 生成机场范围椭圆的轮廓点
function computeEllipsePositions(
  center: { longitude: number; latitude: number },
  semiMajorAxis: number,
  semiMinorAxis: number,
  granularity: number
): Cesium.Cartesian3[] {
  const positions: Cesium.Cartesian3[] = [];
  for (let i = 0; i < 360; i += granularity) {
    const radians = Cesium.Math.toRadians(i);
    const x = semiMajorAxis * Math.cos(radians);
    const y = semiMinorAxis * Math.sin(radians);
    // 将米转换为经纬度
    const position = Cesium.Cartesian3.fromDegrees(center.longitude + x / 111319.9, center.latitude + y / 111319.9);
    positions.push(position);
  }
  // 闭合椭圆，使首尾相连
  positions.push(positions[0]);
  return positions;
}

// 绘制机场点位及范围
function drawAirportPoint() {
  viewer.entities.removeAll();
  points.value.forEach((point: Api.Airport.MachineNestInfo) => {
    const { longitude, latitude, distance, deviceName, bgPointColor } = point;
    const center = { longitude, latitude }; // 明确指定中心点类型

    // 生成椭圆边界点
    const positions = computeEllipsePositions(center, distance, distance, 5);

    // 绘制机场范围（用Polygon模拟椭圆）
    viewer.entities.add({
      polygon: {
        hierarchy: positions, // 多边形的顶点
        material: Cesium.Color.fromCssColorString(bgPointColor).withAlpha(0.25), // 设置填充颜色
        perPositionHeight: false // 使多边形所有点贴地
      },
      description: 'airpoint'
    });

    // 绘制椭圆边框
    viewer.entities.add({
      polyline: {
        positions,
        width: 5,
        material: Cesium.Color.fromCssColorString('#1177fb'), // 边框颜色
        clampToGround: true // 使边框贴地
      },
      description: 'airpointborder'
    });

    // 绘制机场图标
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      billboard: {
        image: marker,
        scale: 0.13
      },
      description: 'airpointicon'
    });

    // 绘制机场标签
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      label: {
        text: deviceName,
        font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
        fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
        outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 8.0,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        pixelOffset: new Cesium.Cartesian2(0, -50) // 向上偏移50像素
      },
      description: 'airpointtext'
    });
  });
}



async function getDockList() {
  const { error, data } = await fetchDockList();
  if (!error) {
    points.value = data.rows.map((item: any) => ({
      id: item.id,
      longitude: item.longitude,
      latitude: item.latitude,
      distance: item.distance,
      autoWindSpeed: item.autoWindSpeed,
      deviceName: item.deviceName,
      deviceSn: item.deviceSn,
      bgPointColor: '#6ea9e6'
    }));
    drawAirportPoint();
  }
}

onMounted(async () => {
  initCesium();
  await getDockList();
});
</script>

<template>
  <div class="position-relative h100vh p-0!">
    <div id="cesiumViewer" class="h100% p-0!"></div>
  </div>
</template>

<style scoped></style>
