<template>
    <div>
        <div class="direction-dashboard" v-show="showDrone">
            <div class="direction">
                <div class="keyboard-layout">
                    <div class="row">
                        <div class="key">
                            <svg t="1744966879733" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="3182" width="24" height="24">
                                <path
                                    d="M913.109333 303.232a32 32 0 1 1 0 64H347.434667c-29.44 0-53.333333 23.893333-53.333334 53.333333l0.597334 183.722667 96.981333-83.029333a32 32 0 1 1 41.642667 48.64l-149.333334 128a32 32 0 0 1-41.642666 0l-149.333334-128a32 32 0 0 1 41.642667-48.64l96.042667 82.304-0.597334-182.698667a117.418667 117.418667 0 0 1 117.333334-117.632h565.674666z"
                                    p-id="3183" fill="#cdcdcd"></path>
                            </svg>
                        </div>
                        <div class="key">
                            <svg t="1744966816621" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="2556" width="48" height="48">
                                <path
                                    d="M489.386667 340.053333a32 32 0 0 1 45.226666 0l298.666667 298.666667a32 32 0 0 1-45.226667 45.226667L512 407.936l-276.053333 276.053333a32 32 0 0 1-41.642667 3.072l-3.584-3.114666a32 32 0 0 1 0-45.226667l298.666667-298.666667z"
                                    p-id="2557" fill="#cdcdcd"></path>
                            </svg>
                        </div>
                        <div class="key">
                            <svg t="1744966898136" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="3338" width="48" height="48">
                                <path
                                    d="M679.509333 303.232a117.333333 117.333333 0 0 1 117.333334 117.333333l-0.64 182.954667 96.085333-82.261333a32 32 0 1 1 41.642667 48.64l-149.333334 128a32 32 0 0 1-41.642666 0l-149.333334-128a32 32 0 1 1 41.642667-48.64l96.938667 83.072 0.64-183.466667c0-29.781333-23.893333-53.632-53.333334-53.632H113.834667a32 32 0 1 1 0-64h565.674666z"
                                    p-id="3339" fill="#cdcdcd"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="row">
                        <div class="key"><span :class="{ active: activeKeys.Q }">Q</span>
                        </div>
                        <div class="key"><span :class="{ active: activeKeys.W }">W</span>
                        </div>
                        <div class="key"><span :class="{ active: activeKeys.E }">E</span>
                        </div>
                    </div>
                </div>
                <div class="keyboard-layout">
                    <div class="row">
                        <div class="key">
                            <svg t="1744966842109" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="2868" width="48" height="48">
                                <path
                                    d="M638.72 190.72a32 32 0 0 1 45.226667 45.226667L407.936 512l276.053333 276.053333a32 32 0 0 1 3.072 41.642667l-3.114666 3.584a32 32 0 0 1-45.226667 0l-298.666667-298.666667a32 32 0 0 1 0-45.226666l298.666667-298.666667z"
                                    fill="#cdcdcd" p-id="2869"></path>
                            </svg>
                        </div>
                        <div class="key">
                            <svg t="1744971542271" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="3494" width="48" height="48">
                                <path
                                    d="M190.72 340.053333a32 32 0 0 1 45.226667 0L512 616.064l276.053333-276.053333a32 32 0 0 1 41.642667-3.072l3.584 3.114666a32 32 0 0 1 0 45.226667l-298.666667 298.666667a32 32 0 0 1-45.226666 0l-298.666667-298.666667a32 32 0 0 1 0-45.226667z"
                                    p-id="3495" fill="#cdcdcd"></path>
                            </svg>
                            <!-- <svg t="1744966824920" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="2712" width="48" height="48"><path d="M190.72 340.053333a32 32 0 0 1 45.226667 0L512 616.064l276.053333-276.053333a32 32 0 0 1 41.642667-3.072l3.584 3.114666a32 32 0 0 1 0 45.226667l-298.666667 298.666667a32 32 0 0 1-45.226666 0l-298.666667-298.666667a32 32 0 0 1 0-45.226667z" p-id="2713"></path></svg> -->
                        </div>
                        <div class="key">
                            <svg t="1744966857122" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="3024" width="48" height="48">
                                <path
                                    d="M340.053333 190.72a32 32 0 0 1 45.226667 0l298.666667 298.666667a32 32 0 0 1 0 45.226666l-298.666667 298.666667a32 32 0 0 1-45.226667-45.226667L616.064 512l-276.053333-276.053333a32 32 0 0 1-3.072-41.642667l3.114666-3.584z"
                                    fill="#cdcdcd" p-id="3025"></path>
                            </svg>
                        </div>
                    </div>
                    <div class="row">
                        <div class="key"><span :class="{ active: activeKeys.A }">A</span>
                        </div>
                        <div class="key"><span :class="{ active: activeKeys.S }">S</span>
                        </div>
                        <div class="key"><span :class="{ active: activeKeys.D }">D</span>
                        </div>
                    </div>
                </div>
            </div>
            <div>
                <div id="dashboard" class="dashboard"></div>
            </div>
        </div>
    </div>

</template>
<script setup lang="ts">
import * as echarts from 'echarts';
import { onMounted, onUnmounted, reactive, ref } from 'vue';
import * as Cesium from 'cesium';
import droneImage from "@/assets/imgs/drone.png";  // 无人机设备图标
import createIsoscelesTriangle from './createTriangle'
const props = defineProps({
    is3D: Boolean
});
let chart: echarts.ECharts;
onMounted(() => {
    direction();
});

let startAngle = 90; // 初始时，北在上，即0度对应90度的位置（因为ECharts的0度是右侧）
let endAngle = 450; // 结束角度，确保绘制一个完整的圆
let currentValue = ref(0);
let option = {
    series: [{
        name: '方向表',
        type: 'gauge',
        detail: {
            textStyle: {
                fontSize: 14,
                fontWeight: 'bolder'
            },
            formatter: function (value: number) {
                const correctedValue = ((value % 360) + 360) % 360; // 确保0-360
                return `${correctedValue > 180 ? correctedValue - 360 : correctedValue}°`;
            }
        },
        startAngle: startAngle,
        splitNumber: 8,
        endAngle: endAngle,
        min: 0,
        max: 360,
        axisLabel: {
            fontSize: 12,
            distance: -40,
            color: '#fff',
            formatter: function (value: number) {
                const directions = ['北', '东北', '东', '东南', '南', '西南', '西', '西北'];
                const index = Math.floor((value + 22.5) / 45) % 8;
                return directions[index];
            }
        },
        axisLine: {
            lineStyle: {
                width: 15,
                color: [[1, 'rgba(0, 0, 0, 0.3)']],
                shadowColor: 'rgba(0, 0, 0, 0.5)',
                shadowBlur: 15
            }
        },
        axisTick: {
            lineStyle: {
                color: "#fff",
            }
        },
        pointer: {
            width: 4,
            itemStyle: {
                color: 'red',
                shadowColor: 'rgba(0, 0, 0, 0.3)',
                shadowBlur: 8,
                shadowOffsetX: 2,
                shadowOffsetY: 4
            }
        },
        data: [{
            value: 0, // 初始值，例如东
        }]
    }]
};
function direction() {
    if (!showDrone.value) return;
    chart = echarts.init(document.getElementById("dashboard"));
    chart.setOption(option);
    window.onresize = function () {
        chart.resize();
    };
}
const changeSeries = (type: string) => {
    const delta = type === 'add' ? 1 : -1;
    currentValue.value = ((currentValue.value + delta + 360) % 360); // 确保数值在0-359范围内
    const newStartAngle = ref((currentValue.value + 90) % 360); // 计算新的开始角度，确保它在0-360之间
    option.series[0].startAngle = newStartAngle.value;
    option.series[0].endAngle = newStartAngle.value + 360;
    option.series[0].data[0].value = currentValue.value; // 更新数据值
    createTriangle.rotateX(currentValue.value)
    chart.setOption(option); // 更新图表
}
const setSeries = (variation: number) => {
    const normalized = ((variation % 360) + 360) % 360; // 标准化到0-360
    option.series[0].data[0].value = normalized;
    chart.setOption(option, true);
}
const activeKeys: { [key: string]: boolean } = reactive({
    Q: false,
    W: false,
    E: false,
    A: false,
    S: false,
    D: false
})
let showDrone = ref<boolean>(false);
// 挂载监听事件
const addEvent = (viewer: Cesium.Viewer) => {
    // window.addEventListener('keydown', handleKeyPress); // 监听键盘按下事件
    window.addEventListener('keydown', (event: KeyboardEvent) => handleKeyPress(event, viewer));
    window.addEventListener('keyup', handleKeyUp); // 监听键盘释放事件
    window.addEventListener('mousedown', (event: MouseEvent) => handleMouseDown(event, viewer)); // 监听鼠标按下事件
    window.addEventListener('mouseup', handleMouseUp); // 新增全局释放监听
}
// 键盘按下事件
const handleKeyPress = (e: KeyboardEvent, viewer: Cesium.Viewer) => {
    if (!createTriangle) createTriangle = new createIsoscelesTriangle(viewer);
    const key = e.key.toUpperCase();
    if (key === 'Q') {
        changeSeries('delete');
    } else if (key === 'E') {
        changeSeries('add');
    }
    if (activeKeys.hasOwnProperty(key)) {
        activeKeys[key] = true
    }
}
let timer: string | number | NodeJS.Timeout | undefined;
// 键盘释放事件
const handleKeyUp = (e: KeyboardEvent) => {
    const key = e.key.toUpperCase();
    if (activeKeys.hasOwnProperty(key)) {
        timer = setTimeout(() => {
            activeKeys[key] = false;
        }, 100);
    }
}
// 定义间隔器和方向映射
let intervalId: ReturnType<typeof setTimeout> | null = null;
const directionActions: { [key: string]: (viewer: Cesium.Viewer) => void } = {
    W: (viewer) => moveDrone(viewer, 'forward'),
    S: (viewer) => moveDrone(viewer, 'backward'),
    A: (viewer) => moveDrone(viewer, 'left'),
    D: (viewer) => moveDrone(viewer, 'right')
};

// 鼠标按下事件
const handleMouseDown = (e: MouseEvent, viewer: Cesium.Viewer) => {
    if (!createTriangle) createTriangle = new createIsoscelesTriangle(viewer);
    const target = e.target as HTMLElement;
    const upperKey: string = target.innerText;

    if (!activeKeys.hasOwnProperty(upperKey) || !viewer) return;

    // 立即执行首次操作
    if (['Q', 'E'].includes(upperKey)) {
        changeSeries(upperKey === 'Q' ? 'delete' : 'add');

    } else if (directionActions[upperKey]) {
        directionActions[upperKey](viewer);
    }

    // 设置间隔器连续触发
    intervalId = setInterval(() => {
        if (['Q', 'E'].includes(upperKey)) {
            changeSeries(upperKey === 'Q' ? 'delete' : 'add');
        } else if (directionActions[upperKey]) {
            directionActions[upperKey](viewer);
        }
    }, 100); // 每100ms触发一次

    activeKeys[upperKey] = true;
}

// 鼠标释放事件
const handleMouseUp = () => {
    if (intervalId) {
        clearInterval(intervalId);
        intervalId = null;
    }
    Object.keys(activeKeys).forEach(key => activeKeys[key] = false);
}
let createTriangle: createIsoscelesTriangle;
const markerEntities: Cesium.Entity[] = [];
// 创建无人机节点
let nowPosition: Cesium.Cartesian3;
const createDrone = (position: Cesium.Cartesian3, viewer: Cesium.Viewer) => {
    if (!createTriangle) createTriangle = new createIsoscelesTriangle(viewer);
    nowPosition = position;
    if (!viewer) return;
    const entity = viewer.entities.add({
        position: nowPosition,
        orientation: new Cesium.CallbackProperty(() => {
            return Cesium.Transforms.headingPitchRollQuaternion(
                nowPosition,
                new Cesium.HeadingPitchRoll(Cesium.Math.toRadians(externalRotation), 0, 0)
            );
        }, false), // 动态更新方向
        billboard: {
            image: droneImage,
            width: 36,
            height: 36,
            rotation: new Cesium.CallbackProperty(() => {
                return Cesium.Math.toRadians(externalRotation);
            }, false),
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            // eyeOffset: new Cesium.Cartesian3(0, 0, -10), // 使广告牌稍微远离摄像机
        }
    });
    markerEntities.push(entity); // 存储引用
    showDrone.value = true;
    direction();
    createTriangle.create(position, 200, 30, 30);
    // createIsoscelesTriangle(viewer, position, 1000, 60)
}
// 移除无人机节点

const removeDrone = (viewer: Cesium.Viewer) => {
    if (!viewer) return;
    viewer.entities.remove(markerEntities[0]);
    markerEntities.length = 0;
    showDrone.value = false;
    createTriangle.clearAllEntities();
}
let externalRotation = 0;
const rotateDrone = (type: string, degrees: number, viewer: Cesium.Viewer) => {
    if (!viewer) return;
    if (type === 'add') {
        externalRotation = externalRotation - degrees % 360; // 更新角度值
        viewer.scene.requestRender();
    } else {
        externalRotation = externalRotation + degrees % 360; // 更新角度值
        viewer.scene.requestRender();
    }
}
// 新增移动参数配置
const MOVEMENT = {
    speed: 15,  // 基础移动速度（米/秒）
    rotateSpeed: 1.5,  // 旋转速度（度/次）
    heightFixed: true   // 保持高度不变
};
const { is3D } = props;
const moveDrone = (viewer: Cesium.Viewer, direction: string) => {
    console.log("is3D: ", is3D)
    let newPosition: Cesium.Cartesian3;
    if (direction === 'forward') {
        if (!is3D){
            viewer.camera.moveUp(MOVEMENT.speed);
        }else{
            moveCameraNorthIn3D(viewer);
        }
        newPosition = moveForward(nowPosition, MOVEMENT.speed, markerEntities[0], 'forward');
        nowPosition = newPosition;
        
    } else if (direction === 'backward') {
        // viewer.camera.moveDown(MOVEMENT.speed)
        if (!is3D) viewer.camera.moveDown(MOVEMENT.speed);
        newPosition = moveForward(nowPosition, MOVEMENT.speed, markerEntities[0], 'backward');
        nowPosition = newPosition;
    } else if (direction === 'left') {
        // viewer.camera.moveLeft(MOVEMENT.speed);
        if (!is3D) viewer.camera.moveLeft(MOVEMENT.speed);
        newPosition = moveForward(nowPosition, MOVEMENT.speed, markerEntities[0], 'left');
        nowPosition = newPosition;
    } else if (direction === 'right') {
        // viewer.camera.moveRight(MOVEMENT.speed);
        if (!is3D) viewer.camera.moveRight(MOVEMENT.speed);
        newPosition = moveForward(nowPosition, MOVEMENT.speed, markerEntities[0], 'right');
        nowPosition = newPosition;
    } else {
        newPosition = nowPosition;
    }
    const fixedPosition = Cesium.Cartesian3.fromRadians(
        Cesium.Cartographic.fromCartesian(newPosition).longitude,
        Cesium.Cartographic.fromCartesian(newPosition).latitude,
        Cesium.Cartographic.fromCartesian(newPosition).height,
    );
    createTriangle.create(fixedPosition, 200, 30, 30)
    // 更新点的位置
    markerEntities[0].position = new Cesium.ConstantPositionProperty(fixedPosition);
}
function moveForward(
    currentPosition: Cesium.Cartesian3,
    speed: number,
    entity: Cesium.Entity,
    direction: 'forward' | 'backward' | 'left' | 'right'
): Cesium.Cartesian3 {
    const orientation = entity.orientation?.getValue(Cesium.JulianDate.now());
    if (!orientation) return currentPosition;

    // 从四元数中提取方向矩阵
    const matrix3 = Cesium.Matrix3.fromQuaternion(orientation);
    let directionVector: Cesium.Cartesian3;
    switch (direction) {
        case 'right':
            // X轴正方向（实体面朝方向）
            directionVector = Cesium.Matrix3.getColumn(matrix3, 0, new Cesium.Cartesian3());
            break;
        case 'left':
            // X轴负方向（实体背面方向）
            directionVector = Cesium.Matrix3.getColumn(matrix3, 0, new Cesium.Cartesian3());
            Cesium.Cartesian3.negate(directionVector, directionVector);
            break;
        case 'backward':
            // Y轴负方向（实体左侧方向）
            directionVector = Cesium.Matrix3.getColumn(matrix3, 1, new Cesium.Cartesian3());
            Cesium.Cartesian3.negate(directionVector, directionVector);
            break;
        case 'forward':
            // Y轴正方向（实体右侧方向）
            directionVector = Cesium.Matrix3.getColumn(matrix3, 1, new Cesium.Cartesian3());
            break;
        default:
            return currentPosition;
    }
    // 计算位移并返回新坐标
    const displacement = Cesium.Cartesian3.multiplyByScalar(
        directionVector,
        speed,
        new Cesium.Cartesian3()
    );
    return Cesium.Cartesian3.add(currentPosition, displacement, new Cesium.Cartesian3());
}
const moveCameraNorthIn3D = (viewer: Cesium.Viewer) => {
    const centerPixel = new Cesium.Cartesian2(
        viewer.canvas.clientWidth / 2,
        viewer.canvas.clientHeight / 2
    );
    const worldPosition = viewer.scene.camera.pickEllipsoid(centerPixel);
    if (worldPosition) {
        const cartographic = Cesium.Ellipsoid.WGS84.cartesianToCartographic(worldPosition);
        const centerLng = Cesium.Math.toDegrees(cartographic.longitude);
        const centerLat = Cesium.Math.toDegrees(cartographic.latitude);
        const surfaceHeight = Cesium.Ellipsoid.WGS84.cartesianToCartographic(worldPosition).height;
        const adjustedAlt = Math.max(surfaceHeight * 1.5, 1500);
        viewer.camera.setView({
            destination: Cesium.Cartesian3.fromDegrees(centerLng, centerLat - 0.03, adjustedAlt),
            orientation: {
                heading: 0, // 朝向角度（以北为基准，单位：弧度）
                pitch: Cesium.Math.toRadians(-15), // 俯仰角度（-90 表示垂直向下）
                roll: 0 // 滚转角度
            }
        });
    }

};
defineExpose({
    changeSeries,
    setSeries,
    addEvent,
    createDrone,
    rotateDrone,
    removeDrone,
    moveDrone,
});
onUnmounted(() => {
    clearTimeout(timer);
});
</script>

<style scoped lang="scss">
.direction-dashboard {
    display: flex;
    gap: 20px;
    align-items: center;
}

.dashboard {
    width: 200px;
    height: 180px;
}

.direction {
    width: 200px;
    height: 140px;
}

.keyboard-layout {
    background: rgba(0, 0, 0, 0.5);
    padding: 6px 10px;
    margin: 10px;
    display: inline-flex;
    flex-direction: column;
    gap: 6px;
    border-radius: 12px;
}

.row {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.key {
    border-radius: 8px;
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: 100;
    color: #fff;

    span {
        width: 22px;
        height: 22px;
        line-height: 22px;
        text-align: center;
        padding: 0 6px;
        border-radius: 5px;
        background: rgba(0, 0, 0, 0.6);
        cursor: pointer;

        :hover {
            // background: rgba(0, 0, 0, 0.1);
            background: rgba(236, 228, 228, 0.1);
        }
    }

    .active {
        background: #2b84e4;
    }
}

/* 单独调整下方E键的尺寸 */
.row:last-child .key {
    width: 16px;
}

/* SVG样式 */
.key svg {
    color: #cdcdcd;
    width: 80%;
    height: 80%;
}
</style>
