<template>
    <div :id="chartId" class="chart-container"></div>
  </template>
  
  <script lang="ts" setup>
  import { onMounted, defineProps } from 'vue';
  import * as echarts from 'echarts';
  
  const props = defineProps<{
    chartId: string;
  }>();
  
  onMounted(() => {
    const barChart = echarts.init(document.getElementById(props.chartId) as HTMLElement);
    const barOption = {
      xAxis: {
        type: 'category',
        data: ['A', 'B', 'C', 'D', 'E'],
        axisLabel: {
          color: '#e6f1ff'
        }
      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: '#e6f1ff'
        }
      },
      series: [
        {
          data: [120, 200, 150, 80, 70],
          type: 'bar',
          itemStyle: {
            color: '#006edd'
          }
        }
      ]
    };
    barChart.setOption(barOption);
  });
  </script>
  
  <style scoped>
  .chart-container {
    width: 100%;
    height: 300px;
  }
  </style>    