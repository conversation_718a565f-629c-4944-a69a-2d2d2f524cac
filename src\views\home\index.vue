<script setup lang="ts" name="home">
import { computed } from 'vue';
import { useAppStore } from '@/store/modules/app';
import HeaderBanner from './modules/header-banner.vue';
import CardData from './modules/card-data.vue';
import LineChart from './modules/line-chart.vue';
import Pie<PERSON>hart from './modules/pie-chart.vue';
import ProjectNews from './modules/project-news.vue';
import CreativityBanner from './modules/creativity-banner.vue';
import OrganizationDetail from './modules/organization-detail.vue';
import DataOverview from './modules/data-overview.vue';

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));
</script>

<template>
  <NSpace vertical :size="16">
    <HeaderBanner />
    <OrganizationDetail />
    <DataOverview />
    <!-- <CardData /> -->
    <!-- <CreativityBanner /> -->
    <!-- <ProjectNews /> -->
    <!-- <NAlert :title="$t('common.warning')" type="warning">
      {{ $t('page.home.branchDesc') }}
    </NAlert> -->
    <!-- <NGrid :x-gap="gap" :y-gap="16" responsive="screen" item-responsive> -->
    <!-- </NGrid> -->
  </NSpace>
</template>

<style scoped></style>
