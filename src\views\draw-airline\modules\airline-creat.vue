<template>
  <div class="absolute zindex-1 bg-dark-8 right-330px top-0 h-100%  text-light">
    <n-flex align="center" justify="space-between" class="bg-dark-1 text-18px px-15px py-15px">
      <n-text class="text-light">单个航点设置</n-text>
      <n-icon @click="() => { emits('handel-creat-modal') }" :component="CloseOutline" size="22"
        class="cursor-pointer" />
    </n-flex>

    <!-- 单点设置 -->
    <n-config-provider :theme="darkTheme">
      <n-card embedded :bordered="false" class="text-light rd-0 ">
        <n-flex align="center" class="pb-3">
          <n-text>选择航点</n-text>
          <n-select @update:value="handlePointSelect" :value="currentPointIndex + 1" :options="waypointOptions"
            class="w-250px" />
        </n-flex>
        <div>
          <n-text>航点设置</n-text>
          <div class="p-2 bg-dark-9 mt-5px ">
            <n-flex align="center">
              <n-text>经度：</n-text>
              <n-input-number :disabled="true" :show-button="false" class="flex-1"
                :value="waypointLatLngArray[currentPointIndex]?.longitude || 0" />
            </n-flex>
            <n-flex align="center" class="mt-10px">
              <n-text>纬度：</n-text>
              <n-input-number :disabled="true" :show-button="false" class="flex-1"
                :value="waypointLatLngArray[currentPointIndex]?.latitude || 0" />
            </n-flex>
            <n-flex align="center" class="mt-10px">
              <n-text>航点高度：</n-text>
                <n-input-number v-model:value="localHeights[currentPointIndex]" clearable size="small"
                @update:value="getPointHeight(Math.round(localHeights[currentPointIndex]))" :show-button="false"
                class="bg-dark-3 w-72.5%" :min="10" :max="500" />
            </n-flex>

          </div>
        </div>
        <n-flex justify="between" align="center" class="mt-2 pb-2">
          <n-text class="flex-1">航点动作</n-text>
          <n-dropdown :options="actionOptions" @select="handlePointActionSelect" placement="bottom-start"
            trigger="hover" :render-label="renderDropdownLabel" size="small">
            <n-button type="info">+ 添加</n-button>
          </n-dropdown>
        </n-flex>
        <n-scrollbar v-if="wayActionArr.values.length" style="height: 60vh">
          <div v-for="(item, index) in wayActionArr.values[currentPointIndex]" :key="index">
            <div class="bg-dark-9 mb-10px">
              <n-flex class="border-b border-dark-2 p-10px" justify="between">
                <div class="flex-1">
                  <n-text class="rd-50% bg-dark-1 inline-block text-center w-25px h-25px mr-10px">
                    {{ index + 1 }}
                  </n-text>
                  <n-text v-if="item.actionActuatorFunc === 'hover'">悬停等待</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'gimbalRotate2'">旋转云台偏航角</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'gimbalRotate1'">旋转云台俯仰角</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'rotateYaw'">飞行器偏航</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'takePhoto'">拍照</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'panoShot'">全景照片</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'startRecord'">开始录像</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'stopRecord'">结束录像</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'zoom' && item.params.focalLength != 0.5">变焦</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'zoom' && item.params.focalLength == 0.5">广角</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'startIr'">开始测温</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'stopIr'">结束测温</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'temperatureMetering'">温度检测</n-text>
                  <n-text v-if="item.actionActuatorFunc === 'irMeteringPhoto'">测温拍照</n-text>
                </div>
                <n-icon @click="delWayActionArrItemByIndex(index)" :component="CloseCircleOutline" size="22"
                  class="cursor-pointer" />
              </n-flex>

              <!-- 悬停等待 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'hover'">
                <div class="p-10px">
                  <n-text class="flex-1">等待时长(s)</n-text>
                  <n-input-number min="1" max="900" v-model:value="item.params.hoverTime" class="pt-10px" />
                </div>
              </div>

              <!-- 旋转云台偏航角 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'gimbalRotate2'">
                <div class="p-10px">
                  <n-text class="flex-1">偏航角度（绝对角度）（°）</n-text>
                  <n-input-number min="-45" max="45" v-model:value="item.params.gimbalYawRotateAngle" class="pt-10px" />
                </div>
                <div class="p-10px">
                  <n-text class="flex-1">云台完成转动用时(s)</n-text>
                  <n-input-number min="0" max="10" v-model:value="item.params.gimbalRotateTime" class="pt-10px" />
                </div>
              </div>

              <!-- 旋转云台俯仰角 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'gimbalRotate1'">
                <div class="p-10px">
                  <n-text class="flex-1">俯仰角度（绝对角度）（°）</n-text>
                  <n-input-number min="-90" max="0" v-model:value="item.params.gimbalPitchRotateAngle"
                    class="pt-10px" />
                </div>
                <div class="p-10px">
                  <n-text class="flex-1">云台完成转动用时(s)</n-text>
                  <n-input-number min="0" max="10" v-model:value="item.params.gimbalRotateTime" class="pt-10px" />
                </div>
              </div>

              <!-- 飞行器偏航 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'rotateYaw'">
                <div class="p-10px">
                  <n-text class="flex-1">偏航目标角度（相对地理北）（°）</n-text>
                  <n-input-number v-model:value="item.params.aircraftHeading" class="pt-10px" />
                </div>
              </div>

              <!-- 拍照 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'takePhoto'">
                <!-- <div class="p-10px"> -->
                <!-- <n-checkbox-group v-model:value="item.params.payloadLensIndex">
                    <n-space item-style="display: flex;">
                      <n-checkbox value="zoom" label="变焦" />
                      <n-checkbox value="wide" label="广角" />
                    </n-space>
                  </n-checkbox-group> -->
                <!-- </div> -->
              </div>

              <!-- 全景照片 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'panoShot'">
                <!-- <div class="p-10px">
                  <n-text class="flex-1">全景照片选项</n-text> -->
                <!-- 这里可以添加其他全景照片相关的内容 -->
                <!-- </div> -->
              </div>

              <!-- 开始录像 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'startRecord'">
                <!-- <div class="p-10px"> -->
                <!-- <n-checkbox-group v-model:value="item.params.payloadLensIndex">
                    <n-space item-style="display: flex;">
                      <n-checkbox value="zoom" label="变焦" />
                      <n-checkbox value="wide" label="广角" />
                    </n-space>
                  </n-checkbox-group> -->
                <!-- </div> -->
              </div>

              <!-- 结束录像 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'stopRecord'">
                <div class="p-10px">
                  <n-text class="flex-1">录像结束</n-text>
                  <!-- 可以添加其他相关内容 -->
                </div>
              </div>

              <!-- 变焦 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'zoom' && item.params.focalLength != 0.5">
                <div class="p-10px">
                  <n-text class="flex-1">变焦倍数</n-text>
                  <n-input-number max="50" min="2" v-model:value="item.params.focalLength" class="pt-10px" />
                </div>
              </div>
              <!-- 广角 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'zoom' && item.params.focalLength == 0.5">

              </div>

              <!-- 开始测温 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'startIr'">

              </div>

              <!-- 结束测温 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'stopIr'">

              </div>

              <!-- 温度检测 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'temperatureMetering'">
                <div class="p-10px">
                  <n-text class="flex-1">最高告警</n-text>
                  <n-input-number max="10000" min="0" v-model:value="item.params.max" class="pt-10px" />
                </div>
                <div class="p-10px">
                  <n-text class="flex-1">最低告警</n-text>
                  <n-input-number max="10000" min="-1000" v-model:value="item.params.min" class="pt-10px" />
                </div>
                <div class="p-10px">
                  <n-text class="flex-1">温度监测描述</n-text>
                  <n-input v-model:value="item.params.tips" class="pt-10px" />
                </div>
              </div>

              <!-- 测温拍照 相关内容 -->
              <div v-if="item.actionActuatorFunc === 'irMeteringPhoto'">
                <div class="p-10px">
                  <n-text class="flex-1">测温描述</n-text>
                  <n-input v-model:value="item.params.tips" class="pt-10px" />
                </div>
              </div>

            </div>
          </div>
        </n-scrollbar>
      </n-card>
    </n-config-provider>
  </div>
</template>

<script setup lang="ts">
import { PropType, VNodeChild, computed, h, onMounted, onUpdated, reactive, ref, watch } from 'vue';
import { darkTheme, useMessage } from 'naive-ui';
import type { DropdownOption, SelectOption } from 'naive-ui';
import { CloseCircleOutline, CloseOutline } from '@vicons/ionicons5'
import { useAirportStore } from '@/store/modules/airport';
import { useRoute } from 'vue-router';

const airportStore = useAirportStore();
const route = useRoute();

defineOptions({ name: 'AirLineCreate' });

// 定义组件接受的属性
const props = defineProps<{
  currentWaypointIndex: number; // 当前修改的航点
  waypointLatLngArray: Api.AirLine.WayPointList[];
  airPointActionArr: any[];
  heightArrParams: number[];
  // updateVerticalLineHeight: (index: number, inputHeight: number) => void;
}>();
const localHeights = ref([...props.heightArrParams].map(height => Math.round(height))); // 本地副本
const emits = defineEmits(['focus-airport', 'handel-creat-modal', 'update:wayActions', 'getHeight']);
const message = useMessage();
// 当前修改航点的下标
const currentPointIndex = ref<number>(props.currentWaypointIndex);
// 监听父组件数据变化
watch(() => props.heightArrParams, (newVal) => {
  localHeights.value = [...newVal.map(height => Math.round(height))]
}, { deep: true })
// 航点列表
const waypointOptions = computed(() => {
  adjustWayActionArr(props.waypointLatLngArray.length);
  return Array.from({ length: props.waypointLatLngArray.length }, (_, i) => {
    return ({ value: i + 1, label: `${i + 1}` })
  })
});

const heightArr = computed({
  get: () => props.heightArrParams,
  set: (value) => {
  }
})

// 高度变化时通知父组件
const getPointHeight = (e: number) => {
  const newHeights = [...localHeights.value]
  newHeights[currentPointIndex.value] = e
  emits('getHeight', newHeights) // 触发父组件更新
  // props.updateVerticalLineHeight(currentPointIndex.value, e)
  //
}
// 航点动作选项
const actionOptions = [
  {
    type: 'group', label: '飞行器动作', key: 'aircraft', children: [
      { label: '悬停等待', key: 'hover' },
      { label: '飞行器偏航', key: 'rotateYaw' }
    ]
  },
  {
    type: 'group', label: '云台动作', key: 'yuntab', children: [
      { label: '旋转云台俯仰角', key: 'gimbalRotate1' },
      { label: '旋转云台偏航角', key: 'gimbalRotate2' },
    ]
  },
  {
    type: 'group', label: '相机动作', key: 'camera', children: [
      { label: '拍照', key: 'takePhoto' },
      { label: '全景照片', key: 'panoShot' },
      { label: '变焦', key: 'zoom' },
      {
        label: '广角', key: 'wide'
      },
      { label: '开始录像', key: 'startRecord' },
      { label: '结束录像', key: 'stopRecord' },

    ]
  },
  {
    type: 'group', label: '测温功能', key: 'irMetering', children: [
      { label: '开始测温', key: 'startIr' },
      { label: '结束测温', key: 'stopIr' },
      { label: '温度检测', key: 'temperatureMetering' },
      { label: '测温拍照', key: 'irMeteringPhoto' },
    ]
  }
]
// 航点动作下拉框渲染
const renderDropdownLabel = (option: DropdownOption) => {
  if (option.type === 'group') {
    return option.label as VNodeChild
  }
  return h(
    'span', {},
    {
      default: () => option.label as VNodeChild
    }
  )
}
interface WayActionArr {
  values: Api.AirLine.AirPointActionItem[][];
}
// 所有航点动作数组
const wayActionArr = reactive<WayActionArr>({ values: [[]] });


// 航点动作下拉框选择事件
function handlePointActionSelect(key: string) {
  // 确保当前索引的数组已初始化
  if (!wayActionArr.values[currentPointIndex.value]) {
    wayActionArr.values[currentPointIndex.value] = [];
  }

  switch (key) {
    case 'hover':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "hover", "params": { "hoverTime": 10.0 } })
      break;
    case 'rotateYaw':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "rotateYaw", "params": { "aircraftHeading": -45.0 } })
      break;
    case 'gimbalRotate1':
      wayActionArr.values[currentPointIndex.value].push({
        "actionActuatorFunc": "gimbalRotate1", "params": {
          "gimbalPitchRotateEnable": 1, // 俯仰角
          "gimbalPitchRotateAngle": -30.0,
          "gimbalRotateTime": 0.0 // 时间
        }
      })
      break;
    case 'gimbalRotate2':
      wayActionArr.values[currentPointIndex.value].push({
        "actionActuatorFunc": "gimbalRotate2", "params": {
          "gimbalYawRotateEnable": 1, // 偏航角
          "gimbalYawRotateAngle": -30.0, // 偏航角度
          "gimbalRotateTime": 0.0 // 时间
        }
      })
      break;
    case 'takePhoto':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "takePhoto", "params": { "payloadLensIndex": ["zoom", "wide"] } })
      break;
    case 'panoShot':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "panoShot", "params": {} })
      break;
    case 'zoom':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "zoom", "params": { "focalLength": 5.0 } })
      break;
    case 'wide':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "zoom", "params": { "focalLength": 0.5 } })
      break;
    case 'startRecord':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "startRecord", "params": { "payloadLensIndex": ["zoom", "wide"] } })
      break;
    case 'startIr':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "startIr", "params": { "mode": 2} })
      break;
    case 'stopIr':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "stopIr", "params": { "mode": 0} })
      break;
    case 'temperatureMetering':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "temperatureMetering", "params": {"max": 100.00, "min": 20.00, "tips": `航点：${  currentPointIndex.value + 1}`} })
      break;
    case 'irMeteringPhoto':
      wayActionArr.values[currentPointIndex.value].push({ "actionActuatorFunc": "irMeteringPhoto", "params": {"tips": `航点：${  currentPointIndex.value + 1}`} })
      break;
    default:
      console.log('handlePointActionSelect err');
      break;
  }

  // 创建新的数组副本再发送更新
  const updatedActions = wayActionArr.values.map(arr => [...arr]);
  emits('update:wayActions', updatedActions);
}

// 删除某项航点动作
function delWayActionArrItemByIndex(index: number) {
  const currentActions = wayActionArr.values[currentPointIndex.value];

  // 创建新的数组副本再发送更新
  const updatedActions = wayActionArr.values.map(arr => [...arr]);
  emits('update:wayActions', updatedActions);
}

// 航点切换
function handlePointSelect(value: number, option: SelectOption) {
  if (value !== undefined) {
    currentPointIndex.value = value - 1; // 更新 currentPointIndex.value
  }
}

// 处理 wayActionArr 数组
function adjustWayActionArr(n: number) {
  const lengthDifference = n - wayActionArr.values.length;

  if (lengthDifference > 0) {
    // 只在初始化时添加空数组，避免覆盖已有的数据
    if (wayActionArr.values.length === 0) {
      wayActionArr.values = Array(n).fill(null).map(() => []);
    } else {
      // 如果已经有数据，只在末尾添加新的空数组
      wayActionArr.values.push(...Array(lengthDifference).fill(null).map(() => []));
    }
  } else if (lengthDifference < 0) {
    wayActionArr.values.splice(n);
  }
}

onMounted(() => {
  // console.log(props.airportFlyInfo[0])
  // wayActionArr.push(...Array(currentPointIndex).fill([]));

  // console.log("onMounted ~ props.airPointActionArr.length && route.query.id:", props.airPointActionArr.length && route.query.id)
})

onUpdated(() => {
  console.log(' onUpdated ~ props.airPointActionArr:', props.airPointActionArr)
  currentPointIndex.value = props.currentWaypointIndex;
  // 只在编辑模式下时更新数据 ( TODO: 且 wayActionArr 未初始化? )
  // console.log(' onUpdated ~ :', props.airPointActionArr.length && route.query.id && !wayActionArr.values.some(arr => arr.length > 0))
  // console.log(' onUpdated ~ props.airPointActionArr.length:', props.airPointActionArr.length)
  // console.log(' onUpdated ~ !wayActionArr.values.some(arr => arr.length > 0):', !wayActionArr.values.some(arr => arr.length > 0))
  // console.log(' onUpdated ~ route.query.id:', route.query.id)
  if (props.airPointActionArr.length && route.query.id) {
    wayActionArr.values = JSON.parse(JSON.stringify(props.airPointActionArr));
    emits('update:wayActions', wayActionArr.values);
  }
})

defineExpose({ wayActionArr });

function useStore() {
  throw new Error('Function not implemented.');
}
</script>

<style scoped>
.n-card.n-card--embedded {
  background-color: rgb(24 24 24 / var(--un-bg-opacity));
}
</style>
