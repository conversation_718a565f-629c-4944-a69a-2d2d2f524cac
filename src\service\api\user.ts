import { request } from '../request';

/**
 * 用户列表
 */
export function fetchUserList(params: Api.List.Table) {
  return request({
    url: '/system/user/list',
    method: 'get',
    params
  });
}

/**
 * 新增用户
 */
export function addUser(data: Api.User.UserItem) {
  return request({
    url: '/system/user',
    method: 'post',
    data
  });
}

/**
 * 编辑用户
 */
export function editUser(data: Api.User.UserItem) {
  return request({
    url: '/system/user',
    method: 'put',
    data
  });
}

/**
 * 用户管理部门树
 */
export function deptTree(params: Api.List.Table) {
  return request({
    url: '/system/user/deptTree',
    method: 'get',
    params
  });
}

/**
 * 重置密码
 */
export function resetPwd(data: Api.User.UserItem) {
  return request({
    url: '/system/user/resetPwd',
    method: 'put',
    data
  });
}

/**
 * 删除用户
 */
export function delUser(userId: number) {
  return request({
    url: `/system/user/${userId}`,
    method: 'delete'
  });
}

/**
 * 修改用户状态
 */
export function changeUserStatus(data: Api.User.UserItem) {
  return request({
    url: '/system/user/changeStatus',
    method: 'put',
    data
  });
}
