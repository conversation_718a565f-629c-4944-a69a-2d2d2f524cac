<template>
    <div
        class="fixed zindex-2 flex items-center justify-between relative bg-transparent w-full h-50px p-15px shadow-[0_10px_25px_-5px_rgba(0,0,0,0.5)] ml--1">
        <!-- 背景图片容器 -->
        <div class="bg-image absolute top-0 left-0 w-full h-full z-[-1]"></div>
        <div class="bg-images absolute top-0 left-0 w-full h-full z-[1]"></div>
        <!-- 新增时间显示区域 -->
        <div class="flex items-center pl-10% top-3 absolute left-0 mt-14  z-[2]">
            <img src="@/assets/imgs/little3.png" class="absolute h-50% w-0.5% left-20%" />
            <img src="@/assets/imgs/little3.png" class="absolute h-50% w-0.5% right-9.5%" />
            <div class="weather-container relative h-20px w-100 flex items-center">
                <img src="@/assets/imgs/little2.png" class="absolute h-full w-full ml--15" />
                <span class="text-white text-4 tracking-1">{{ currentTime }}</span>
            </div>
        </div>
        <!--左侧背景 -->
        <img src="@/assets/imgs/home_left.png" alt="Title Image" class="h-100vh w-full z-[2] ml-1" />
        <!--右侧背景 -->
        <img src="@/assets/imgs/home_left.png" alt="Title Image"
            class=" absolute h-full w-full z-[2] scale-x-[-1] ml--3.5" />
        <!-- 标题 -->
        <div class="absolute top-0 left-1/2 transform -translate-x-1/2 inline-block text-center h-65px w-full"
            style="z-index: 1;">
            <div class="flex items-center justify-center h-full">
                <!-- 标题logo -->
                <img :src="authStore.sysInfo.webConfig.webLogoUrl"
                    class="h-95% object-contain mr-5 mt-5 rounded-full" />
                <span class="text-white text-8 pt-8 tracking-1" style="font-family: 'DouyuFont', sans-serif;">{{
                    authStore.sysInfo.webConfig.webName }}</span>
            </div>
        </div>
        <!-- 天气组件 -->
        <div class="absolute top-3 right-8% flex items-center z-[2] mt-14">
            <img src="@/assets/imgs/little3.png" class="absolute h-50% w-1% left--2%" />
            <img src="@/assets/imgs/little3.png" class="absolute h-50% w-0.8% right--2%" />
            <div class="weather-container relative h-20px w-80 flex items-center">
                <img src="@/assets/imgs/little2.png" class="absolute h-full w-full" />
                <div class="flex items-center px-10 z-[1]">
                    <span class="text-white text-4 tracking-2 ml-5" style="font-family: 'DouyuFont', sans-serif;">
                        <span>晴</span>
                        <span class="mx-1">|</span>
                        <span>25°C</span>
                        <span class="mx-1">|</span>
                        <span>东南风</span>
                    </span>
                </div>
            </div>
        </div>

        <!-- 上部分 -->
        <div class="w-95% absolute left-3% top-13%">
            <div class="grid grid-cols-3 grid-rows-2 gap-4">
                <!-- 模块1：成员数量 -->
                <div class="module-container">
                    <div class="module-content">
                        <div class="module-icon">
                            <img src="@/assets/imgs/little1.png" class="h-10 w-1 mr-5" />
                            <img src="@/assets/imgs/icon3.png" class="h-10 w-10" />
                        </div>
                        <div class="module-text mt-2">
                            <div>
                                <span class="text-sm mr-8">成员数量</span>
                                <span class="text-lg font-bold color-yellow">{{ indexOrg.userCount }}</span>
                                <span class="text-sm font-bold color-yellow">人</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模块2：设备数量 -->
                <div class="module-container">
                    <div class="module-content">
                        <div class="module-icon">
                            <img src="@/assets/imgs/little1.png" class="h-10 w-1 mr-5" />
                            <img src="@/assets/imgs/icon5.png" class="h-10 w-10" />
                        </div>
                        <div class="module-text mt-2">
                            <div>
                                <span class="text-sm mr-8">设备数量</span>
                                <span class="text-lg font-bold color-yellow">{{ indexOrg.dockCount }}</span>
                                <span class="text-sm font-bold color-yellow">个</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模块3：飞行次数 -->
                <div class="module-container">
                    <div class="module-content">
                        <div class="module-icon">
                            <img src="@/assets/imgs/little1.png" class="h-10 w-1 mr-5" />
                            <img src="@/assets/imgs/icon1.png" class="h-10 w-10" />
                        </div>
                        <div class="module-text mt-2">
                            <div>
                                <span class="text-sm mr-8">飞行次数</span>
                                <span class="text-lg font-bold color-yellow">{{ indexOrg.flightCount }}</span>
                                <span class="text-sm font-bold color-yellow">次</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模块4：航线数量 -->
                <div class="module-container">
                    <div class="module-content">
                        <div class="module-icon">
                            <img src="@/assets/imgs/little1.png" class="h-10 w-1 mr-5" />
                            <img src="@/assets/imgs/icon1.png" class="h-10 w-10" />
                        </div>
                        <div class="module-text mt-2">
                            <div>
                                <span class="text-sm mr-8">航线数量</span>
                                <span class="text-lg font-bold color-yellow">{{ indexOrg.flightLineCount }}</span>
                                <span class="text-sm font-bold color-yellow">条</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模块5：AI能力 -->
                <div class="module-container">
                    <div class="module-content">
                        <div class="module-icon">
                            <img src="@/assets/imgs/little1.png" class="h-10 w-1 mr-5" />
                            <img src="@/assets/imgs/icon2.png" class="h-10 w-10" />
                        </div>
                        <div class="module-text mt-2">
                            <div>
                                <span class="text-sm mr-8">AI能力</span>
                                <span class="text-lg font-bold color-yellow">{{ indexOrg.aiCount }}</span>
                                <span class="text-sm font-bold color-yellow">种</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 模块6：在线设备 -->
                <div class="module-container">
                    <div class="module-content">
                        <div class="module-icon">
                            <img src="@/assets/imgs/little1.png" class="h-10 w-1 mr-5" />
                            <img src="@/assets/imgs/icon5.png" class="h-10 w-10" />
                        </div>
                        <div class="module-text mt-2">
                            <div>
                                <span class="text-sm mr-8">在线设备</span>
                                <span class="text-lg font-bold color-yellow">{{ indexOrg.onlineDevice }}</span>
                                <span class="text-sm font-bold color-yellow">个</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 中间部分 - 两行四列布局 -->
        <div class="w-95% absolute left-3% top-30.5%">
            <!-- 第一行 - 标题行 -->
            <div class="grid grid-cols-4 gap-4 mb-4">
                <!-- 空域标题 -->
                <div class="relative h-10 flex items-center justify-center ">
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.8% left-0 ml-10 z-2" />
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.8% right-0 mr-10 z-2" />
                    <img src="@/assets/imgs/little2.png" class="absolute h-full w-full" />
                    <span class="text-white text-5 tracking-1 z-[1]"
                        style="font-family: 'DouyuFont', sans-serif;">空域</span>
                </div>

                <!-- 航线标题 -->
                <div class="relative h-10 flex items-center justify-center ">
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.8% left-0 ml-10 z-2" />
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.8% right-0 mr-10 z-2" />
                    <img src="@/assets/imgs/little2.png" class="absolute h-full w-full" />
                    <span class="text-white text-5 tracking-1 z-[1]"
                        style="font-family: 'DouyuFont', sans-serif;">航线</span>
                </div>

                <!-- 起降场标题 -->
                <div class="relative h-10 flex items-center justify-center ">
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.8% left-0 ml-10 z-2" />
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.8% right-0 mr-10 z-2" />
                    <img src="@/assets/imgs/little2.png" class="absolute h-full w-full" />
                    <span class="text-white text-5 tracking-1 z-[1]"
                        style="font-family: 'DouyuFont', sans-serif;">起降场</span>
                </div>

                <!-- 安全标题 -->
                <div class="relative h-10 flex items-center justify-center ">
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.8% left-0 ml-10 z-2" />
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.8% right-0 mr-10 z-2" />
                    <img src="@/assets/imgs/little2.png" class="absolute h-full w-full" />
                    <span class="text-white text-5 tracking-1 z-[1]"
                        style="font-family: 'DouyuFont', sans-serif;">安全</span>
                </div>
            </div>

            <!-- 第二行 - 内容行 -->
            <div class="grid grid-cols-4 gap-4">
                <!-- 空域内容 -->
                <div class="content-container">
                    <div class="content-placeholder">
                        <!-- 这里放空域相关图片或内容 -->
                        <img src="@/assets/imgs/flyArea.png" class="absolute h-full w-full left-0 z-2"
                            @click="jumpArea()" />
                    </div>
                </div>

                <!-- 航线内容 -->
                <div class="content-container">
                    <div class="content-placeholder">
                        <!-- 这里放航线相关图片或内容 -->
                        <img src="@/assets/imgs/air.png" class="absolute h-full w-full left-0 z-2" @click="jumpAir()" />
                    </div>
                </div>

                <!-- 起降场内容 -->
                <div class="content-container">
                    <div class="content-placeholder">
                        <!-- 这里放起降场相关图片或内容 -->
                        <img src="@/assets/imgs/airfield.png" class="absolute h-full w-full left-0 z-2"
                            @click="jumpAirfield()" />
                    </div>
                </div>

                <!-- 安全内容 -->
                <div class="content-container">
                    <div class="content-placeholder">
                        <!-- 这里放安全相关图片或内容 -->
                        <img src="@/assets/imgs/safety.png" class="absolute h-full w-full left-0 z-2"
                            @click="jumpSafety()" />
                    </div>
                </div>
            </div>
        </div>
        <!-- 底部状态栏 -->
        <div class="w-95% absolute left-3% bottom-0 mb-7 h-38%">
            <!-- 第一行 - 标题行 -->
            <div class="grid grid-cols-2 gap-2 mb-4">
                <!-- 飞行数据标题 -->
                <div class="relative h-10 flex items-center justify-center">
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.3% left-0 ml-10 z-2" />
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.3% right-0 mr-10 z-2" />
                    <img src="@/assets/imgs/little2.png" class="absolute h-full w-full" />
                    <span class="text-white text-5 tracking-1 z-[1]"
                        style="font-family: 'DouyuFont', sans-serif;">飞行数据</span>
                </div>

                <!-- 易损易耗件标题 -->
                <div class="relative h-10 flex items-center justify-center">
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.3% left-0 ml-10 z-2" />
                    <img src="@/assets/imgs/little3.png" class="absolute h-20% w-0.3% right-0 mr-10 z-2" />
                    <img src="@/assets/imgs/little2.png" class="absolute h-full w-full" />
                    <span class="text-white text-5 tracking-1 z-[1]"
                        style="font-family: 'DouyuFont', sans-serif;">易损易耗件</span>
                </div>
            </div>

            <!-- 第二行 - 内容行 -->
            <div class="grid grid-cols-2 gap-2 mb-2">
                <!-- 飞行数据内容 -->
                <div class="content-container-bottom">
                    <div class="content-placeholder-bottom">
                        <!-- 这里放飞行数据相关图片或内容 -->
                        <div id="dataSummaryChart" class="w-full h-full z-2 pb-5"></div>
                    </div>
                </div>

                <!-- 易损易耗件内容 -->
                <div class="content-container-bottom">
                    <div class="content-placeholder-bottom">
                        <!-- 这里放易损易耗件相关图片或内容 -->
                        <div id="mediaDataChart" class="w-full h-full z-2"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { Ref, inject, onMounted, onUnmounted, ref, watch, nextTick, onActivated, } from 'vue';
import "@/assets/font/DouyuFont.woff2";
import { useRouterPush } from '@/hooks/common/router';
import * as echarts from 'echarts';
import {
    fetchIndexOrg, fileSize, flightSize, flightMileageGoup, fetchDataOverview, getLossInfo, flightTimeGoup
} from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
const { routerBack } = useRouterPush();
const authStore = useAuthStore();
const { routerPushByKey } = useRouterPush();
const timeOptions = [
    { label: '全部', value: 0 },
    { label: '本周', value: 1 },
    { label: '上周', value: 2 },
    { label: '本月', value: 3 },
    { label: '上个月', value: 4 },
    { label: '近30天', value: 5 },
    { label: '近六个月', value: 6 }
];
// const selectedTimeRange = ref(0); // 0:全部, 1:本月, 3:近三个月, 6:近六个月
// // 时间范围选择处理函数
// const selectTimeRange = (month: number) => {
//     selectedTimeRange.value = month;
//     // currentPage.value = 1; // 重置页码
//     // hasMore.value = true; // 重置是否有更多数据
//     // initDataSummaryChart(month);
//     // // getSummaryChartM(month)
//     // let stime = '';
//     // let etime = '';
//     // let type = month + '';
//     // initMachineTypeChart(stime, etime, type)
//     // initTaskListChart(month)
// };
// 新增时间相关逻辑
const currentTime = ref('');
let timer: ReturnType<typeof setInterval>;
function formatTime(date: Date): string {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    const hours = String(date.getHours()).padStart(2, '0');
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
}
function updateTime() {
    currentTime.value = formatTime(new Date());
}

// 标题和组织信息
const indexOrg = ref<Api.Home.IndexOrg>({
    aiCount: 0,
    dockCount: 0,
    flightCount: 0,
    flightLineCount: 0,
    onlineDevice: 0,
    resultCount: 0,
    userCount: 0,
    droneCount: 0
});
const getIndexOrg = async () => {
    const res = await fetchIndexOrg();
    indexOrg.value = res.data;
}


interface chart {
    size: number,
    yymm: string
}
interface chartM {
    mileage: number,
    yymm: string
}
interface chartT {
    time: number,
    yymm: string
}
interface DataChartItem {
    drone_sn: string,
    flight_count: number,
    nick_name: string
}
interface DataChartItem1 {
    drone_sn: string,
    mileage: number,
    nickname: string
}
interface DataChart {
    flightCount: Array<DataChartItem>,
    flightTime: Array<DataChartItem>,
    flightMileage: Array<DataChartItem1>
}
let DataChart = ref<DataChart>()
let SummaryChart = ref<chart[]>()
let SummaryChartM = ref<chartM[]>()
let SummaryChartT = ref<chartT[]>()
let dataSummaryChartInstance: echarts.ECharts | null = null;
let mediaDataChartInstance: echarts.ECharts | null = null;
const initMediaDataChart = async () => {
    const chartDom = document.getElementById('mediaDataChart');
    if (!chartDom) return;
    if (mediaDataChartInstance) {
        mediaDataChartInstance.dispose();
        mediaDataChartInstance = null;
    }
    const chartData = await getLossInfo()
    console.log('易损易耗件', chartData.data)
    const chart = echarts.init(chartDom);
    mediaDataChartInstance = chart
    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'axis',
        },
        legend: {
            data: ['健康', '警告', '待报废'],
            textStyle: {
                color: '#fff'
            },
            right: 10,
            top: 10
        },
        grid: {
            left: '20%',
            right: '10%',
            bottom: '15%',
            top: '20%'
        },
        xAxis: {
            type: 'value',
            name: '件',
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0.5)'
                }
            },
            axisLabel: {
                color: '#fff'
            },
            splitLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0.1)'
                }
            }
        },
        yAxis: {
            type: 'category',
            data: [''],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0.5)'
                }
            },
            axisLabel: {
                color: '#fff'
            }
        },
        dataZoom: [
            {
                type: 'slider', // 滑动条型数据区域缩放组件
                show: true,
                yAxisIndex: [0], // 注意这里是yAxisIndex因为这是横向条形图
                start: 0,       // 初始起始位置百分比
                end: 100,       // 初始结束位置百分比
                width: 15,      // 组件宽度（横向滚动条）
                right: 35,       // 组件距离容器右侧距离
                backgroundColor: 'rgba(0,0,0,0.2)',
                dataBackground: {
                    lineStyle: { color: '#4ECB73' },
                    areaStyle: { color: 'rgba(78, 203, 115, 0.2)' }
                },
                borderColor: 'rgba(255,255,255,0.3)',
                fillerColor: 'rgba(78, 203, 115, 0.2)',
                textStyle: { color: '#fff' },
                handleStyle: {
                    color: '#4ECB73',
                    borderColor: 'rgba(78, 203, 115, 0.8)'
                },
                filterMode: 'filter' // 设置为'filter'则数据窗口外的数据会被过滤掉
            },
            {
                type: 'inside', // 内置型数据区域缩放组件
                yAxisIndex: [0],
                zoomOnMouseWheel: false,  // 关闭滚轮缩放
                moveOnMouseMove: true,   // 开启鼠标移动触发平移
                moveOnMouseWheel: true    // 开启滚轮平移
            }
        ],
        series: [
            {
                name: '健康',
                type: 'bar',
                stack: '设备状态',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 1, 0,
                        [
                            { offset: 0, color: 'rgba(78, 203, 115, 0.6)' },  // 浅绿
                            { offset: 1, color: 'rgba(78, 203, 115, 0.8)' }   // 深绿
                        ]
                    )
                },
                data: [0]
            },
            {
                name: '警告',
                type: 'bar',
                stack: '设备状态',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 1, 0,
                        [
                            { offset: 0, color: 'rgba(231, 76, 60, 0.6)' },   // 浅红
                            { offset: 1, color: 'rgba(231, 76, 60, 0.8)' }    // 深红
                        ]
                    )
                },
                data: [0]
            },
            {
                name: '待报废',
                type: 'bar',
                stack: '设备状态',
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(
                        0, 0, 1, 0,
                        [
                            { offset: 0, color: 'rgba(155, 89, 182, 0.6)' },  // 浅紫
                            { offset: 1, color: 'rgba(155, 89, 182, 0.8)' }   // 深紫
                        ]
                    )
                },
                data: [0]
            }
        ]
    };
    let deviceNames = [];
    let healthyData = [];    // 健康状态数据
    let warningData = [];     // 警告状态数据
    let scrapData = [];        // 待报废状态数据
    if (chartData.data) {
        for (let index = 0; index < chartData.data.length; index++) {
            deviceNames.push(chartData.data[index].dockName)
            healthyData.push(chartData.data[index].health)
            warningData.push(chartData.data[index].warn)
            scrapData.push(chartData.data[index].scrapped)
            option.yAxis.data = deviceNames
            option.series[0].data = healthyData
            option.series[1].data = warningData
            option.series[2].data = scrapData
        }
    }
    chart.setOption(option);
    chart.resize();
    let index = 0;
    setInterval(function () {
        chart.dispatchAction({
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: index
        });
        index++;
        if (index > chartData.data.length) {
            index = 0;
        }
    }, 1000);
    window.addEventListener('resize', function () {
        chart.resize();
    });
};
// 飞行数据图表 - 柱状折线混合图
const initDataSummaryChart = async (selectedTimeRange: number) => {
    const chartDom = document.getElementById('dataSummaryChart');
    if (!chartDom) return;
    // 销毁现有实例
    if (dataSummaryChartInstance) {
        dataSummaryChartInstance.dispose();
        dataSummaryChartInstance = null;
    }
    const chart = echarts.init(chartDom);
    dataSummaryChartInstance = chart
    const res = await flightSize(selectedTimeRange, '', '');
    SummaryChart.value = res.data;
    const res1 = await flightMileageGoup(selectedTimeRange, '', '');
    SummaryChartM.value = res1.data;
    const res2 = await flightTimeGoup(selectedTimeRange, '', '');
    console.log('时间---------', res2.data)
    SummaryChartT.value = res2.data;
    const option = {
        backgroundColor: 'transparent',
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['飞行次数(次)', '飞行里程(m)', '飞行时长(min)'],
            textStyle: {
                color: '#fff'
            },
            right: 10,
            top: -5
        },
        grid: {
            left: '15%',
            right: '15%',
            bottom: '15%',
            top: '25%'
        },
        xAxis: {
            type: 'category',
            data: [''],
            axisLine: {
                lineStyle: {
                    color: 'rgba(255,255,255,0.5)'
                }
            },
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '数量',
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,0.5)'
                    }
                },
                axisLabel: {
                    color: '#fff'
                },
                splitLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,0.1)'
                    }
                }
            },
            {
                type: 'value',
                name: '数量',
                axisLine: {
                    lineStyle: {
                        color: 'rgba(255,255,255,0.5)'
                    }
                },
                axisLabel: {
                    color: '#fff'
                },
                splitLine: {
                    show: false
                }
            }
        ],
        dataZoom: [
            {
                type: 'slider', // 滑动条型数据区域缩放组件
                show: true,
                xAxisIndex: [0],
                start: 0,       // 初始起始位置百分比
                end: 100,       // 初始结束位置百分比
                height: 25,     // 组件高度
                bottom: -5,      // 组件距离容器底部距离
                backgroundColor: 'rgba(0,0,0,0.2)',
                dataBackground: {
                    lineStyle: { color: '#4ECB73' },
                    areaStyle: { color: 'rgba(78, 203, 115, 0.2)' }
                },
                borderColor: 'rgba(255,255,255,0.3)',
                fillerColor: 'rgba(78, 203, 115, 0.2)',
                textStyle: { color: '#fff' },
                handleStyle: {
                    color: '#4ECB73',
                    borderColor: 'rgba(78, 203, 115, 0.8)'
                }
            },
            {
                type: 'inside', // 内置型数据区域缩放组件
                xAxisIndex: [0],
                zoomOnMouseWheel: false,  // 关闭滚轮缩放
                moveOnMouseMove: true,   // 开启鼠标移动触发平移
                moveOnMouseWheel: true   // 开启滚轮平移
            }
        ],
        series: [
            {
                name: '飞行次数(次)',
                type: 'bar',
                data: [0],
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#4ECB73' },
                        { offset: 1, color: '#2EC7C9' }
                    ])
                }
            },
            {
                name: '飞行里程(m)',
                type: 'line',
                yAxisIndex: 1,
                data: [0],
                symbol: 'circle',
                symbolSize: 8,
                itemStyle: {
                    color: '#FBD437'
                },
                lineStyle: {
                    width: 3
                }
            },
            // 新增的折线系列
            {
                name: '飞行时长(min)',
                type: 'line',
                yAxisIndex: 1, // 使用右侧Y轴
                data: [0], // 使用静态数据
                symbol: 'diamond',
                symbolSize: 8,
                itemStyle: {
                    color: '#9B59B6' // 紫色
                },
                lineStyle: {
                    width: 3,
                    type: 'dashed' // 虚线样式
                }
            }
        ]
    };

    let yymmList = []
    let sizeList = []
    let mileageList = []
    let timeList = []
    if (SummaryChart.value) {
        for (let index = 0; index < SummaryChart.value.length; index++) {
            yymmList.push(SummaryChart.value[index].yymm)
            sizeList.push(SummaryChart.value[index].size)
            option.xAxis.data = yymmList
            option.series[0].data = sizeList
        }
    }
    if (SummaryChartM.value) {
        for (let index = 0; index < SummaryChartM.value.length; index++) {
            mileageList.push(SummaryChartM.value[index].mileage)
            option.series[1].data = mileageList
        }
    }
    if (SummaryChartT.value) {
        for (let index = 0; index < SummaryChartT.value.length; index++) {
            timeList.push(SummaryChartT.value[index].time)
            option.series[2].data = timeList
        }
    }
    chart.setOption(option);
    chart.resize();
    let index = 0;
    setInterval(function () {
        chart.dispatchAction({
            type: 'showTip',
            seriesIndex: 0,
            dataIndex: index
        });
        index++;
        if (SummaryChart.value && (index > SummaryChart.value.length)) {
            index = 0;
        }
    }, 1000);
    window.addEventListener('resize', function () {
        chart.resize();
    });
};
onMounted(() => {
    // 初始化地图
    updateTime(); // 初始加载时显示当前时间
    timer = setInterval(updateTime, 1000); // 每秒更新一次
    // 初始化图表
    nextTick(() => {
        initDataSummaryChart(0);
        initMediaDataChart();
        getIndexOrg()
    });
});
onActivated(() => {
    // 在这里重新调用图表初始化函数
    nextTick(() => {
        initDataSummaryChart(0);
        initMediaDataChart();
        getIndexOrg()
    });
});
onUnmounted(() => {
    // 确保在组件卸载时销毁所有图表实例
    if (dataSummaryChartInstance) {
        dataSummaryChartInstance.dispose();
        dataSummaryChartInstance = null;
    }

    if (mediaDataChartInstance) {
        mediaDataChartInstance.dispose();
        mediaDataChartInstance = null;
    }
    clearInterval(timer); // 组件卸载时清除定时器
});
function jumpArea() {
    routerPushByKey('flyablearea');
}
function jumpAir() {
    routerPushByKey('allairline');
}
function jumpSafety() {
    routerPushByKey('safety');
}
function jumpAirfield() {
    routerPushByKey('airfield');
}

</script>

<style scoped>
/* 背景图片样式 */
.bg-image {
    background-image: url(@/assets/imgs/home.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.7;
}

.bg-images {
    background-image: url(@/assets/imgs/uphome.png);
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    opacity: 0.7;
}

/* 原有样式调整（移除渐变背景） */
.bg-gradient-to-b {
    background: transparent;
}

/* 时间显示样式（根据实际情况调整） */
.text-4 {
    font-size: 12px;
    font-family: 'DouyuFont', sans-serif;
}


/* 面板容器样式 */
.panel-container {
    display: flex;
    flex-direction: column;
    margin-bottom: 15px;
    /* 面板之间的间距 */
}

/* 标题栏样式 */
.title-bar {
    display: flex;
    align-items: center;
    margin-bottom: 0px;
    /* 标题和面板之间的间距 */
}

.map-filter {
    filter: url('#x-rays') brightness(1.5) contrast(2.4) grayscale(0.22);
}

/* 保持原有样式，只添加必要的图表容器样式 */
#machineTypeChart,
#dataSummaryChart {
    position: relative;
    z-index: 10;
    padding-top: 15px;
}

/* 确保面板内容不被背景图覆盖 */
.relative {
    position: relative;
}

/* 设备信息图标间距 */
.device-item .n-icon {
    margin-right: 4px;
}

.module-container {
    height: 60px;
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(42, 153, 190, 0.7) 0%, rgba(84, 226, 231, 0.7) 100%);
    box-shadow: 0 4px 15px rgba(77, 231, 211, 0.2);
    display: flex;
    align-items: center;
    padding: 0 15px;
    position: relative;
    overflow: hidden;
}

.module-container::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    transform: rotate(30deg);
}

.module-content {
    display: flex;
    align-items: center;
    width: 100%;
    z-index: 1;
}

.module-icon {
    margin-right: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.module-text {
    color: white;
    font-family: 'DouyuFont', sans-serif;
}

.module-text div:first-child {
    font-size: 24px;
    margin-bottom: 5px;
}

.module-text div:last-child {
    font-size: 14px;
    opacity: 0.9;
}

/* 内容容器样式 */
.content-container {
    height: 190px;
    /* 调整高度以适应你的设计 */
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(42, 153, 190, 0.3) 0%, rgba(84, 226, 231, 0.3) 100%);
    box-shadow: 0 4px 15px rgba(77, 231, 211, 0.1);
    position: relative;
    overflow: hidden;
}

.content-placeholder {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: 'DouyuFont', sans-serif;
    padding: 10px;
    box-sizing: border-box;
}

/* 添加一些装饰元素 */
.content-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.content-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

/* 内容容器样式 */
.content-container-bottom {
    height: 280px;
    /* 调整高度以适应你的设计 */
    border-radius: 8px;
    background: linear-gradient(135deg, rgba(42, 153, 190, 0.3) 0%, rgba(84, 226, 231, 0.3) 100%);
    box-shadow: 0 4px 15px rgba(77, 231, 211, 0.1);
    position: relative;
    overflow: hidden;
}

.content-placeholder-bottom {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-family: 'DouyuFont', sans-serif;
    padding: 1px;
    box-sizing: border-box;
}

/* 添加一些装饰元素 */
.content-container-bottom::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

.content-container-bottom::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

/* 新增流光边框动画 */
@keyframes flowing-light {
    0% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }

    100% {
        background-position: 0% 50%;
    }
}

/* 模块卡片光效 */
.module-container {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.module-container:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px -5px rgba(78, 203, 115, 0.5);
}

/* 流光粒子效果 */
.module-container::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 50%;
    height: 100%;
    background: linear-gradient(90deg,
            rgba(255, 255, 255, 0) 0%,
            rgba(255, 255, 255, 0.1) 50%,
            rgba(255, 255, 255, 0) 100%);
    transform: skewX(-25deg);
    animation: flowing-particles 6s linear infinite;
}

@keyframes flowing-particles {
    0% {
        left: -100%;
    }

    100% {
        left: 150%;
    }
}

/* 数据图表容器光效 */
.content-container,
.content-container-bottom {
    position: relative;
    overflow: hidden;
}

.content-container::before,
.content-container-bottom::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle,
            rgba(78, 203, 115, 0.15) 0%,
            transparent 70%);
    animation: pulse-glow 8s linear infinite;
}

@keyframes pulse-glow {

    0%,
    100% {
        transform: scale(0.8);
        opacity: 0.3;
    }

    50% {
        transform: scale(1);
        opacity: 0.5;
    }
}
</style>