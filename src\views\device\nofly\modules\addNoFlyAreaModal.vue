<template>
  <n-modal v-model:show="showModal" :title-style="{ fontSize: '16px', fontWeight: 600 }" title="选择禁飞区类型" preset="dialog"
    class="select-modal" :showIcon="false" @close="changeModalStatus(false)">
    <div class="modal-content">
      <div class="option" @click="selectType('polygon')">
        <n-card class="option-card" :class="{ selected: selectedType === 'polygon' }">
          <template #header>
            <span>
              多边形禁飞区
            </span>
          </template>
          <n-image width="48" :src="polygonImg" preview-disabled />
        </n-card>
      </div>
      <div class="option" @click="selectType('circle')">
        <n-card class="option-card" :class="{ selected: selectedType === 'circle' }">
          <template #header>
            <span>
              圆形禁飞区
            </span>
          </template>
          <n-image width="48" :src="circleImg" preview-disabled />
        </n-card>
      </div>
    </div>
    <template #action>
      <n-button type="primary" @click="clickEnter">确认</n-button>
      <n-button @click="cancelSelection">取消</n-button>
    </template>
  </n-modal>
</template>

<script setup lang="ts">
import { ref, toRefs } from 'vue';
import { NModal, NCard } from 'naive-ui';
import circleImg from '@/assets/imgs/circle.png';
import polygonImg from "@/assets/imgs/polygon.png";
const props = defineProps<{
  showModal: boolean;
  changeModalStatus: (status: boolean) => void;
  confirmSelection: (selectedType: string) => void;
}>();
const { showModal } = toRefs(props);
const { changeModalStatus, confirmSelection } = props;
const selectedType = ref<string | null>(null);

const selectType = (type: string) => {
  selectedType.value = type;
};

const clickEnter = () => {
  if (selectedType.value) {
    confirmSelection(selectedType.value);
  }
  selectedType.value = null;
  changeModalStatus(false);
};
const cancelSelection = () => {
  selectedType.value = null;
  changeModalStatus(false);
};
</script>

<style scoped lang="scss">
.select-modal {
  width: 700px;
  background-color: #fff;
}

.modal-content {
  display: flex;
  justify-content: space-evenly;
  margin-top: 30px;
}

.option {
  width: 170px;
  cursor: pointer;
  transition: transform 0.2s;
  font-size: 18px;
}

.option-card {
  text-align: center;
  padding: 10px;

  span {
    font-size: 16px;
    font-weight: bold;
  }
}

.option:hover {
  transform: scale(1.05);
}

.selected {
  border: 2px solid blue;
  border-radius: 8px;
}
</style>
