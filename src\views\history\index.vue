<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { NPopover, NTag, NText, useDialog, useMessage } from 'naive-ui';
import dayjs from 'dayjs';
import { deptTree } from '@/service/api/user';
import { deleteTaskRecord, fetchAirLineList, fetchFlyHistoryList, fetchStatusList } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';

const dialog = useDialog();
const message = useMessage();
const loading = ref(false);
const hasMore = ref(true);
const { routerPushByKey } = useRouterPush();
const flyHistoryData = ref<Api.AirLine.AirLineItem[]>([]);

const formValue = reactive<{
  deptId: number | null;
  dockName: string;
  flightJobType: string | null;
  createName: string;
  droneName: string;
  flightJobName: string;
  dateRange: [number, number] | null;
  flightId: number | null;
}>({
  deptId: null,
  flightJobName: '',
  flightJobType: null,
  droneName: '',
  dockName: '',
  createName: '',
  dateRange: null,
  flightId: null
});

const airLineList = ref<Api.Task.TaskItem[]>([]);
const tableLoading = ref(true);
const treeData = ref([]);
let generalStatusOptions: { label: string; value: string }[];

const tableColumns = [
  { title: '组织', key: 'workspaceName' },
  { title: '飞行时间', key: 'execTime' },
  { title: '任务名称', key: 'flightJobName' },
  {
    title: '航线名称',
    key: 'flightName',
    render(row: Api.Task.TaskItem) {
      return h(
        NText,
        {
          type: ''
        },
        {
          default: () => (!row.flightName || row.flightName === '手动飞行' ? '---' : row.flightName)
        }
      );
    }
  },
  {
    title: '任务类型',
    key: 'flightJobType',
    render(row: Api.Task.TaskItem) {
      return h(
        NTag,
        {
          type: row.flightJobType === 1 ? 'info' : row.flightJobType === 2 ? 'success' : 'warning',
          bordered: false
        },
        {
          default: () => (row.flightJobType === 1 ? '立即执行' : row.flightJobType === 2 ? '定时任务' : '周期任务')
        }
      );
    }
  },
  {
    title: '执行状态',
    key: 'status',
    render(row: Api.Task.TaskItem) {
      return h(
        NPopover,
        {
          trigger: 'hover',
          disabled: row.status !== 1
        },
        {
          trigger: () =>
            h(
              NTag,
              {
                type: row.status === 0 ? 'info' : row.status === 1 ? 'error' : 'default'
              },
              {
                default: () => (row.status === 0 ? '执行完成' : row.status === 1 ? '执行失败' : '-----')
              }
            ),
          default: () => row.exceptionInfo && JSON.parse(row.exceptionInfo).msg
        }
      );
    }
  },
  { title: '机场名称', key: 'dockName' },
  { title: '飞行器名称', key: 'droneName' },
  { title: '里程 | 时间', key: 'flightInfo' },
  { title: '操作员', key: 'operator' },
  // { title: '所属组织', key: 'organization' },
  // { title: '飞行地点', key: 'address' },
  {
    title: '操作',
    key: 'action',
    render(row: any) {
      return h('span', [
        h(
          NText,
          {
            type: 'info',
            class: 'cursor-pointer mr-10px fz-25px',
            onClick: () => look(row)
          },
          { default: () => '回放' }
        ),
        h(
          NText,
          {
            type: 'info',
            class: 'cursor-pointer mr-10px fz-25px',
            onClick: () => report(row)
          },
          { default: () => '报告' }
        ),
        h(
          NText,
          {
            type: 'error',
            class: 'cursor-pointer',
            onClick: () => deleteFlyHistory(row)
          },
          { default: () => '删除' }
        )
      ]);
    }
  }
];

const disablePreviousDate = (ts: number) => {
  return ts > Date.now();
};

const handleResetClick = () => {
  tableLoading.value = true;
  formValue.deptId = null;
  formValue.flightJobType = null;
  formValue.flightJobName = '';
  formValue.droneName = '';
  formValue.dockName = '';
  formValue.createName = '';
  formValue.flightId = null;
  formValue.dateRange = null;
  getFlyHistoryList();
};

// 点击查询
const handleQueryClick = () => {
  tableLoading.value = true;
  getFlyHistoryList();
};

const paginationReactive = reactive({
  page: 1,
  pageCount: 1,
  pageSize: 10,
  itemCount: 0,
  onChange: (page: number) => {
    // 切换第几页时
    paginationReactive.page = page;
    handleQueryClick();
  }
});

async function look(row: Api.Task.TaskItemHis) {
  if (row.status === 0) {
    // let res = await getVideo(row.recordId, row.droneSn);
    if (row.recordId) {
      routerPushByKey('flyhistroy', {
        query: {
          recordId: String(row.recordId)
        }
      });
    }
  } else {
    message.warning('该飞行任务正在处理中，回放不可用！');
  }
}

async function report(row: Api.Task.TaskItemHis) {
  if (row.status === 0) {
    if (row.recordId) {
      routerPushByKey('report', {
        query: {
          recordId: String(row.recordId)
        }
      });
    }
  } else {
    message.warning('该飞行任务异常，报告未生成！');
  }
}
const pagination = ref({
  pageNum: 1,
  pageSize: 20,
  total: 0,
  keyword: ''
});
// 加载选项数据
const loadOptions = async (isSearch = false) => {
  if (loading.value) return;
  loading.value = true;
  try {
    if (isSearch) {
      pagination.value.pageNum = 1;
      airLineList.value = [];
      hasMore.value = true;
    }
    if (!hasMore.value) return;

    const response = await fetchAirLineList({
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize
    });
    if (response.data.rows.length === 0) {
      hasMore.value = false;
      if (pagination.value.pageNum === 1) {
        message.info('暂无数据');
      } else {
        message.info('没有更多数据了');
      }
      return;
    }

    // 转换数据格式为 NSelect 需要的格式
    const newOptions = response.data.rows.map((item: Api.AirLine.AirLineItem) => ({
      label: item.flightName, // 假设 flightName 是显示文本
      value: item.flightId // 假设 flightId 是值
    }));

    airLineList.value = [...airLineList.value, ...newOptions];
    pagination.value.total = response.data.total;

    if (response.data.rows.length < pagination.value.pageSize) {
      hasMore.value = false;
    } else {
      pagination.value.pageNum += 1;
    }
  } catch (error) {
    message.error('加载数据失败');
    console.error(error);
  } finally {
    loading.value = false;
  }
};

// 滚动事件处理
const handleScroll = (e: any) => {
  const { scrollTop, scrollHeight, clientHeight } = e.target;
  // 接近底部时加载更多
  if (scrollHeight - (scrollTop + clientHeight) < 50) {
    loadOptions();
  }
};

// // 搜索处理
// const handleSearch = (query) => {
//   pagination.value.keyword = query
//   loadOptions(true)
// }

async function deleteFlyHistory(row: Api.Task.TaskItem) {
  dialog.warning({
    title: '确认删除',
    content: `确认删除这条飞行历史记录吗？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      if (row.recordId) {
        // 从 airLineList 中移除该项
        const res = await deleteTaskRecord(row.recordId);
        if (res.data) {
          handleQueryClick();
          message.success('删除成功');
        }
      }
    },
    onNegativeClick: () => {
      // 取消操作
    }
  });
}

async function getStatus(dataType: string) {
  const { data } = await fetchStatusList(dataType);
  generalStatusOptions = data.map((v: any) => ({
    label: v.dictLabel,
    value: v.dictValue
  }));
}

// 获取飞行历史列表
async function getFlyHistoryList() {
  const { flightJobName, flightJobType, createName, dockName, droneName, dateRange, deptId, flightId } = formValue;

  const json: Api.Task.TaskScreen = {
    deptId: deptId || undefined,
    droneName,
    dockName,
    flightJobName,
    createName,
    flightId: flightId || undefined,
    flightJobType: flightJobType ? Number(flightJobType) : undefined,
    startDate: dateRange && dateRange[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : null,
    endDate: dateRange && dateRange[1] ? dayjs(dateRange[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null,
    pageNum: paginationReactive.page,
    pageSize: paginationReactive.pageSize
  };
  const { data, error } = await fetchFlyHistoryList(json);
  if (!error) {
    tableLoading.value = false;
    flyHistoryData.value = data.rows;
    paginationReactive.itemCount = data.total;
  }
}

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
}

onMounted(() => {
  // getAirLineList();
  loadOptions();
  getDeptTree();
  getStatus('flight_job_type');
  getFlyHistoryList();
});
</script>

<template>
  <div>
    <NCard title="飞行历史记录" :bordered="false">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :cols="4" :x-gap="24">
          <NFormItemGi label="组织选择：" path="deptId">
            <NTreeSelect
              v-model:value="formValue.deptId"
              clearable
              filterable
              key-field="id"
              label-field="label"
              children-field="children"
              :options="treeData"
              :default-value="formValue.deptId"
            />
          </NFormItemGi>
          <NFormItemGi label="任务名称：" path="flightJobName">
            <NInput v-model:value="formValue.flightJobName" clearable placeholder="输入任务名称" />
          </NFormItemGi>
          <NFormItemGi label="任务类型：" path="flightJobType">
            <NSelect
              v-model:value="formValue.flightJobType"
              placeholder="请选择任务类型"
              clearable
              :options="generalStatusOptions"
            />
          </NFormItemGi>
          <NFormItemGi label="航线名称：" path="flightId">
            <!--
 <NSelect v-model:value="formValue.flightId" placeholder="请选择" :options="airLineList" value-field="flightId"
              clearable label-field="flightName" filterable />
-->
            <NSelect
              v-model:value="formValue.flightId"
              :options="airLineList"
              :loading="loading"
              filterable
              clearable
              :reset-menu-on-options-change="false"
              @scroll="handleScroll"
            />
          </NFormItemGi>
          <NFormItemGi label="操作员：" path="createName">
            <NInput v-model:value="formValue.createName" clearable placeholder="请输入飞手名称" />
          </NFormItemGi>
          <NFormItemGi label="日期范围：" path="dateRange">
            <NDatePicker
              v-model:value="formValue.dateRange"
              value-format="yyyy.MM.dd"
              type="daterange"
              :is-date-disabled="disablePreviousDate"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi label="机场名称：" path="dockName">
            <NInput v-model:value="formValue.dockName" clearable placeholder="请输入机场名称" />
          </NFormItemGi>
          <NFormItemGi label="飞行器名称：" path="droneName">
            <NInput v-model:value="formValue.droneName" clearable placeholder="请输入飞行器名称" />
          </NFormItemGi>
          <NFormItemGi>
            <NButton attr-type="button" class="mr2" @click="handleResetClick">重置</NButton>
            <NButton type="primary" attr-type="button" @click="handleQueryClick">查询</NButton>
          </NFormItemGi>
        </NGrid>
      </NForm>

      <NDataTable
        :columns="tableColumns"
        :data="flyHistoryData"
        :loading="tableLoading"
        :pagination="paginationReactive"
        remote
      />
    </NCard>
  </div>
</template>

<style scoped></style>
