<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
defineOptions({ name: 'VideoItem' });

// 定义组件属性接口
interface Props {
  src?: string; // 视频文件地址，支持网络地址 https 和相对地址
  poster?: string; // 视频封面地址，支持网络地址 https 和相对地址
  second?: number; // 在未设置封面时，自动截取视频第 second 秒对应帧作为视频封面
  autoplay?: boolean; // 视频就绪后是否马上播放，优先级高于 preload
  controls?: boolean; // 是否向用户显示控件，比如进度条，全屏等
  loop?: boolean; // 视频播放完成后，是否循环播放
  muted?: boolean; // 是否静音
  preload?: 'auto' | 'metadata' | 'none'; // 是否在页面加载后载入视频，如果设置了 autoplay 属性，则 preload 将被忽略
  showPlay?: boolean; // 播放暂停时是否显示播放器中间的暂停图标
  fit?: 'none' | 'fill' | 'contain' | 'cover'; // video 的 poster 默认图片和视频内容缩放规则
}

// 使用 withDefaults 设置属性默认值
const props = withDefaults(defineProps<Props>(), {
  src: undefined,
  poster: undefined,
  second: 0.5,
  autoplay: false,
  controls: true,
  loop: false,
  muted: false,
  preload: 'metadata',
  showPlay: true,
  fit: 'contain'
});

// 定义 ref 变量
const veoRef = ref();
const veoPoster = ref();
const originPlay = ref(true);
const hidden = ref(false); // 是否隐藏播放器中间的播放按钮
const isPlaying = ref(false); // 当前是否正在播放

// 在组件挂载时执行
onMounted(() => {
  if (props.autoplay) {
    hidden.value = true;
    originPlay.value = false;
  }
});

// 获取视频封面
function getPoster() {
  veoRef.value.currentTime = props.second;
  const canvas = document.createElement('canvas');
  const ctx = canvas.getContext('2d');
  canvas.width = veoRef.value.videoWidth;
  canvas.height = veoRef.value.videoHeight;
  ctx?.drawImage(veoRef.value, 0, 0, canvas.width, canvas.height);
  veoPoster.value = canvas.toDataURL('image/png');
}

// 播放视频
function onPlay() {
  if (originPlay.value) {
    veoRef.value.currentTime = 0;
    originPlay.value = false;
  }
  if (props.autoplay) {
    veoRef.value?.pause();
  } else {
    hidden.value = true;
    veoRef.value?.play();
    isPlaying.value = true;
  }
}

// 暂停视频
function onPause() {
  hidden.value = false;
  isPlaying.value = false;
}

// 视频播放中
function onPlaying() {
  hidden.value = true;
  isPlaying.value = true;
}

// 鼠标移入事件
function onMouseEnter() {
  hidden.value = false;
}

// 鼠标移出事件
function onMouseLeave() {
  if (!isPlaying.value && originPlay.value) {
    hidden.value = false;
  } else {
    hidden.value = true;
  }
}
</script>

<template>
  <!-- 视频播放器容器 -->
  <div :class="['relative', 'bg-black', 'cursor-pointer', 'w-full', 'h-full', { 'hover:bg-black/70': !hidden }]"
    @mouseover="onMouseEnter" @mouseout="onMouseLeave">
    <!-- 视频元素 -->
    <video ref="veoRef" :class="['inline-block', 'w-full', 'h-full', 'vertical-bottom', `object-${props.fit}`]"
      :src="src" :poster="poster ? poster : veoPoster"
      :autoplay="autoplay" :controls="!originPlay && controls" :loop="loop" :muted="autoplay || muted"
      :preload="preload" crossorigin="anonymous" @loadedmetadata="poster ? () => false : getPoster()"
      @pause="showPlay ? onPause() : () => false" @playing="showPlay ? onPlaying() : () => false"
      @click.prevent.once="onPlay" v-bind="$attrs">
      您的浏览器不支持video标签。
    </video>
    <!-- 播放按钮 -->
    <span v-show="!isPlaying && (originPlay || showPlay)" :class="['absolute', 'top-0', 'right-0', 'bottom-0', 'left-0', 'm-auto', 'w-20', 'h-20', 'rounded-full', 'bg-black/60', 'pointer-events-none', 'transition-colors', { 'opacity-0': hidden }]"
      @mouseover="onMouseEnter" @mouseout="onMouseLeave">
      <SvgIcon icon="mdi:play" class="inline-block text-#eee fill-white w-16 h-16 mt-2 ml-2" />
    </span>
  </div>
</template>