// 机场相关接口
import { LocationQueryValue } from 'vue-router';
import { request, pRequest } from '../request';

// 获取机场注册信息
export function fetchAirportRegisterInfo() {
  return request({ url: '/device/gateway', method: 'get' });
}

// 发送机场注册信息
export function sendAirportRegisterInfo(data: Api.Airport.MqttRegisterInfo) {
  return request({ url: '/device/gateway', method: 'post', data });
}

// 发送设备注册信息(机场、无人机)
export function sendDeviceRegisterInfo(data: Api.Airport.MqttRegisterInfo) {
  return request({ url: '/device/addGateway', method: 'post', data });
}

// 删除某条机场注册信息 - id
export function deleteAirportByID(id: number) {
  return request({ url: `/device/gateway/${id}`, method: 'delete' });
}

// 获取机场列表
export function fetchAirportList(data: { name: string, deptId?: number } & Api.List.Table) {
  return request({
    url: '/device/gatewayPage',
    method: 'get',
    params: data
  });
}

// 解绑某条机场注册信息 - id
export function resetAirport(data: object) {
  return request({
    url: `/device/rescissionGateway`,
    method: 'post',
    data
  });
}
// 解绑某条机场注册信息 - id
export function updateNickName(data: object) {
  return request({
    url: `/device/updateNickName`,
    method: 'post',
    data
  });
}

// 获取飞行页面已激活的机场列表
export function fetchAirportDeviceList() {
  return request({ url: '/web/device/record' });
}

// 获取机场调试列表 eg. 7CTDM3900BM6WS
export function fetchDebugMenu(deviceSN: string) {
  return request({ url: `/deviceDebug/getDebugMenu?sn=${deviceSN}` });
}

// 开启远程调试
export function openRemoteDebug(deviceSN: string) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/jobs/debug_mode_open`, method: 'post' });
}

// 关闭远程调试
export function closeRemoteDebug(deviceSN: string) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/jobs/debug_mode_close`, method: 'post' });
}

// 控制调试各项远程菜单
export function triggerRemoteDebugItem(deviceSN: string, type: string, action: object) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/jobs/${type}`, method: 'post', data: action });
}

// 查询设备当前状态
export function fetchDebugStatus(deviceSN: string) {
  return request({ url: `/deviceDebug/getDeviceInfo/${deviceSN}` });
}

// 开启直播 params.sn , url_type:1-RTSP,0-声网
export function startLivestream(data: object) {
  return request({ url: '/manage/api/v1/live/streams/start', method: 'post', data: { ...data, url_type: 1 } });
}

// 停止直播 params.sn
export function stopLivestream(data: object) {
  return request({ url: '/manage/api/v1/live/streams/stop', method: 'post', data });
}

// 获取直播流参数 params.sn
export function fetchLivestreamInfo(deviceSN: string) {
  return request({ url: `/manage/api/v1/live/allocationAgora/${deviceSN}`, method: 'post' });
}

// 根据ID删除某条航线
export function deleteAirLineByID(flightId: string) {
  return request({ url: `/line/flight/${flightId}`, method: 'delete' });
}
// 获取航线列表
export function fetchAirLineList(params: Api.List.Table) {
  return request({ url: '/line/flight/list', method: 'get', params });
}

// 解析航线列表
export function analysisAirLine(params: string) {
  return request({ url: `/analysisAirLine?airFileName=${params}`, method: 'get' });
}

// 发送航点航线保存信息
export function sendAirLineSaveInfo(data: any) {
  return request({ url: '/line/flight/edit', method: 'put', data });
}

// 获取航点航线保存信息
export function fetchAirLineSaveInfo(flightId: string) {
  return request({ url: `/line/flight/getById/${flightId}`, method: 'get' });
}

// 下载航点航线
export function fetchDownloadKMZ(flightId: string) {
  return request({ url: `/line/flight/download/kmz/${flightId}`, method: 'get' });
}

// 获取航点信息
export function getJobById(record_id: string) {
  return request({ url: `/line/flight/getJobById/${record_id}`, method: 'get' });
}

// 获取飞行控制权
export function fetchFlightControl(deviceSN: string) {
  return request({ url: `/control/api/v1/workspaces/drc/enter/${deviceSN}`, method: 'post' });
}

// 获取负载控制权
export function fetchPayloadControl(deviceSN: string) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/authority/payload`, method: 'post' });
}

// 云台重置
export function fetchPayloadReset(deviceSN: string, data: { resetMode: string }) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'gimbal_reset',
      data
    }
  });
}

// 云台摄像头类型变化-广角/变焦/红外
export function fetchPayloadCameraMode(deviceSN: string, videoType: string) {
  return request({
    url: `/manage/api/v1/live/streams/switch/${deviceSN}`, method: 'post',
    data: {
      video_quality: 0,
      videoType
    }
  });
}

// 云台摄像头切换为分屏-变焦/红外
export function fetchPayloadCameraSplitScreen(deviceSN: string, enable: boolean) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'camera_screen_split',
      data: { enable }
    }
  });
}

// 云台摄像头-红外测温模式设置 type:0-关闭 1-点测温 2-区域测温(默认全屏)
export function fetchPayloadCameraIRTemp(deviceSN: string, type: string | number) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'ir_metering_mode_set',
      data: { mode: type}
    }
  });
}

// 云台摄像模式切换-拍照cameraMode:0/录像cameraMode:1
export function fetchPayloadCameraSwitch(deviceSN: string, cameraMode: number) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'camera_mode_switch',
      data: { cameraMode }
    }
  });
}

// 云台摄像头变焦
export function fetchPayloadCameraZoom(deviceSN: string, data: { cameraType: string, zoomFactor: number }) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'camera_focal_length_set',
      data
    }
  });
}

// 云台摄像头拍照
export function fetchPayloadCameraPhotoTake(deviceSN: string) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'camera_photo_take'
    }
  });
}

// 云台摄像头开始录像
export function fetchPayloadCameraRecordStart(deviceSN: string) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'camera_recording_start'
    }
  });
}

// 云台摄像头停止录像
export function fetchPayloadCameraRecordStop(deviceSN: string) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'camera_recording_stop'
    }
  });
}

// 云台摄像头旋转控制
export function fetchPayloadCameraControl(deviceSN: string, data: { pitchSpeed: number, yawSpeed: number }) {
  return request({
    url: `/control/api/v1/devices/${deviceSN}/payload/commands`, method: 'post',
    data: {
      cmd: 'camera_screen_drag',
      data: {
        locked: false,
        ...data
      }
    }
  });
}

// 地图搜索
export function fetchTianDiMapSearch(data: { keyWord: string, mapBound: string }) {
  // 构造请求配置对象
  return request({
    url: `/other/api/tiandituSearch`,
    method: 'post',
    data: {
      ...data,
    }
  })
}
// 获取所有航线信息
export function fetchAllAirlineList() {
  return request({ url: 'index/allFlight', method: 'get' });
}
export function imgUpload(recordId: string, sn: string | LocationQueryValue[], formData: FormData
) {
  return request({
    url: `/aifile/upload/${recordId}/${sn}`,
    method: 'post',
    data: formData
  })
}

export function uploadFileInfo(data: {
  eventId: string,
  deviceSn: string,
  deviceName: string,
  aiSubType: string,
  eventName: string
}) {
  return request({
    url: `/aifile/uploadFileInfo`,
    method: 'post',
    data: data
  })
}
