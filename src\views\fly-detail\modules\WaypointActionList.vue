<template>
  <div 
    class="absolute bottom-0 right-0 bg-dark-theme border border-gray-600 shadow-lg overflow-y-hidden transition-all duration-300"
    :class="{
      'w-40px h-40px rd-50% mb-25%': isCollapsed,
      'w-20% h-300px mb-25.2%': !isCollapsed
    }"
    :style="{
      margin: isCollapsed ? '0px 10px 25% 0px' : '',
      paddingTop: '10px',
      paddingBottom: '10px',
      color: 'white'
    }"
    v-if="props.taskName !== '手动飞行' && props.taskName"
  >
    <!-- 标题栏 -->
    <div class="relative">
      <div v-if="!isCollapsed" class="font-bold text-15px mb-10px text-white">
        航点动作列表
      </div>
      <div 
        class="absolute right-0 top--2px cursor-pointer transition-colors duration-300 hover:text-blue-500"
        :style="{ left: !isCollapsed ? '' : '1px' }"
        @click="toggleCollapse"
      >
        <n-icon :size="24">
          <svg v-if="!isCollapsed" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
          </svg>
          <img src="@/assets/imgs/action.png" v-else />
        </n-icon>
      </div>
    </div>

    <!-- 内容区域 -->
    <div v-if="!isCollapsed" class="text-center h-full bg-dark-7">
      <n-carousel 
        direction="vertical" 
        mousewheel 
        class="w-full h-full" 
        autoplay 
        :interval="2000" 
        draggable
        effect="fade" 
        :show-dots="false"
      >
        <div class="pt-5%" v-if="props.flightPointActionList.length > 0">
          航点{{ props.sort }}：{{ props.flightPointActionList.map(a => a.actionActuatorFuncDesc).join('，') }}
        </div>
        <div v-if="props.flightPointActionList.length <= 0" class="pt-5%">
          暂无航点动作 (长度: {{ props.flightPointActionList.length }})
        </div>
      </n-carousel>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

// 定义接口
interface FlightPointAction {
  actionActuatorFuncDesc: string;
  [key: string]: any;
}

// 定义 props
interface Props {
  taskName?: string;
  flightPointActionList: FlightPointAction[];
  sort: number;
}

const props = withDefaults(defineProps<Props>(), {
  taskName: '',
  flightPointActionList: () => [],
  sort: 0
});

// 响应式数据
const isCollapsed = ref(true);

// 调试：监听props变化
watch(() => props.flightPointActionList, (newVal) => {
  console.log('WaypointActionList - flightPointActionList changed:', newVal);
}, { deep: true, immediate: true });

watch(() => props.taskName, (newVal) => {
  console.log('WaypointActionList - taskName changed:', newVal);
}, { immediate: true });

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped>
/* 如果需要额外的样式可以在这里添加 */
</style>
