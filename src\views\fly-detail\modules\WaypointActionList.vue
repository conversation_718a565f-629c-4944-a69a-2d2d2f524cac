<template>
  <div 
    class="absolute bottom-0 right-0 bg-dark-theme border border-gray-600 shadow-lg overflow-y-hidden transition-all duration-300"
    :class="{
      'w-40px h-40px rd-50% mb-25%': isCollapsed,
      'w-20% h-120px mb-25.2%': !isCollapsed
    }"
    :style="{
      margin: isCollapsed ? '0px 10px 25% 0px' : '',
      paddingTop: '10px',
      paddingBottom: '10px',
      color: 'white'
    }"
    v-if="props.taskName !== '手动飞行' && props.taskName"
  >
    <!-- 标题栏 -->
    <div class="relative">
      <div v-if="!isCollapsed" class="font-bold text-15px mb-10px text-white text-left pl-20px">
        航点动作列表
      </div>
      <div 
        class="absolute right-0 top--2px cursor-pointer transition-colors duration-300 hover:text-blue-500"
        :style="{ left: !isCollapsed ? '' : '1px' }"
        @click="toggleCollapse"
      >
        <n-icon :size="24">
          <svg v-if="!isCollapsed" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
          </svg>
          <img src="@/assets/imgs/action.png" v-else />
        </n-icon>
      </div>
    </div>

    <!-- 内容区域 -->
    <div v-if="!isCollapsed" class="bg-dark-7 rd-6px p-12px">
      <!-- 航点信息 -->
      <div v-if="props.flightPointActionList.length > 0" class="flex items-center gap-8px mb-8px">
        <div class="w-24px h-24px bg-blue-500 rd-50% flex items-center justify-center text-white text-12px font-bold">
          {{ props.sort }}
        </div>
        <span class="text-white text-14px font-medium">航点</span>
      </div>

      <!-- 动作列表 -->
      <div class="h-54px overflow-y-auto">
        <div v-if="props.flightPointActionList.length > 0" class="text-gray-300 text-13px leading-relaxed">
          <span
            v-for="(action, index) in props.flightPointActionList"
            :key="index"
            class="inline-block px-6px py-2px bg-dark-8 rd-4px mr-4px mb-4px"
          >
            {{ action.actionActuatorFuncDesc }}
          </span>
        </div>
        <div v-else class="flex items-center justify-center h-full text-gray-500 text-13px">
          暂无航点动作
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 定义接口
interface FlightPointAction {
  actionActuatorFuncDesc: string;
  [key: string]: any;
}

// 定义 props
interface Props {
  taskName?: string;
  flightPointActionList: FlightPointAction[];
  sort: number;
}

const props = withDefaults(defineProps<Props>(), {
  taskName: '',
  flightPointActionList: () => [],
  sort: 0
});

// 响应式数据
const isCollapsed = ref(true);



// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
</script>

<style scoped>
/* 如果需要额外的样式可以在这里添加 */
</style>
