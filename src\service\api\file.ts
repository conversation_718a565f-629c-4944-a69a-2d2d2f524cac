import { request } from '../request';

// 上传base64
export function uploadBase64<PERSON>ommon(base64String: string | null) {
  return request({ url: '/common/upload/base64', method: 'post', data: { base64: base64String } });
}

/**
 * 获取照片/视频
 * "fileType": "0", //文件类型0图片1视频
 */
export function fetchFilesList(params: Api.File.FileQuery = { pageNum: 1, pageSize: 10 }) {
  return request({ url: '/media/api/v1/files/list', method: 'get', params });
}
