<template>
  <div>
    <NCard :bordered="false" title="模型库">
      <!-- 添加按钮 -->
      <n-form ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <n-grid :cols="24" :x-gap="24">
          <n-form-item-gi :span="8" label="模型名称：" path="fileName">
            <n-input v-model:value="formValue.fileName" placeholder="输入模型名称" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="排序方式：" path="sortBy">
            <n-select v-model:value="formValue.sortBy" placeholder="选择排序方式" :options="generalAirportOptions"
              clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="上传日期：" path="dateRange">
            <n-date-picker v-model:value="formValue.dateRange" type="daterange" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8" label="模型类型：" path="fileType">
            <n-select v-model:value="formValue.fileType" placeholder="请选择模型类型" :options="modelTypeOptions" clearable />
          </n-form-item-gi>
          <n-form-item-gi :span="8">
            <n-button attr-type="button" @click="handleResetClick" class="mr2">重置</n-button>
            <n-button type="primary" attr-type="button" @click="handleQueryClick" class="mr2">查询</n-button>
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <n-button attr-type="button" @click="showModal1 = !showModal1" type="primary">
        <n-icon size="20" color="black">
          <CloudUploadOutline />
        </n-icon>
        <span style="margin-left: 5px;">上传</span>
      </n-button>
      <n-divider />

      <n-spin :show="loading">
        <n-infinite-scroll style="height: 69vh" :distance="10" @load="handleLoad">
          <n-empty v-if="!loading && airLineList.length === 0" description="暂无模型数据" size="large">
            <template #extra>
              <n-button size="small" @click="handleQueryClick">重新加载</n-button>
            </template>
          </n-empty>
          <NGrid v-else x-gap="50" y-gap="20" cols="600:2 900:3 1200:4">
            <NGi v-for="(i, index) in airLineList" :key="index" class="group relative">
              <NCard class="relative p-5"
                @click="goModel({ name: i.name, id: i.id, userId: i.createBy, deptId: i.workspaceId })">
                <div class="flex">
                  <NImage preview-disabled :src="i.flightImage" class="h-32 w-45%" />
                  <div class="w-50% flex-col justify-between pl-20px">
                    <div class="pb-10px font-size-18px font-bold">
                      <NTooltip trigger="hover">
                        <template #trigger>
                          <h3 class="w-98% truncate">{{ i.name }}</h3>
                        </template>
                        {{ i.name }}
                      </NTooltip>
                    </div>
                    <div>
                      <h4>
                        {{
                          i.type === 1
                            ? '三维模型' : '其他'
                        }}
                      </h4>
                      <h4>{{ i.createTime }}</h4>
                      <h4>{{ i.createName }}</h4>
                    </div>
                  </div>
                </div>
              </NCard>
            </NGi>
          </NGrid>
        </n-infinite-scroll>
      </n-spin>
    </NCard>

    <!-- 引入弹窗组件 -->
    <n-modal v-model:show="showModal1" title="上传模型" preset="card" draggable :on-after-leave="onAfterLeave"
      :style="{ width: '80vw', height: '80vh', margin: ' 0 auto' }">
      <Upload ref="modelDialog" />
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue';
import { NEmpty, NSpin, NButton, NCard, NForm, NGrid, NFormItemGi, NInput, NSelect, NDatePicker, NDivider } from 'naive-ui';
import Upload from '../upload/index.vue';
import dayjs from 'dayjs';
import { useRouter } from 'vue-router';
import { fileList } from '@/service/api/results';
import { CloudUploadOutline } from '@vicons/ionicons5'
const router = useRouter();

const loading = ref(true);
const showModal1 = ref(false);
const modelList = reactive([]);

// 排序方式选项
const generalAirportOptions: { label: string; value: number }[] = [
  { label: '拍摄时间', value: 1 },
  { label: '更新时间', value: 2 },
];

// 三维模型选项
const modelTypeOptions: { label: string; value: number }[] = [
  { label: '三维模型', value: 1 },
];

// const getValue = (e: any) => {
//   console.log('--------------', e);
//   formValue.dateRange = e;
// };

const handleLoad = () => {
  // modelList.value += 1
};
const onAfterLeave = () => {
  handleQueryClick();
}
const handleResetClick = () => {
  Object.assign(formValue, {
    sortBy: null, // 重置为 null
    fileName: '',
    fileType: null, // 重置为 null
    count: 0,
    dateRange: null,
    pageSize: 16,
    pageNum: 1,
  });
  handleQueryClick();
};

const goModel = (p0: any) => {
  router.push({ path: '/achievement/3dmaterialDetail', query: p0 });
};

const handleQueryClick = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
    getAirLineList();
  }, 1000);
};

const airLineList = ref<Api.threeMaterial.ModleItem[]>([]);

let formValue = reactive<{
  fileName: string;
  fileType: number | null; // fileType 改为 number | null
  dateRange: [number, number] | null;
  pageSize: number;
  pageNum: number;
  count: number;
  sortBy: number | null; // sortBy 改为 number | null
}>({
  sortBy: null, // 初始值为 null
  fileName: '',
  fileType: null, // 初始值为 null
  count: 0,
  dateRange: null,
  pageSize: 16,
  pageNum: 1,
});

// 获取航线列表
const getAirLineList = async () => {
  airLineList.value = []
  loading.value = true;
  const { fileName, fileType, dateRange, pageSize, sortBy, pageNum } = formValue;
  const json = {
    sortBy, // 直接传递数值
    fileName,
    fileType, // 直接传递数值
    startDate: dateRange && dateRange[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : null,
    endDate: dateRange && dateRange[1] ? dayjs(dateRange[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null,
    pageSize,
    pageNum,
  };
  const { data, error } = await fileList(json);
  if (!error) {
    formValue.count = data.total;
    airLineList.value = [...airLineList.value, ...data.rows]; // 追加数据
    for (let i = 0; i < airLineList.value.length; i++) {
      if (!airLineList.value[i].flightImage) {
        airLineList.value[i].flightImage = `${localStorage.getItem('newCoverBase64')}`;
        localStorage.removeItem('newCoverBase64');
        break;
      }
    }
  }
  loading.value = false;
};

onMounted(() => {
  getAirLineList();
});

setTimeout(() => {
  loading.value = false;
}, 1000);
</script>

<style scoped>
.n-spin-container {
  min-height: 200px;
}

.n-empty {
  margin: 32px 0;
}
</style>