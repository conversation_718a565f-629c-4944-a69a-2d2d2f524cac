<template>
  <!-- 航点动作列表 -->
  <WaypointActionList
    :task-name="airportDockItem.host?.taskName"
    :flight-point-action-list="flightPointActionDTOList"
    :sort="sort"
  />



  <!-- 调试信息 -->
  <div v-if="true" style="position: fixed; top: 110px; left: 110px; background: black; color: white; padding: 10px; z-index: 9999; max-width: 500px; word-break: break-all; font-size: 12px;">
    <p><strong>航点总数:</strong> {{ flightPointDTOList.length }}</p>
    <p><strong>当前航点序号:</strong> {{ sort }}</p>
    <p><strong>当前航点动作数量:</strong> {{ flightPointActionDTOList.length }}</p>
    <p><strong>动作列表:</strong> {{ JSON.stringify(flightPointActionDTOList.map(a => a.actionActuatorFuncDesc)) }}</p>
    <p><strong>无人机位置:</strong> {{ airportDroneItem.host?.latitude }}, {{ airportDroneItem.host?.longitude }}</p>
  </div>

  <!-- AI实时识别图像 -->
  <AIImageRecognition v-if="msgShow || imageList.length > 0" :image-list="imageList" :class-data="classData"
    :is-loading="isScreenshotLoading" />

  <div class=" color-light absolute left-[calc(50%+5vw)] bottom-1">

    <div v-show="boxStatus" class="flex w-max">

      <div class="relative h-max">

        <div class="absolute right--1 top-1 translate-x-1/1">
          <!-- <div class="flex justify-between mb-5px"> -->
          <div class="mb-5px text-left">
            <n-text class="color-light pr-5px fw-bold">{{ airportDroneItem.host?.nickname || droneName }}
            </n-text>
            <n-tag class="mr-10px" size="small" :type="droneStatus?.listClass || 'default'">
              {{ droneStatus?.dictLabel || '离线' }}
            </n-tag>
          </div>
          <!-- <n-iconclass="cursor-pointer" size="20" color="#ffffff"
              :component="ChevronBackCircleOutline" /> -->
          <!-- </div> -->

          <div class="flex justify-between pt-5px ml--5px">
            <div>
              <n-tag :bordered="false" size="small"
                :color="{ color: '#272727', textColor: '#555', borderColor: '#555' }">
                <div @click="showCarAIMessage" class="flex items-center text-light">
                  <SvgIcon icon="mdi:satellite-variant" class="text-icon text-#fff cursor-pointer" />
                  <n-text class="px-5px text-light">{{ airportDroneItem.host?.gpsNumber || 0 }}</n-text>
                  <SvgIcon icon="mdi:signal-cellular-outline" class="text-icon mx-5px text-#fff cursor-pointer" />
                  <n-text class="pr-5px text-light"> RTK {{ airportDroneItem.host?.rtkNumber || 0 }} FIX</n-text>
                </div>
              </n-tag>
            </div>
            <div class="w-max text-right">
              <n-button :disabled="!(airportDockItem.host?.modeCode === '0')" @click="onClickToTakeOff" size="small"
                type="warning" class="mr-10px">一键起飞 </n-button>
              <n-button v-show="currentScreenStatus != 2"
                :disabled="!(airportDockItem.host?.modeCode === '0' || airportDockItem.host?.modeCode === '2')"
                @click="handelRemoteDebug" :secondary="debugBoxStatus" size="small" type="info">
                {{ debugBoxStatus ? '关闭调试' : '远程调试' }}
              </n-button>
            </div>
          </div>
        </div>
        <!-- 视频窗口 -->
        <div>
          <div v-show="currentScreenStatus === 2"
            class="text-light relative lh-234px h-234px w-416px text-center bg-dark-theme">正在大窗播放</div>
          <div v-show="currentScreenStatus === 0"
            class="text-light absolute top-0 left-0 lh-234px h-234px w-416px text-center bg-dark-theme zindex-1">
            设备暂未开始直播</div>
          <div id='video-container' :class="[
            'bg-dark-theme overflow-hidden',
            currentScreenStatus === 2
              ? 'fixed top-55px left-95px h-720px w-1280px z-100'
              : 'h-234px w-416px aspect-video relative'
          ]">

            <video id="player-container-3" class="w-full h-full object-contain" preload="auto" muted playsinline
              webkit-playsinline></video>
            <canvas ref="canvasRef" v-show="canvasShow" id="canvas-container"
              class="absolute top-0 left-0 w-full h-full" width="1280" height="720"></canvas>
            <div v-show="currentScreenStatus === 2"
              class="overflow-hidden fixed top-55px left-95px h-720px w-1280px z-100">
              <div class="p-5px bg-black absolute right-0 bottom-0 cursor-pointer" @click="handelVideoScreen">
                <SvgIcon class="cursor-pointer text-lg text-gray hover:text-gray-300 transition-colors duration-200"
                  size="24" icon="mdi:arrow-bottom-right-thick" />
              </div>
              <div @click="refreshStart" class="p-5px zindex-2 bg-black bg-opacity-50 top-0 right-0 w-max absolute">
                <p>{{ currentDate }}</p>
                <p>{{ currentTime }}</p>
              </div>
            </div>
            <div
              v-show="currentScreenStatus === 0 && currentScreenStatusErr === 2 && droneStatus?.dictLabel != '离线' && droneStatus?.dictLabel"
              class="overflow-hidden fixed top-55px left-95px h-720px w-1280px z-100">
              <div @click="refreshStart" class="p-5px zindex-2 bg-black bg-opacity-50 top-0 right-0 w-max absolute">
                <SvgIcon icon="humbleicons:refresh"
                  class="cursor-pointer text-lg text-gray hover:text-gray-300 transition-colors duration-200"
                  size="24" />
              </div>
            </div>

            <div v-show="currentScreenStatus === 1">
              <div class="p-5px bg-black absolute zindex-2 bg-opacity-50 left-0 top-0 cursor-pointer"
                @click="handelVideoScreen">
                <SvgIcon class="cursor-pointer text-lg text-gray hover:text-gray-300 transition-colors duration-200"
                  size="24" icon="mdi:arrow-top-left-thick" />
              </div>
              <div @click="refreshStart" class="p-5px zindex-2 bg-black bg-opacity-50 top-0 right-0 w-max absolute">
                <p>{{ currentDate }}</p>
                <p>{{ currentTime }}</p>
              </div>
            </div>
            <div
              v-show="currentScreenStatus === 0 && currentScreenStatusErr === 1 && droneStatus?.dictLabel != '离线' && droneStatus?.dictLabel">
              <div @click="refreshStart" class="p-5px zindex-2 bg-black bg-opacity-50 top-0 right-0 w-max absolute">
                <SvgIcon icon="humbleicons:refresh"
                  class="cursor-pointer text-lg text-gray hover:text-gray-300 transition-colors duration-200"
                  size="24" />
              </div>
            </div>
          </div>

        </div>

        <!-- 环境数据 -->
        <div class="absolute w-90% right--2 bottom-0 translate-x-1/1">

          <div class="pb-20px w-100%">
            <div class="flex justify-between-left">
              <n-text class="text-4 text-light lh-30px font-mono pr-80px">纬度：
                {{ Number(airportDroneItem.host?.latitude || 0).toFixed(6) }}</n-text>
              <n-text class="text-4 text-light lh-30px font-mono pl--5px">经度：
                {{ Number(airportDroneItem.host?.longitude || 0).toFixed(6) }}</n-text>
            </div>
            <div class="flex justify-between-left">
              <n-text class="text-4 text-light lh-30px font-mono pr-15px">海拔高度：
                {{ Number(airportDroneItem.host?.height || 0).toFixed(1) }}（m）</n-text>
            </div>
            <div class="flex justify-between-left">
              <n-text class="text-4 text-light lh-30px font-mono pr-15px">相对高度：
                {{ Number(airportDroneItem.host?.elevation || 0).toFixed(1) }}（m）</n-text>
              <n-text class="text-4 text-light lh-30px font-mono pl-5px">直线距离：
                {{ Number(airportDroneItem.host?.homeDistance || 0).toFixed(1) }}（m）</n-text>
            </div>
            <div class="flex justify-between-left">

              <n-text class="text-4 text-light lh-30px font-mono pr-15px ">风速：
                {{ Number(airportDroneItem.host?.windSpeed || 0).toFixed(1) }} （m/s）</n-text>
              <n-text class="text-4 text-light lh-30px font-mono ">升降速度：
                {{ Number(airportDroneItem.host?.verticalSpeed || 0).toFixed(1) }} （m/s）</n-text>
            </div>
          </div>

          <!-- 演示告警弹窗 -->
          <!-- @click="onScreenshotModel" @click="onScreenshotModelRain"-->
          <n-flex justify="space-between" class="w-100%">
            <n-text class="color-light">地面风速： <n-text class="color-light font-mono text-14px">{{
              airportDockItem.host?.windSpeed || 0 }} m/s</n-text></n-text>
            <n-text class="color-light">地面雨量： <n-text class="color-light font-mono text-14px">
                {{ airportDockItem.host?.rainfall === '1' ? '小' : airportDockItem.host?.rainfall === '2' ? '中' :
                  airportDockItem.host?.rainfall === '3' ? '大' : '无' }}
              </n-text></n-text>
            <n-text class="color-light">舱内温度： <n-text class="color-light font-mono text-14px ">
                {{ airportDockItem.host?.temperature || 0 }}℃</n-text></n-text>
          </n-flex>
        </div>

        <!-- 远程调试菜单 -->
        <div v-show="debugBoxStatus" class="fixed bottom-65 right-1 bg-dark-theme w-max">
          <div class="flex-y-center justify-between p-2  ">
            <n-text class="color-light text-4">远程调试 </n-text>
            <n-icon @click="handelRemoteDebug" class="cursor-pointer" size="20" color="#ffffff" :component="Close" />
          </div>
          <n-config-provider :theme="darkTheme">
            <div class="flex justify-around m-10px ">
              <!-- 机场 -->
              <div class="flex-1 p-15px bg-dark-3 rd mr-10px">
                <div class="flex-y-center justify-between">
                  <n-text>机场</n-text>
                  <n-button size="small" @click="updateStatus('dock', 'device_reboot', { 'action': 0 })">重启</n-button>
                  <n-button size="small" @click="updateStatus('dock', 'device_format', { 'action': 0 })">格式化</n-button>
                </div>
                <div class="mt-10px">
                  <div v-for="(item, index) in debugMenu.dock" :key="index"
                    class="flex-y-center justify-between m-y-5px bg-dark-1 p-x-2 p-y-5px rd">
                    <!-- <div v-if="item.name == '空调' && Number(airportDockItem.host?.airConditionerSwitchTime) > 5">
                    <n-text class="w-max pr-5px text-3">{{ item.name }}</n-text>
                  </div> -->
                    <div>
                      <n-text class="w-max pr-5px text-3">{{ item.name }}</n-text>
                    </div>

                    <span class="w-max " style="display:flex">

                      <n-text v-if="Number(airportDockItem.host?.airConditionerSwitchTime) > 5 && item.name == '空调'"
                        class="pr-5px text-3">{{ airportDockItem.host?.airConditionerSwitchTime }}</n-text>
                      <n-radio-group v-if="item.list.length <= 2"
                        @update:value="($event: string) => onChangeDebugItem('dock', $event, index)" :size="'small'"
                        v-model:value="item.status" name="radiobuttongroup1" class="w-max">
                        <n-radio-button v-for="litem in item.list" :key="litem.path" :value="litem.value"
                          :label="litem.label" />
                      </n-radio-group>
                      <n-select v-else @update:value="($event: string) => onChangeDebugItem('dock', $event, index)"
                        :disabled="item.name == '空调' && airCouint > 5 ? true : false" v-model:value="item.status"
                        :options="item.list" size="tiny" :consistent-menu-width="false" />
                    </span>
                  </div>
                </div>
              </div>
              <!-- 无人机 -->
              <div class="flex-1 p-15px bg-dark-2 rd">
                <div class="flex-y-center justify-between">
                  <n-text>飞行器</n-text>
                  <n-button size="small" @click="updateStatus('drone', 'drone_format', { 'action': 0 })">格式化</n-button>
                </div>
                <div class="mt-15px">
                  <div v-for="(item, index) in debugMenu.drone" :key="index"
                    class="flex-y-center justify-between m-y-5px bg-dark-1 p-x-10px p-y-5px rd">
                    <n-text class="w-max pr-10px text-3">{{ item.name }}</n-text>
                    <span class="w-max">
                      <n-radio-group v-if="item.list.length <= 2"
                        @update:value="($event: string) => onChangeDebugItem('drone', $event, index)" :size="'small'"
                        v-model:value="item.status" name="radiobuttongroup1">
                        <n-radio-button v-for="litem in item.list" :key="litem.path" :value="litem.value"
                          :label="litem.label" />
                      </n-radio-group>
                      <n-select v-else @update:value="($event: string) => onChangeDebugItem('drone', $event, index)"
                        v-model:value="item.status" :options="item.list" size="tiny" class="w-100px" />
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </n-config-provider>

        </div>
      </div>

      <div v-show="!boxStatus" @click="handelBoxChange"
        class="px-15px py-10px  border-rd bg-#1a1b1c text-center cursor-pointer ">
        <SvgIcon icon="mdi:quadcopter" class="text-32px  text-#fff mb-5px " />
        <n-text class="text-14px text-light">详情</n-text>
      </div>
      <n-modal v-model:show="showFireModal">
        <n-card size="huge" :bordered="false" class="w-1280px">
          <div class="flex-x-center justify-center pb-10px">
            <n-icon size="35" :component="Warning" color="red"></n-icon>
            <!--            <n-text class="font-size-24px">警告！检测到<n-text class="font-700">湖面漂浮物</n-text>！</n-text>-->
            <n-text class="font-size-24px">警告！疑似检测到<n-text class="font-700">墙面脱落</n-text>！</n-text>
          </div>
          <img :src="screenshotSrc" class="w-1280vw" alt="" srcset="">
        </n-card>
      </n-modal>

      <n-modal v-model:show="showRainModal">
        <n-card size="huge" :bordered="false" class="w-1280px">
          <div class="flex-x-center justify-center pb-10px">
            <n-icon size="35" :component="Warning" color="red"></n-icon>
            <n-text class="font-size-24px">警告！检测到<n-text class="font-700">垃圾违规倾倒</n-text>！</n-text>
          </div>
          <img :src="screenshotSrcRain" class="w-1280vw" alt="" srcset="">
        </n-card>
      </n-modal>

    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, h, inject, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { LocationQueryValue, useRoute, useRouter } from 'vue-router';
import type { Component, Ref } from 'vue'
import { Vue3Marquee } from 'vue3-marquee'
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { ChevronBackCircleOutline, Close, Warning, List, AirplaneOutline, BatteryHalfOutline, CloudUpload } from '@vicons/ionicons5'
import { useAirportStore } from '@/store/modules/airport';
import { MessageRenderMessage, NAlert, NIcon, useMessage, useModal, useDialog } from 'naive-ui';
import { darkTheme } from 'naive-ui';
import { io } from 'socket.io-client';
import { getServiceBaseURL } from '@/utils/service';
import VerticalMarquee from './VerticalMarquee.vue';
import AIImageRecognition from './AIImageRecognition.vue';
import WaypointActionList from './WaypointActionList.vue';
import {
  closeRemoteDebug, fetchDebugMenu, fetchDebugStatus, fetchLivestreamInfo, openRemoteDebug, returnHome,
  startAILivestream, stopAILivestream, startLivestream, imgUpload, stopLivestream, takeOffToPoint, triggerRemoteDebugItem, getJobById, uploadFileInfo, uploadBase64Common
} from '@/service/api';
import { useDeviceStore } from '@/store/modules/device';
import flvjs from 'flv.js';
import html2canvas from 'html2canvas';
import TCPlayer, { type TCPlayerConfig } from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
import { useAIServerStore } from '@/store/modules/ai';
import { log } from 'console';
interface ScreenshotImage {
  url: string;
  id: number;
  time: string;
}
interface aiItem {
  机动车: number;
  单非机动车: number;
  多非机动车: number
}
const emits = defineEmits(['cancel']);
const imageList = ref<ScreenshotImage[]>([]); // 明确指定数组元素类型
const itemsKey = ref(0);
defineOptions({ name: 'DroneVideo' });
const currentDate = ref("")
const currentTime = ref("");
const airportStore = useAirportStore();
const deviceStore = useDeviceStore();
const { routerPushByKey } = useRouterPush();
const message = useMessage();
const aiServerStore = useAIServerStore();
// const modal = useModal();

// 判断是否使用 HTTP 代理
const isHttpProxy = false;
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

const route = useRoute();
const router = useRouter();
const dialog = useDialog();
// 连接WebSocket服务器
const socket = inject<{ chatMessage: Ref<string> }>('useSocket');  // 通过 inject 获取 provide 的 socket 对象
const detectSocket = io(otherBaseURL.pserver, { query: { dsn: route.query.dsn } }); // 连接检测的WebSocket服务器
const classData = ref<aiItem[]>([]);
// 定义组件接受的属性
// const props = defineProps<{
//   // airportVideoItem: Api.Airport.AirportDeviceInfo;
// }>();

const flyInfoDockSN = ref();
const flyInfoDroneSN = ref();
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});
const airCouint = ref(-1);
const droneName = ref();

// 可以开启AI检测的modeCode列表
const CanHandelAIModeList = ['3', '5', '7', '16', '17'];
const AIModeList = ['3', '5', '6', '7', '8', '9', '10', '11', '12', '16', '17'];


const boxStatus = ref(true); // 整个窗口状态： false-小窗口 true-大窗口
const debugBoxStatus = ref(false); // debug窗口状态 false-关闭 true-打开

const debugMenu = ref<Api.Airport.DebugMenu>({ dock: [], drone: [] }); // 远程调试菜单

const playerState = reactive({
  instance: null as InstanceType<typeof TCPlayer> | null,
  config: {
    licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1329835514_1/v_cube.license',
    ProgressMarker: false,
    connectRetryCount: 3,
    showLog: true,
    muted: true,
    controls: false,
    autoplay: true,
    width: '416px',  // 初始宽度
    height: '234px', // 初始高度
    controlBar: {
      fullscreenToggle: false, // 不需要全屏按钮
      playToggle: false,
      volumePanel: false,
      timeDisplay: false,
    },
    webrtcConfig: {
      connectRetryCount: 3,
      connectRetryDelay: 3,
      receiveAudio: false,
      receiveSEI: false,
      showLog: true
    }
  }
});

// 当前窗口播放状态 0-未播放 1-小屏 2-全屏
const currentScreenStatus = ref(0);
const currentScreenStatusErr = ref(1);

// 获取无人机状态
const droneStatus = computed(() => {
  return deviceStore.droneStatusList[Number(airportDroneItem.value.host?.modeCode)];
});

// 大/小窗口切换事件
function handelBoxChange() {
  boxStatus.value = !boxStatus.value;
}

function refreshStart() {
  onStreamStart()
}
const scaleRatio = ref(1); // 缩放比例，默认1（全屏）
// 控制小屏/大屏播放
function handelVideoScreen() {
  const videoContainer = document.querySelector('#video-container');
  const canvas = canvasRef.value;
  if (!videoContainer || !canvas) return;
  if (currentScreenStatus.value === 1) { // 小窗口状态
    // 计算小窗口宽度与原始宽度的比例
    scaleRatio.value = 416 / 1280; // 假设小窗口宽度固定为416px
  } else { // 全屏状态
    scaleRatio.value = 1;
  }
  if (currentScreenStatus.value === 1) { // 切换到大窗口
    videoContainer.className = 'fixed top-55px left-95px h-720px w-1280px z-100';
    canvas.style.transform = ''; // 重置transform
    canvas.style.width = '1280px';
    canvas.style.height = '720px';
    currentScreenStatus.value = 2;
    currentScreenStatusErr.value = 2
    debugBoxStatus.value = false;
  } else { // 切换回小窗口
    videoContainer.className = 'h-234px w-416px aspect-video relative';

    // 计算缩放比例：416/1280 ≈ 0.325
    const scale = 416 / 1280;

    // 设置canvas样式
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';
    canvas.style.width = '1280px';
    canvas.style.height = '720px';
    canvas.style.transform = `scale(${scale})`;
    canvas.style.transformOrigin = '0 0';
    currentScreenStatus.value = 1;
    currentScreenStatusErr.value = 1
    if (airportDockItem.value.host?.modeCode === '2') {
      debugBoxStatus.value = true;
    }
  }
}

// 一键起飞
async function onClickToTakeOff() {
  const { error } = await takeOffToPoint(flyInfoDockSN.value || '', { targetHeight: 120 });
  if (!error) {
    message.success('一键起飞任务已下发，正在自检，请等候设备响应');
  }
}

// 查询机场当前状态（用于恢复设备状态）
// TODO: "非离线状态，则开启直播"有兼容性问题
async function queryDebugStatus() {
  const { data } = await fetchDebugStatus(flyInfoDockSN.value);
  deviceStore.setDockInfo(data); // 保存到store
  if (data.modeCode === '2') {// 调试中
    getDebugMenu();
    debugBoxStatus.value = true;
  }
  droneName.value = data.droneName || '';
  if (data.droneModeCode !== 'null') { // 非离线状态，则开启直播
    setTimeout(() => {
      onStreamStart();
    }, 2000);
  }
}

// 控制远程调试的开启与关闭 debugBoxStatus.value: true-当前为开启状态，则关闭 false-当前为关闭状态，则开启
async function handelRemoteDebug() {
  let error = null;
  if (!debugBoxStatus.value) { // 打开调试
    error = (await openRemoteDebug(flyInfoDockSN.value)).error;
    if (error) return;
    getDebugMenu();
    debugBoxStatus.value = true;
    message.success('远程调试模式已开启')
  } else { // 关闭调试
    error = (await closeRemoteDebug(flyInfoDockSN.value)).error;
    debugBoxStatus.value = false;
    message.success('远程调试模式已关闭')
  }
}

// 添加追帧相关的状态
const catchUpInterval = ref<number | null>(null);
const rtmpUrl = ref('');

// 视频相关设置
const videoWidth = ref(240); // 默认小窗口宽度
const videoHeight = ref(180); // 默认小窗口高度
const canvasWidth = ref(videoWidth.value); // canvas宽度
const canvasHeight = ref(videoHeight.value); // canvas高度

const error_webrtc = ref([1004, 1005, 1006]);

// 开始播放
const onStreamStart = async () => {
  getAIClssIDList();
  const { data } = await startLivestream({ sn: flyInfoDroneSN.value });
  if (!data || !data.url) {
    return;
  }

  if (!playerState.instance) {
    // 给DOM一个短暂的时间重置
    await new Promise(resolve => { setTimeout(resolve, 100) });

    // 创建新的播放器实例
    playerState.instance = new TCPlayer('player-container-3', {
      ...playerState.config,
      sources: [{
        src: data.url,
      }],
    });
    currentScreenStatus.value = 1; // 设置为小窗口播放状态

    // 初始化canvas缩放
    await nextTick(() => {
      const canvas = canvasRef.value;
      if (canvas) {
        const scale = 416 / 1280;
        canvas.style.position = 'absolute';
        canvas.style.top = '0';
        canvas.style.left = '0';
        canvas.style.width = '1280px';
        canvas.style.height = '720px';
        canvas.style.transform = `scale(${scale})`;
        canvas.style.transformOrigin = '0 0';
      }
    });
  }

  // 监听直播异常
  const errorCount = ref(0);
  playerState.instance.on('webrtcevent', (event: any) => {
    console.log('直播监听', event)
    console.log('直播状态：', event.data.code)
    console.log('直播异常码：', error_webrtc.value, error_webrtc.value.includes(event.data.code))
    errorCount.value++;
    if (error_webrtc.value.includes(event.data.code) && errorCount.value >= playerState.config.webrtcConfig.connectRetryCount) {
      console.log('直播异常，停止播放')
      isOpenTag.value = false; // 关闭标签
      onStreamStop();
    }
  });

  // 获取视频宽高
  playerState.instance.on('loadedmetadata', () => {
    if (playerState.instance) {
      videoWidth.value = playerState.instance.videoWidth();
      videoHeight.value = playerState.instance.videoHeight();
      console.log('drone playerState.instance.videoWidth(): ', playerState.instance.videoWidth());
      console.log('drone playerState.instance.videoHeight(): ', playerState.instance.videoHeight());
    }
  });
  rtmpUrl.value = data.rtmpUrl;
  await onDetectSocketServer(rtmpUrl.value); // 开始绘制AI识别
}

// 停止播放
const onStreamStop = async () => {
  console.log('onStreamStop: ', playerState.instance)
  if (playerState.instance) {
    playerState.instance.dispose(); // 销毁播放器实例
    playerState.instance = null;

    await stopLivestream({ sn: flyInfoDroneSN.value });

    // 获取视频容器
    const container = document.querySelector('#video-container');
    if (container) {
      // 创建新的 video 元素
      const newVideo = document.createElement('video');
      newVideo.id = 'player-container-3';
      newVideo.className = 'object-fill w-100% h-100% pt-0';
      newVideo.setAttribute('preload', 'auto');
      newVideo.setAttribute('muted', '');
      newVideo.setAttribute('playsinline', '');
      newVideo.setAttribute('webkit-playsinline', '');

      // 插入到容器的开始位置
      container.insertBefore(newVideo, container.firstChild);
    }
    currentScreenStatus.value = 0; // 恢复到小窗口-未播放状态
  }
  await stopAILivestream({ input_stream: rtmpUrl.value });
}

async function updateStatus(type: 'dock' | 'drone', path: string, param: object) {
  var meg = "重启";
  if (path == 'device_reboot') {
    meg = "重启";
  } else if (path == 'device_format' || path == 'drone_format') {
    meg = "格式化";
  }
  console.log('asd');
  var deviceName;

  if (type == 'dock') {
    deviceName = airportDockItem.value.host?.nickname;
  } else {
    deviceName = '无人机';
  }

  dialog.warning({
    title: '警告',
    content: `确定要 ${meg} ${deviceName} 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const { error } = await triggerRemoteDebugItem(flyInfoDockSN.value, path, param);
      if (!error) {
        message.success('操作成功');
      } else {
        message.error(error.message)
      }
    }
  });


}
// 修改机场/无人机调试项
async function onChangeDebugItem(type: 'dock' | 'drone', value: string, index: number) {
  const cdebugMenu = JSON.parse(JSON.stringify(debugMenu.value));
  const item = debugMenu.value[type][index];
  const foundItem = item.list.find(item => item.value === value);

  if (foundItem) {
    const { param, path } = foundItem;
    const { error } = await triggerRemoteDebugItem(flyInfoDockSN.value, path, param);
    if (!error) {
      message.success("操作成功，请留意设备变化");
    } else { // 请求失败则还原选项
      debugMenu.value = cdebugMenu;
    }
  }
}

// 获取调试菜单
async function getDebugMenu() {
  const { data, error } = await fetchDebugMenu(flyInfoDockSN.value);
  debugMenu.value = data;
}

const isOpenTag = ref(false); // 无人机是否开机，控制视频播放及上线消息提醒
const snTag = ref(''); // 无人机是否开机，控制视频播放及上线消息提醒
// 处理socket消息
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  if (biz_code === 'device_osd' && flyInfoDroneSN.value === sn) { // 本次任务无人机消息
    airportDroneItem.value = data;
  } else if (biz_code === 'dock_osd' && flyInfoDockSN.value === sn) { // 本次任务机场消息
    airportDockItem.value = data
    // taskId.value = airportDockItem.value.host?.taskId || ''
    airCouint.value = Number(airportDockItem.value.host?.airConditionerSwitchTime);
  } else if (biz_code === 'device_hms') { // 设备告警
  } else if (biz_code === 'device_status' && sn === flyInfoDroneSN.value && data.host.status) { // 无人机上线
    console.log('上线 isOpenTag.value: ', isOpenTag.value);
    if (!isOpenTag.value) {
      imageList.value = []
      message.info(`设备 ${sn} 已上线`);
      onStreamStart(); // 开启直播
      isOpenTag.value = true; // 开启标签
    }

  } else if (biz_code === 'device_status' && sn === flyInfoDroneSN.value && !data.host.status) { // 无人机下线
    console.log('下线 isOpenTag.value: ', isOpenTag.value);
    if (isOpenTag.value) {
      imageList.value = []
      message.warning(`设备 ${sn} 已离线`);
      isOpenTag.value = false; // 关闭标签
    }
    onStreamStop();
  }
}

// 监听Socket消息
watch(() => socket?.chatMessage?.value, (msg) => {
  // 处理接收到的消息
  onProcessMessage(msg);
});
const flightPointDTOList = ref<Api.AirLine.FlightPoint[]>([]);
const flightPointActionDTOList = ref<Api.AirLine.FlightPointAction[]>([]);
const isScreenshotLoading = ref(false)

// 调试：监听flightPointActionDTOList变化
watch(flightPointActionDTOList, (newVal) => {
  console.log('drone-video - flightPointActionDTOList changed:', newVal);
  console.log('drone-video - flightPointActionDTOList length:', newVal?.length);
  if (newVal && newVal.length > 0) {
    console.log('动作描述:', newVal.map(a => a.actionActuatorFuncDesc));
  }
}, { deep: true, immediate: true });
// 监听值变化
watch(() => airportDockItem.value.host?.taskId, (msg, old) => {
  // console.log('airportDockItem.value.host?.taskId', msg)
  if (msg != '' && msg != undefined && airportDockItem.value.host?.taskName != '手动飞行') {
    flightPointActionDTOList.value = []
    getJobById(msg).then((res) => {
      if (res.data) {
        flightPointDTOList.value = res.data.flightPointDTOList
      }
    })
  }
});
// 定义 Haversine 公式计算距离的函数
function haversineDistance(lat1: number, lon1: number, lat2: number, lon2: number) {
  const R = 6371e3; // 地球半径，单位：米
  const φ1 = lat1 * (Math.PI / 180);
  const φ2 = lat2 * (Math.PI / 180);
  const Δφ = (lat2 - lat1) * (Math.PI / 180);
  const Δλ = (lon2 - lon1) * (Math.PI / 180);

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

  return R * c;
}
const sort = ref(0)
// 初始化已处理元素的索引
let processedIndex = 0;

watch(
  () => airportDroneItem.value.host?.latitude,
  (newUser) => {
    if (flightPointDTOList.value && Number(localStorage.getItem('processedIndex')) < flightPointDTOList.value.length) {
      const currentPoint = flightPointDTOList.value[Number(localStorage.getItem('processedIndex'))];
      const pointLat = currentPoint.geoJson.coordinates.latitude;
      const pointLon = currentPoint.geoJson.coordinates.longitude;
      const hostLat = Number(airportDroneItem.value.host?.latitude);
      const hostLon = Number(airportDroneItem.value.host?.longitude);
      if (hostLat !== undefined && hostLon !== undefined) {
        const distance = haversineDistance(hostLat, hostLon, pointLat, pointLon);
        if (distance <= 10) {
          console.log('找到匹配航点:', currentPoint);
          console.log('航点动作列表:', currentPoint.flightPointActionDTOList);

          // 更新当前航点的动作列表
          flightPointActionDTOList.value = currentPoint.flightPointActionDTOList || [];
          sort.value = currentPoint.sort + 1
          if (!localStorage.getItem('processedIndex')) {
            processedIndex++
            localStorage.setItem('processedIndex', processedIndex + '')
          } else {
            processedIndex = Number(localStorage.getItem('processedIndex')) + 1
            localStorage.setItem('processedIndex', Number(localStorage.getItem('processedIndex')) + 1 + '')
          }
          localStorage.setItem('processedIndex', processedIndex + '')
        }
      }
    }

    if (airportDroneItem.value.host?.modeCode == '9' && Number(localStorage.getItem('processedIndex')) >= flightPointDTOList.value.length) {
      flightPointActionDTOList.value = []
    }
  },
  { deep: true }
);
// 添加 canvas 引用
const canvasRef = ref<HTMLCanvasElement | null>(null);
// 是否开启canvas画布
const canvasShow = ref(false)
const msgShow = ref(false)
const aiSubType = ref('')
const eventName = ref('')
// 监听检测结果并绘制，接收rtmpUrl
async function onDetectSocketServer(url: string) {
  try {
    await startAILivestream({ input_stream: url, class_ids: aiClassList.value, dsn: route.query.dsn });
  } catch (error) {
    console.log('视频流报错：', error)
  }
  detectSocket.on('detection_result', (data) => {
    if (!canvasShow.value) {
      stopScreenshotTimer(); // 新增：停止定时截图
      return
    }
    const canvas = canvasRef.value;
    if (!canvas) return;
    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // 设置设备像素比
    const dpr = window.devicePixelRatio || 1;

    // 获取原始尺寸
    const originalWidth = data.img_size.width;
    const originalHeight = data.img_size.height;

    // 计算目标显示区域的宽高比（16:9）
    const targetRatio = 1280 / 720;

    // 计算原始视频的宽高比
    const originalRatio = originalWidth / originalHeight;

    // 判断视频类型：基于宽高比而非绝对尺寸
    // 1. 如果原视频比例更接近4:3, 则居中显示并保持原比例
    // 2. 如果原视频比例接近16:9, 则拉伸填充
    const ratio43 = 4 / 3; // 4:3宽高比
    const needsCentering = Math.abs(originalRatio - ratio43) < Math.abs(originalRatio - targetRatio);
    // 计算视频在720高度下的缩放宽度
    const scaledWidth = needsCentering ? Math.round(originalHeight * (originalWidth / originalHeight)) : 1280;

    // 计算居中偏移量
    const offsetX = needsCentering ? Math.floor((1280 - scaledWidth) / 2) : 0;

    // 设置canvas尺寸（始终使用容器宽度1280）
    canvas.width = 1280 * dpr;
    canvas.height = 720 * dpr;

    // 设置canvas的CSS显示尺寸
    canvas.style.width = '1280px';
    canvas.style.height = '720px';
    canvas.style.position = 'absolute';
    canvas.style.top = '0';
    canvas.style.left = '0';

    // 根据DPR缩放上下文
    ctx.scale(dpr, dpr);

    // 清空Canvas
    ctx.clearRect(0, 0, 1280, 720);

    // 保存上下文状态
    ctx.save();

    // 如果需要居中，应用水平偏移
    if (needsCentering) {
      ctx.translate(offsetX, 0);
    }

    // 过滤掉已关闭的AI检测类别
    const filteredObjects = data.objects.filter((obj: any) => {
      const aiItem = aiServerStore.aiList.find(item => item.aiType === obj.class_id);
      return aiItem ? aiItem.status : true; // 如果在aiList中找不到对应项，默认显示
    });

    // 绘制检测框
    filteredObjects.forEach((obj: any) => {
      const vertices = obj.vertices_percent;
      const classId = obj.class_id;

      // 从 all_classes 中获取对应的 class name
      const classType = data.all_classes.find((cls: any) => cls.class_id === classId)?.name || 'UNKNOWN';

      // 从 all_classes 中获取对应的 color
      const classColor = data.all_classes.find((cls: any) => cls.class_id === classId)?.color || '#ADD8E6';

      // 设置线条属性
      ctx.strokeStyle = classColor;
      ctx.lineWidth = 1; // 线条

      // 关闭抗锯齿以获得更清晰的线条
      ctx.imageSmoothingEnabled = false;

      // 根据是否需要居中计算点位置
      const points = vertices.map((vertex: [number, number]) => {
        if (needsCentering) {
          // 如果是居中显示，使用缩放后的视频尺寸计算
          return [
            (vertex[0] / 100) * scaledWidth,
            (vertex[1] / 100) * 720
          ];
        } else {
          // 如果是拉伸填充，使用canvas尺寸计算
          return [
            (vertex[0] / 100) * 1280,
            (vertex[1] / 100) * 720
          ];
        }
      });

      // 绘制检测框
      const drawPartialLine = (
        ctx: CanvasRenderingContext2D,
        startX: number,
        startY: number,
        endX: number,
        endY: number
      ) => {
        const deltaX = endX - startX;
        const deltaY = endY - startY;
        const ratio = 0.3; // 30%的长度

        // 计算终点
        const partialX = startX + (deltaX * ratio);
        const partialY = startY + (deltaY * ratio);

        ctx.beginPath();
        ctx.moveTo(startX, startY);
        ctx.lineTo(partialX, partialY);
        ctx.stroke();
      };

      // 设置检测框的样式
      ctx.strokeStyle = classColor;
      ctx.lineWidth = 2;

      // 绘制四个角
      for (let i = 0; i < 4; i++) {
        const currentPoint = points[i];
        const nextPoint = points[(i + 1) % 4];
        const prevPoint = points[(i + 3) % 4];

        // 向下一个点方向绘制
        drawPartialLine(
          ctx,
          currentPoint[0],
          currentPoint[1],
          nextPoint[0],
          nextPoint[1]
        );

        // 向上一个点方向绘制
        drawPartialLine(
          ctx,
          currentPoint[0],
          currentPoint[1],
          prevPoint[0],
          prevPoint[1]
        );
      }

      // 绘制标签
      const padding = 4; // 增加内边距
      const textWidth = ctx.measureText(classType).width;
      const textHeight = 12; // 调整文本高度

      // 保存当前上下文状态
      ctx.save();

      // 移动到左上角点，并向上偏移标签高度
      ctx.translate(points[0][0], points[0][1] - (textHeight + padding * 2));

      // 设置背景色（80%透明度）
      const backgroundColor = 'rgba(0, 0, 0, 0.6)';
      ctx.fillStyle = backgroundColor;

      // 绘制背景
      ctx.fillRect(
        0, 0,
        textWidth + padding * 2, // 背景宽度 = 文本宽度 + 两侧内边距
        textHeight + padding * 2  // 背景高度 = 文本高度 + 上下内边距
      );

      // 绘制文本
      ctx.fillStyle = classColor;
      ctx.font = '12px Arial'; // 调整字体大小
      ctx.textBaseline = 'middle';
      ctx.textAlign = 'center';
      ctx.fillText(
        classType,
        (textWidth + padding * 2) / 2, // 文本水平居中
        (textHeight + padding * 2) / 2  // 文本垂直居中
      );
      // 恢复上下文状态
      ctx.restore();
    });
    if (data.total_objects != 0 && filteredObjects.length != 0 && msgShow.value
    ) {
      let value = data.class_counts
      const objString = `机动车: ${data.class_counts.机动车 || 0}, 单非机动车: ${data.class_counts.单非机动车 || 0}, 多非机动车: ${data.class_counts.多非机动车 || 0}`;
      eventName.value = objString
      startScreenshotTimer(value, data.timestamp);
    }
    // 恢复上下文状态
    ctx.restore();
  });
}

// 在其他 ref 变量附近添加
const showRainModal = ref(false)
const screenshotSrcRain = ref('')

const showFireModal = ref(false)
const screenshotSrc = ref('')

// 添加新的截图函数
function onScreenshotModel() {
  // 获取视频元素 - 需要获取实际的 video 元素
  const videoContainer = document.querySelector('#video-container');
  const videoElement = videoContainer?.querySelector('video') as HTMLVideoElement;

  if (videoElement && videoElement.videoWidth && videoElement.videoHeight) {
    // 创建一个 canvas 元素
    const canvas = document.createElement('canvas');
    canvas.width = videoElement.videoWidth;
    canvas.height = videoElement.videoHeight;

    // 获取 canvas 的 2D 上下文
    const context = canvas.getContext('2d');
    if (context) {
      try {
        // 绘制视频的当前帧到 canvas
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

        // 将 canvas 的内容转换为 Base64 格式的图像数据 URL
        screenshotSrc.value = canvas.toDataURL('image/png');
        showFireModal.value = !showFireModal.value;
      } catch (error) {
        console.error('Screenshot failed:', error);
        // message.error('截图失败，请确保视频正在播放');
      }
    }
  } else {
    console.error('Video element not found or video dimensions not available');
    // message.error('无法获取视频元素或视频尺寸');
  }
}

const renderMessage: MessageRenderMessage = (props) => {
  const { type } = props
  return h(
    NAlert,
    {
      closable: props.closable,
      onClose: props.onClose,
      type: type === 'loading' ? 'default' : type,
      title: '警告！此处有机动车及非机动车违停！',
      style: {
        boxShadow: 'var(--n-box-shadow)',
        maxWidth: 'calc(100vw - 32px)',
        margin: '5vw auto',
        width: '480px'
      }
    },
    {
      default: () => props.content
    }
  )
}
function showCarAIMessage() {
  message.warning('', {
    render: renderMessage,
    closable: true,
    duration: 60000
  })
}

// 监听 airportDroneItem 的 modeCode
watch(() => airportDroneItem.value.host?.modeCode, (newModeCode, oldModeCode) => {
  // 根据modeCode列表判断是否开启canvas
  canvasShow.value = Boolean(newModeCode && CanHandelAIModeList.includes(newModeCode));
  msgShow.value = Boolean(newModeCode && AIModeList.includes(newModeCode));
  if (!msgShow.value) {
    stopScreenshotTimer(); // 新增：停止定时截图
  }
  console.log('ztai', newModeCode, oldModeCode)
  if (newModeCode == '0' && oldModeCode == '10' && flyInfoDockSN.value == route.query.sn) {
    // imageList.value = []
    emits('cancel')
  }
  if (newModeCode == '4' && flyInfoDockSN.value == route.query.sn) {
    imageList.value = []
  }
  // onDetectSocketServer(rtmpUrl.value); // 调用 onDetectSocketServer 函数
});

const faceLoading = ref(false);
const showFaceModal = ref(false);
const faceCaptureImg = ref('');

function handleFaceCapture() {
  faceLoading.value = true;
  // 复用截图逻辑
  const videoContainer = document.querySelector('#video-container');
  const videoElement = videoContainer?.querySelector('video') as HTMLVideoElement;

  if (videoElement && videoElement.videoWidth && videoElement.videoHeight) {
    const canvas = document.createElement('canvas');
    canvas.width = videoElement.videoWidth;
    canvas.height = videoElement.videoHeight;
    const context = canvas.getContext('2d');
    if (context) {
      context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
      faceCaptureImg.value = canvas.toDataURL('image/png');
    }
  }

  setTimeout(() => {
    faceLoading.value = false;
    showRainModal.value = false;
    showFaceModal.value = true;
  }, 2000);
}
const time = ref('')
const screenshotInterval = ref(10000);
const screenshotTimer = ref<NodeJS.Timeout | null>(null);
const isScreenshotInProgress = ref(false); // 标记是否正在处理截图
const lastScreenshotTime = ref(0); // 记录上次截图时间
function startScreenshotTimer(value: { 机动车: number; 单非机动车: number; 多非机动车: number; }, timestamp: any) {
  if (!canvasShow.value) return;

  // 如果正在处理截图，直接返回
  if (isScreenshotInProgress.value) return;
  // 检查是否达到时间间隔
  const now = Date.now();
  if (now - lastScreenshotTime.value < screenshotInterval.value) return;
  time.value = timestamp
  // 标记开始处理
  isScreenshotInProgress.value = true;
  lastScreenshotTime.value = now;

  // 添加分类数据
  classData.value.push(value);
  // 执行截图操作
  captureDetectionFrame().finally(() => {
    // 无论成功失败，都重置标记
    isScreenshotInProgress.value = false;

    // 设置下一次截图的定时器
    if (canvasShow.value) {
      screenshotTimer.value = setTimeout(() => {
        if (canvasShow.value) {
          isScreenshotInProgress.value = false; // 重置以便可以再次截图
        }
      }, screenshotInterval.value);
    }
  });
}
// 捕获带有检测框的视频帧
async function captureDetectionFrame() {
  if (!canvasShow.value) return;
  try {
    const videoContainer = document.querySelector('#video-container');
    const videoElement = videoContainer?.querySelector('video') as HTMLVideoElement;
    const canvasElement = document.querySelector('#canvas-container') as HTMLCanvasElement;

    if (!videoElement || !canvasElement) return;

    // 获取视频原始尺寸（如 960×720）
    const videoWidth = videoElement.videoWidth;
    const videoHeight = videoElement.videoHeight;
    console.log('视频框高：', videoWidth, videoHeight);

    // 获取 canvas 的显示尺寸和缩放比例
    const rect = canvasElement.getBoundingClientRect();


    // 创建临时 canvas，尺寸为视频原始尺寸
    const tempCanvas = document.createElement('canvas');
    tempCanvas.width = videoWidth;
    tempCanvas.height = videoHeight;
    const ctx = tempCanvas.getContext('2d');
    if (!ctx) return;

    // 1. 绘制视频原始帧
    ctx.drawImage(videoElement, 0, 0, videoWidth, videoHeight);

    // 创建一个新的 960x720 的 canvas 用于绘制检测框
    const resizedCanvas = document.createElement('canvas');
    resizedCanvas.width = videoWidth;
    resizedCanvas.height = videoHeight;
    const resizedCtx = resizedCanvas.getContext('2d');
    if (!resizedCtx) return;
    // 计算目标显示区域的宽高比（16:9）
    const targetRatio = 1280 / 720;
    // 计算原始视频的宽高比(4:3)
    const originalRatio = videoWidth / videoHeight;
    const ratio43 = 4 / 3;
    const needsCentering = Math.abs(originalRatio - ratio43) < Math.abs(originalRatio - targetRatio);
    //1.3《1.7
    // 计算视频在720高度下的缩放宽度
    const scaledWidth = needsCentering ? Math.round(videoHeight * (videoWidth / videoHeight)) : 1280;
    // 计算居中偏移量
    const offsetX = needsCentering ? Math.floor((1280 - scaledWidth) / 2) : 0;
    // 将原 canvas 内容按比例绘制到新 canvas 上
    resizedCtx.drawImage(
      canvasElement,
      0, 0, canvasElement.width, canvasElement.height,
      0, 0, videoWidth, videoHeight
    );
    //但是检测框需要偏移
    if (needsCentering) {
      resizedCtx.translate(-offsetX, 0);
    }
    // 2. 绘制调整后的检测框
    ctx.drawImage(resizedCanvas, -offsetX, 0, 1280, 720);
    // 处理截图结果
    const dataUrl = tempCanvas.toDataURL('image/png', 1);
    const newId = imageList.value.length + 1;
    imageList.value.push({
      url: dataUrl,
      id: newId,
      time: time.value
    });
    isScreenshotLoading.value = false;
    const file = base64ToFile(dataUrl, 'file');
    let id = airportDockItem.value.host?.taskId || '';
    let sn = route.query.sn || '';
    const formData = new FormData();
    let params = {
      deviceSn: route.query.dsn + '',
      deviceName: airportDockItem.value.host?.nickname || "无人机",
      aiSubType: "机动车,单非机动车,多非机动车",
      eventName: eventName.value,
      eventId: ''
    };

    formData.append('file', file);
    // 上传图片
    const uploadRes = await imgUpload(id, sn, formData);
    console.log("图片上传完成", uploadRes);

    if (uploadRes.data?.eventId) {
      params.eventId = uploadRes.data.eventId;
      // 上传文件信息
      await uploadFileInfo(params);
      console.log("文件信息上传完成");
      isScreenshotLoading.value = false;
    }
  } catch (error) {
    console.error('截图失败:', error);
    isScreenshotLoading.value = false;
    throw error; // 重新抛出错误以便外层处理
  }
}

// 停止定时截图
function stopScreenshotTimer() {
  if (screenshotTimer.value) {
    clearTimeout(screenshotTimer.value);
    screenshotTimer.value = null;
  }
  isScreenshotInProgress.value = false;
}

function base64ToFile(base64String: string, filename: string): File {
  // 分离Base64数据和MIME类型
  const arr = base64String.split(',');
  const mimeMatch = arr[0].match(/:(.*?);/);
  const mime = mimeMatch ? mimeMatch[1] : 'application/octet-stream';
  const bstr = atob(arr[1]);

  let n = bstr.length;
  const u8arr = new Uint8Array(n);

  while (n--) {
    u8arr[n] = bstr.charCodeAt(n);
  }

  return new File([u8arr], filename, { type: mime });
}
const updateTime = () => {
  const now = new Date();
  currentTime.value = now.toLocaleTimeString();
  currentDate.value = now.toLocaleDateString();
}

const aiClassList = ref<number[]>([]); // 已开启的AI类型的classid
// 获取已开启的AI类型的classid
function getAIClssIDList() {
  aiClassList.value = aiServerStore.aiList.map(item => item.aiType);
  console.log('aiClassList: ', aiClassList.value);
}
onMounted(() => {
  if (route.query.sn) { // 获取设备编码
    flyInfoDockSN.value = route.query.sn;
    flyInfoDroneSN.value = route.query.dsn;
    queryDebugStatus(); // 查询机场当前调试状态（用于恢复调试/直播状态）
  }
  updateTime();
  setInterval(updateTime, 1000);
})


onBeforeUnmount(() => {
  imageList.value = []
  if (catchUpInterval.value) {
    clearInterval(catchUpInterval.value);
    catchUpInterval.value = null;
  }
  onStreamStop();
})

</script>

<style scoped>
.n-radio-group .n-radio-button {
  padding: 0 0.5rem;
  font-size: 0.7rem;
}




.vertical-marquee-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  /* border: 1px solid #eee; */
  border-radius: 8px;
}

.marquee-wrapper {
  height: 200px;
  width: 240px;
}

.marquee-wrapper1 {
  height: 300px;
}

.marquee-item {
  display: flex;
  /* 使用flex布局 */
  align-items: center;
  /* 垂直居中 */
  gap: 15px;
  margin: 15px -10px;
  /* 图片和描述之间的间距 */
  padding: 0 20px;
  /* 添加左右内边距 */
  flex-wrap: wrap;
}

.marquee-image {
  flex-shrink: 0;
  /* 防止图片被压缩 */
}

.img-desc {
  color: white;
  font-size: 14px;
  margin: 0;
  /* 移除默认外边距 */
  white-space: wrap;
  /* 防止文字换行 */
}

.img-desc p {
  display: block;
  margin: 0;
  padding: 0;
}

.toggle-arrow {
  position: absolute;
  right: 0;
  top: -2px;
  cursor: pointer;
}

.toggle-arrow:hover {
  color: #1890ff;
}

.carousel-img {
  width: 100%;
  height: 240px;
  object-fit: cover;
}
</style>
