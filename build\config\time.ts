import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

export function getBuildTime() {
  dayjs.extend(utc);
  dayjs.extend(timezone);

  const buildTime = dayjs.tz(Date.now(), 'Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

  return buildTime;
}

export function getTextTime(time:string|Date) {
  dayjs.extend(utc);
  dayjs.extend(timezone);

  // const textTime = dayjs(time)'Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');
  const textTime = dayjs(time).tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss');

  return textTime;
}
