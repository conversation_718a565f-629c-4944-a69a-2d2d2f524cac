{"name": "flight_web", "type": "module", "version": "", "description": "Vue3 admin template", "author": {"name": "SongAnJiTuan", "email": "<EMAIL>"}, "keywords": ["Vue3 admin ", "vue-admin-template", "Vite5", "TypeScript", "naive-ui", "naive-ui-admin", "ant-design-vue v4", "UnoCSS"], "engines": {"node": ">=18.12.0", "pnpm": ">=8.7.0"}, "scripts": {"dev": "vite --mode test", "build": "vite build --mode prod", "build:test": "vite build --mode test", "cleanup": "sa cleanup", "commit": "sa git-commit", "commit:zh": "sa git-commit -l=zh-cn", "dev:prod": "vite --mode prod", "gen-route": "sa gen-route", "lint": "eslint . --fix", "prepare": "simple-git-hooks", "preview": "vite preview", "release": "sa release", "update-pkg": "sa update-pkg"}, "dependencies": {"@better-scroll/core": "2.5.1", "@iconify/vue": "4.1.2", "@sa/axios": "workspace:*", "@sa/color": "workspace:*", "@sa/hooks": "workspace:*", "@sa/materials": "workspace:*", "@sa/utils": "workspace:*", "@turf/turf": "6.5.0", "@types/cesium": "^1.70.0", "@types/three": "^0.174.0", "@vicons/ionicons5": "^0.12.0", "@vueuse/core": "11.0.1", "agora-rtc-sdk-ng": "^4.22.1", "cesium": "^1.127.0", "cesium-extends": "^1.8.20", "clipboard": "2.0.11", "cos-js-sdk-v5": "^1.8.7", "dayjs": "1.11.12", "echarts": "5.5.1", "flv.js": "^1.6.2", "html2canvas": "^1.4.1", "leaflet": "^1.9.4", "mapv": "^2.0.62", "mars3d": "^3.8.3", "naive-ui": "2.39.0", "nprogress": "0.2.0", "photo-sphere-viewer": "^4.8.1", "pinia": "2.2.2", "socket.io": "^4.8.1", "tailwind-merge": "2.5.2", "tcplayer.js": "^5.2.0", "three": "^0.174.0", "video.js": "^8.19.1", "vite-plugin-cesium": "^1.2.23", "vite-svg-loader": "^5.1.0", "vue": "3.4.38", "vue-draggable-plus": "0.5.3", "vue-i18n": "9.14.0", "vue-router": "4.4.3", "vue3-marquee": "^4.2.2"}, "devDependencies": {"@elegant-router/vue": "0.3.8", "@iconify/json": "2.2.238", "@sa/scripts": "workspace:*", "@sa/uno-preset": "workspace:*", "@soybeanjs/eslint-config": "1.4.0", "@types/node": "22.4.1", "@types/nprogress": "0.2.3", "@types/socket.io-client": "^3.0.0", "@types/toastr": "^2.1.43", "@unocss/eslint-config": "0.62.2", "@unocss/preset-icons": "0.62.2", "@unocss/preset-uno": "0.62.2", "@unocss/transformer-directives": "0.62.2", "@unocss/transformer-variant-group": "0.62.2", "@unocss/vite": "0.62.2", "@vicons/fluent": "^0.13.0", "@vitejs/plugin-vue": "5.1.2", "@vitejs/plugin-vue-jsx": "4.0.1", "eslint": "9.9.0", "eslint-plugin-vue": "9.27.0", "json5": "2.2.3", "lint-staged": "15.2.9", "sass": "1.77.8", "simple-git-hooks": "2.11.1", "tsx": "4.17.0", "typescript": "5.5.4", "unplugin-icons": "0.19.2", "unplugin-vue-components": "0.27.4", "vite": "5.4.1", "vite-plugin-progress": "0.0.7", "vite-plugin-svg-icons": "2.0.1", "vite-plugin-vue-devtools": "7.3.8", "vue-eslint-parser": "9.4.3", "vue-tsc": "2.0.29"}, "lint-staged": {"*": "eslint --fix"}}