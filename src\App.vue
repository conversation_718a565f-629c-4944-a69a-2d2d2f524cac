<script setup lang="ts">
import { Ref, computed, inject, nextTick, onMounted, provide, ref } from 'vue';
import { NConfigProvider, darkTheme } from 'naive-ui';
import type { WatermarkProps } from 'naive-ui';
import { useAppStore } from './store/modules/app';
import { useThemeStore } from './store/modules/theme';
import { naiveDateLocales, naiveLocales } from './locales/naive';
// import useSocket from "@/service/websocket/useSocket"

defineOptions({
  name: 'App'
});

type ChatMessageType = string | null;

const appStore = useAppStore();
const themeStore = useThemeStore();

const naiveDarkTheme = computed(() => (themeStore.darkMode ? darkTheme : undefined));

const naiveLocale = computed(() => {
  return naiveLocales[appStore.locale];
});

const naiveDateLocale = computed(() => {
  return naiveDateLocales[appStore.locale];
});

const isRouterActive = ref(true);

// inject<{ chatMessage: Ref<ChatMessageType> }>('useSocket');

const watermarkProps = computed<WatermarkProps>(() => {
  return {
    content: themeStore.watermark?.text || 'SoybeanAdmin',
    cross: true,
    fullscreen: true,
    fontSize: 16,
    lineHeight: 16,
    width: 384,
    height: 384,
    xOffset: 12,
    yOffset: 60,
    rotate: -15,
    zIndex: 9999
  };
});

provide('reload', () => {
  isRouterActive.value = false
  nextTick(() => {
    isRouterActive.value = true
  })
})

onMounted(() => {
  // useSocket().retryConnect();
})
</script>

<template>
  <NConfigProvider :theme="naiveDarkTheme" :theme-overrides="themeStore.naiveTheme" :locale="naiveLocale"
    :date-locale="naiveDateLocale" class="h-full">
      <AppProvider>
        <RouterView class="bg-layout" />
        <!-- <NWatermark v-if="themeStore.watermark?.visible" v-bind="watermarkProps" /> -->
      </AppProvider>
  </NConfigProvider>
</template>
<style scoped>
</style>
