import { request } from '../request';

/**
 * 获取首页汇总数据
 */
export function fetchIndexAll() {
  return request({ url: '/index/all', method: 'get' });
}

/**
 * 获取首页组织信息
 */
export function fetchIndexOrg() {
  return request({ url: '/index/org', method: 'get' });
}
// 热力图
export function heatMap() {
  return request({ url: '/index/heatMap', method: 'get' });
}
// 飞行数据
export function flightSize(type: number, stime: string, etime: string) {
  return request({ url: '/index/flightSizeGoup?type=' + type + '&stime=' + stime + '&etime=' + etime, method: 'get' });
}
// 数据汇总
export function fileSize() {
  return request({ url: '/index/fileSize?type=5', method: 'get' });
}
// 飞行器数据
export function flightMileageGoup(type: number, stime: string, etime: string) {
  return request({ url: '/index/flightMileageGoup?type=' + type + '&stime=' + stime + '&etime=' + etime, method: 'get' });
}
// 飞行时间
export function flightTimeGoup(type: number, stime: string, etime: string) {
  return request({ url: '/index/flightTimeGoup?type=' + type + '&stime=' + stime + '&etime=' + etime, method: 'get' });
}
// 设备信息
export function allDeviceInfo() {
  return request({ url: '/index/allDeviceInfo', method: 'get' });
}
// 设备信息
export function getLossInfo() {
  return request({ url: '/index/getLossInfo', method: 'get' });
}
/**
 * 获取首页数据概览数据
 */
export function fetchDataOverview(params: { stime: string, etime: string, type: string }) {
  return request({ url: '/index/dataOverview/all', method: 'get', params });
}
