<template>
  <div ref="container" class="vertical-marquee-container" @mouseenter="handleMouseEnter" @mouseleave="handleMouseLeave"
    @wheel="handleWheel">
    <div ref="contentOriginal" class="vertical-marquee-content original" :style="originalStyle">
      <slot />
    </div>
    <div v-if="shouldClone" ref="contentClone" class="vertical-marquee-content clone" :style="cloneStyle">
      <slot />
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, onMounted, onBeforeUnmount, nextTick, watch } from 'vue';

export default defineComponent({
  name: 'VerticalMarquee',
  props: {
    speed: {
      type: Number,
      default: 50, // 像素/秒
    },
    hoverPause: {
      type: Boolean,
      default: true,
    },
    direction: {
      type: String,
      default: 'up', // 'up' 或 'down'
      validator: (value: string) => ['up', 'down'].includes(value),
    },
    contentKey: {
      type: [String, Number],
      default: '',
    },
  },
  setup(props) {
    const container = ref<HTMLElement | null>(null);
    const contentOriginal = ref<HTMLElement | null>(null);
    const contentClone = ref<HTMLElement | null>(null);

    const scrollPosition = ref(0);
    const lastTime = ref(0);
    const isPaused = ref(false);
    const animationId = ref<number | null>(null);
    const contentHeight = ref(0);
    const containerHeight = ref(0);
    const prevContentHeight = ref(0);

    const shouldClone = computed(() => contentHeight.value > containerHeight.value);

    const originalStyle = computed(() => ({
      transform: `translateY(${props.direction === 'up' ? '-' : ''}${scrollPosition.value}px)`,
    }));

    const cloneStyle = computed(() => ({
      transform: `translateY(${props.direction === 'up' ? contentHeight.value - scrollPosition.value : -contentHeight.value + scrollPosition.value}px)`,
    }));

    // 监听内容变化
    watch(() => props.contentKey, () => {
      nextTick(() => {
        updateContentHeight();
      });
    });

    const handleMouseEnter = () => {
      if (props.hoverPause) {
        pause();
      }
    };

    const handleMouseLeave = () => {
      if (props.hoverPause) {
        resume();
      }
    };

    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();

      const wheelDelta = e.deltaY > 0 ? 1 : -1;
      const directionFactor = props.direction === 'up' ? 1 : -1;
      scrollPosition.value += wheelDelta * 20 * directionFactor;

      // 确保位置在有效范围内
      if (scrollPosition.value < 0) {
        scrollPosition.value += contentHeight.value;
      } else if (scrollPosition.value >= contentHeight.value) {
        scrollPosition.value -= contentHeight.value;
      }

      lastTime.value = performance.now();
    };

    const animate = (time: number) => {
      if (isPaused.value) {
        return;
      }

      if (!lastTime.value) {
        lastTime.value = time;
      }

      const deltaTime = time - lastTime.value;
      lastTime.value = time;

      // 计算滚动距离（速度单位：像素/秒）
      const deltaY = (props.speed * deltaTime) / 1000;
      scrollPosition.value += props.direction === 'up' ? deltaY : -deltaY;

      // 检查是否滚动到底部/顶部
      if (props.direction === 'up' && scrollPosition.value >= contentHeight.value) {
        scrollPosition.value -= contentHeight.value;
      } else if (props.direction === 'down' && scrollPosition.value <= 0) {
        scrollPosition.value += contentHeight.value;
      }

      animationId.value = requestAnimationFrame(animate);
    };

    const pause = () => {
      isPaused.value = true;
      if (animationId.value) {
        cancelAnimationFrame(animationId.value);
        animationId.value = null;
      }
    };

    const resume = () => {
      if (isPaused.value) {
        isPaused.value = false;
        lastTime.value = performance.now();
        animationId.value = requestAnimationFrame(animate);
      }
    };

    const updateContentHeight = () => {
      if (!contentOriginal.value || !container.value) return;

      const wasEmpty = prevContentHeight.value === 0;
      prevContentHeight.value = contentHeight.value;

      const newHeight = contentOriginal.value.getBoundingClientRect().height;
      contentHeight.value = newHeight;
      containerHeight.value = container.value.getBoundingClientRect().height;

      // 关键修改：初始加载时定位内容
      if (wasEmpty && newHeight > 0) {
        if (props.direction === 'up') {
          // 向上滚动：让内容从容器底部开始出现
          scrollPosition.value = Math.max(0, newHeight - containerHeight.value-10);
        } else {
          // 向下滚动：从顶部开始
          scrollPosition.value = 0;
        }
      }

      if (shouldClone.value && contentClone.value) {
        contentClone.value.style.visibility = 'visible';
      }

      if (contentHeight.value <= containerHeight.value) {
        pause();
      } else if (!animationId.value) {
        startAnimation();
      }
    };

    const setup = () => {
      updateContentHeight();

      if (contentHeight.value > containerHeight.value) {
        startAnimation();
      }
    };

    const startAnimation = () => {
      lastTime.value = performance.now();
      animationId.value = requestAnimationFrame(animate);
    };

    const stopAnimation = () => {
      if (animationId.value) {
        cancelAnimationFrame(animationId.value);
        animationId.value = null;
      }
    };

    onMounted(() => {
      nextTick(() => {
        setup();
      });

      // 监听窗口大小变化
      window.addEventListener('resize', setup);
    });

    onBeforeUnmount(() => {
      stopAnimation();
      window.removeEventListener('resize', setup);
    });

    return {
      container,
      contentOriginal,
      contentClone,
      originalStyle,
      cloneStyle,
      shouldClone,
      handleMouseEnter,
      handleMouseLeave,
      handleWheel,
    };
  },
});
</script>

<style scoped>
.vertical-marquee-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.vertical-marquee-content {
  position: absolute;
  width: 100%;
  will-change: transform;
  transition: transform 0.5s ease-out;
}

.vertical-marquee-content.clone {
  visibility: hidden;
}
</style>