<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import dayjs from 'dayjs';
import { fetchAirLineList, fetchFilesList, fetchFlyRecordList } from '@/service/api';
import VideoItem from './modules/video-item.vue';
import { NEmpty, NSpin, useMessage } from 'naive-ui';
import type { MessageReactive } from 'naive-ui'
import { deptTree } from "@/service/api/user";
const airLineList = ref<Api.AirLine.AirLineItem[]>([]);
const photoList = ref<Api.File.VideoListItem[]>([]);
const groupedVideos = ref<{ date: string; video: Api.File.VideoListItem[] }[]>([]);
const message = useMessage();

const rollLoading = ref(false);
const treeData = ref([]);
const generalFlyRecordOptions = ref<{ label: string; value: string }[]>([]);

const flyRecordList = ref<Api.File.VideoListItem[]>([]);

const formValue = reactive<{ deptId: number | null; flightJobId: string | null; range: [number, number] | null; flightId: string | null; }>({
  flightJobId: null,
  deptId: null,
  range: null,
  flightId: null
});

const paginationReactive = reactive({
  page: 1,
  pageCount: 1,
  pageSize: 10,
  itemCount: 0
});

const disablePreviousDate = (ts: number) => {
  return ts > Date.now();
};

const loading = ref(true);

async function getVedioList() {
  loading.value = true;
  const { flightJobId, range, deptId, flightId } = formValue;
  const record = flyRecordList.value.find((item) => flightJobId === String(item.flightJobId));
  const remark = record && record?.remark || null;
  const json = {
    deptId,
    recordIds: remark,
    startDate: range && range[0] ? dayjs(range[0]).format('YYYY-MM-DD') : null,
    endDate: range && range[1] ? dayjs(range[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null,
    flightId: flightId,
    pageNum: 1,
    pageSize: paginationReactive.pageSize,
    fileType: 1
  };
  const { data, error } = await fetchFilesList(json);
  if (!error) {
    photoList.value = data.rows;
    paginationReactive.itemCount = data.total;
    groupedVideos.value = handleVideoList(photoList.value);
  }
  loading.value = false;
}

async function rollVideoList() {
  const { flightJobId, range } = formValue;
  const record = flyRecordList.value.find(item => flightJobId === String(item.flightJobId));
  const remark = record ? record.remark : null;
  const { page, pageSize, itemCount } = paginationReactive;
  const json = {
    recordIds: remark,
    startDate: range && range[0] ? dayjs(range[0]).format('YYYY-MM-DD') : null,
    endDate: range && range[1] ? dayjs(range[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null,
    pageNum: page,
    pageSize,
    fileType: 1
  };
  rollLoading.value = true;
  const { data, error } = await fetchFilesList(json);
  if (!error) {
    paginationReactive.itemCount = data.total;
    if ((page - 1) * pageSize < itemCount) {
      photoList.value.push(...data.rows);
    } else {
      paginationReactive.page = 1;
      photoList.value = data.rows;
    }
    rollLoading.value = false;
    groupedVideos.value = handleVideoList(photoList.value);
    removeMessage();
  }
}

async function getTaskList() {
  const { error, data } = await fetchFlyRecordList();
  if (!error) {
    flyRecordList.value = data.rows;
    generalFlyRecordOptions.value = data.rows.map((v: Api.Task.TaskItem) => ({
      label: v.flightJobName,
      value: String(v.flightJobId)
    }));
  }
}

const handleLoadTable = () => {
  const { page, pageSize, itemCount } = paginationReactive;
  if (page * pageSize < itemCount && !rollLoading.value) {
    paginationReactive.page += 1;
    rollVideoList();
    createMessage();
  }
};

const handleResetClick = () => {
  photoList.value = [];
  groupedVideos.value = [];
  formValue.deptId = null;
  formValue.flightJobId = null;
  formValue.range = null;
  formValue.flightId = null;
  paginationReactive.page = 1;
  paginationReactive.pageSize = 10;
  paginationReactive.itemCount = 0;
  getVedioList();
};

const handleQueryClick = () => {
  photoList.value = [];
  groupedVideos.value = [];
  paginationReactive.page = 1;
  paginationReactive.pageSize = 10;
  paginationReactive.itemCount = 0;
  getVedioList();
};

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
}
const loadingA = ref(false)
const hasMore = ref(true)

const pagination = ref({
  pageNum: 1,
  pageSize: 20,
  total: 0,
  keyword: ''
})
// 加载选项数据
const loadOptions = async (isSearch = false) => {
  if (loadingA.value) return;
  loadingA.value = true;
  try {
    if (isSearch) {
      pagination.value.pageNum = 1;
      airLineList.value = [];
      hasMore.value = true;
    }
    if (!hasMore.value) return;

    const response = await fetchAirLineList({
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
    });
    if (response.data.rows.length === 0) {
      hasMore.value = false;
      if (pagination.value.pageNum === 1) {
        message.info('暂无数据');
      } else {
        message.info('没有更多数据了');
      }
      return;
    }

    // 转换数据格式为 NSelect 需要的格式
    const newOptions = response.data.rows.map((item: Api.AirLine.AirLineItem) => ({
      label: item.flightName, // 假设 flightName 是显示文本
      value: item.flightId,   // 假设 flightId 是值
    }));

    airLineList.value = [...airLineList.value, ...newOptions];
    pagination.value.total = response.data.total;

    if (response.data.rows.length < pagination.value.pageSize) {
      hasMore.value = false;
    } else {
      pagination.value.pageNum++;
    }
  } catch (error) {
    message.error('加载数据失败');
    console.error(error);
  } finally {
    loadingA.value = false;
  }
};

// 滚动事件处理
const handleScroll = (e: any) => {
  const { scrollTop, scrollHeight, clientHeight } = e.target
  // 接近底部时加载更多
  if (scrollHeight - (scrollTop + clientHeight) < 50) {
    loadOptions()
  }
}
// 处理视频列表，按日分类
const handleVideoList = (list: Api.File.VideoListItem[]) => {
  const groupedvideos = Object.entries(
    list.reduce((acc: { [key: string]: Api.File.VideoListItem[] }, video) => {
      const date = dayjs(video.createTime).format("YYYY-MM-DD");
      if (!acc[date]) {
        acc[date] = [];
      }
      acc[date].push(video);
      return acc;
    }, {})
  ).map(([date, video]) => ({ date, video }));
  console.log("分组后的视频列表：", groupedvideos);
  return groupedvideos;
};
let messageReactive: MessageReactive | null = null
const removeMessage = () => {
  if (messageReactive) {
    messageReactive.destroy()
    messageReactive = null
  }
}
const createMessage = () => {
  if (!messageReactive) {
    messageReactive = message.loading('加载更多中', {
      duration: 0,
    });
  }
}
onMounted(() => {
  loadOptions();
  getDeptTree();
  getVedioList();
  getTaskList();
});
</script>

<template>
  <div>
    <NCard :bordered="false" title="视频">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :cols="24" :x-gap="24">
          <NFormItemGi :span="4" label="组织选择：" path="deptId">
            <NTreeSelect clearable v-model:value="formValue.deptId" filterable key-field="id" label-field="label"
              children-field="children" :options="treeData" :default-value="formValue.deptId" />
          </NFormItemGi>
          <NFormItemGi :span="4" label="航线名称：" path="flightId">
            <NSelect v-model:value="formValue.flightId" :options="airLineList" :loading="loadingA" filterable
              :reset-menu-on-options-change="false" @scroll="handleScroll" />
          </NFormItemGi>
          <NFormItemGi :span="4" label="任务名称：" path="flightJobId">
            <NSelect v-model:value="formValue.flightJobId" filterable placeholder="请选择任务"
              :options="generalFlyRecordOptions" clearable />
          </NFormItemGi>
          <NFormItemGi :span="6" label="拍摄日期：" path="range">
            <NDatePicker v-model:value="formValue.range" type="daterange" :is-date-disabled="disablePreviousDate"
              clearable />
          </NFormItemGi>
          <NFormItemGi :span="6">
            <NButton attr-type="button" class="mr2" @click="handleResetClick">重置</NButton>
            <NButton type="primary" attr-type="button" @click="handleQueryClick">查询</NButton>
          </NFormItemGi>
        </NGrid>
      </NForm>
      <NDivider />

      <NSpin :show="loading">
        <NInfiniteScroll style="height: 75vh" :distance="10" @load="handleLoadTable">
          <NEmpty v-if="!loading && photoList.length === 0" description="暂无视频数据" size="large">
            <template #extra>
              <NButton size="small" @click="handleQueryClick">重新加载</NButton>
            </template>
          </NEmpty>
          <n-list v-else>
            <n-list-item v-for="(item, index) in groupedVideos" :key="index">
              <n-thing :title="item.date"></n-thing>
              <NGrid x-gap="12" y-gap="12" cols="600:2 900:3 1200:4 1500:5">
                <NGi v-for="i in item.video" :key="i.id">
                  <NCard hoverable>
                    <template #cover>
                      <VideoItem :poster="i.cover" :src="i.filePath" class="h-60 w-100%" />
                    </template>
                    <h4 style="font-weight: bold" class="mt-10px">{{ i.workspaceName ? i.workspaceName : '---' }}</h4>
                    <h6 style="font-weight: lighter" class="mt-10px">
                      {{ '文件大小：' + ((i.size ? i.size : 0) / 1024 / 1024).toFixed(2) + 'MB | ' + i.payload }}
                    </h6>
                    <h6 style="font-weight: lighter" class="mt-10px">{{ '录制时间：' + i.createTime }}</h6>
                    <h6 class="mt-6px" style="font-weight: lighter">上传渠道：{{ i.flightType == 0 ? '航线任务' : i.flightType ==
                      1
                      ? '一键起飞任务' :
                      '本地上传' }}</h6>
                    <h6 class="mt-6px" style="font-weight: lighter">{{ '飞行任务名称：' + i.jobName }}</h6>
                  </NCard>
                </NGi>
              </NGrid>
            </n-list-item>
          </n-list>

        </NInfiniteScroll>
      </NSpin>
    </NCard>
  </div>
</template>

<style scoped>
.n-spin-container {
  min-height: 200px;
}

.n-empty {
  margin: 32px 0;
}
</style>
