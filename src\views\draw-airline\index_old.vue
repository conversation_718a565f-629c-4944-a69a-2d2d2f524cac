<template>
  <div class="h100vh relative">
    <div id="cesiumViewer" class="h100%"></div>
    <n-float-button :left="100" :top="60" @click="toggleViewMode">
      {{ is3D ? '2D' : '3D' }}
    </n-float-button>
    <status-bar id="status-bar" class="w-300px h-max" v-if="loaded" :viewer="viewer"></status-bar>
    <geoMeasureKit :viewer="viewer" @changeStatus="handleClickStatus"
      :objSpace="{ place: { bottom: 10, left: 100 }, width: 30, direction: 'left' }" ref="childRef" :style="{
        position: 'fixed',
        bottom: '10px',
        left: '100px',
        zIndex: 1000
      }" />
    <n-float-button :left="100" :top="10">
      <n-icon @click="() => { routerBack() }">
        <ArrowBack />
      </n-icon>
    </n-float-button>
    <!-- 整条航线设置 -->
    <airLineInfo :sumFlyDistance="sumFlyDistance" :waypointLatLngArray="waypointLatLngArray"
      :airLineConfig="airLineConfig" @save-airline="handleClickThrottle" ref="airLineInfoRef" />
    <!-- 单个航点设置 -->
    <airLineCreate v-show="airlineSetModalShow" :currentWaypointIndex="currentWaypointIndex"
      :airPointActionArr="airPointActionArr" :waypointLatLngArray="waypointLatLngArray"
      :heightArrParams="heightArrParams" @handel-creat-modal="handelAirlineSetModalShow"
      :updateVerticalLineHeight="updateVerticalLineHeight" @update:wayActions="updateWayActions" ref="airLineCreateRef"
      @getHeight="getHeight" />
    <!-- 方向仪表盘 -->
    <directionDashboard class="dashboard" ref="dashboardRef" :viewer="viewer" :is3D="is3D" />
  </div>
</template>

<script setup lang="ts">
import * as Cesium from 'cesium';
import { Ref, h, onBeforeUnmount, onMounted, reactive, ref, toRefs, watch, provide } from 'vue';
import { MouthPositionHandler, cesiumConfig, josiahProvider, labelProvider, position, rectangle } from '@/config/cesium_config';
import marker from "@/assets/imgs/airport.png";
import { LocationQueryValue, useRoute, useRouter } from 'vue-router';
import { useRouterPush } from '@/hooks/common/router';
import { fetchAirLineSaveInfo, fetchAirportDeviceList, sendAirLineSaveInfo, uploadBase64Common } from '@/service/api';
import { Widget, DomUtil } from '@cesium-extends/common';
import ZoomController from '@cesium-extends/zoom-control';
import { MouseTooltip } from '@cesium-extends/tooltip';
import statusBar from '@/components/cesium/status-bar.vue';
import geoMeasureKit from '@/components/cesium/geo-measure-kit/index.vue';
import { useAirportStore } from '@/store/modules/airport';
import airLineInfo from './modules/airline-info.vue';
import airLineCreate from './modules/airline-creat.vue';
import { ArrowBack, InformationCircleSharp, Locate } from '@vicons/ionicons5';
import { NButton, useMessage, useModal } from 'naive-ui';
import directionDashboard from '@/components/cesium/direction-dashboard/index.vue';

// import vis3d from "./vis3d.js";
// import mars3d from 'mars3d';

const airportStore = useAirportStore();
const message = useMessage();
const route = useRoute();
const { routerBack } = useRouterPush();
const router = useRouter();
// cesium viewer 初始化
let viewer: Cesium.Viewer;
// const noFlyZoneDataSource = reactive(new Cesium.CustomDataSource('NoFlyZone'));
// const drawAirLineLayer = reactive(new Cesium.CustomDataSource('AirLine'));

let handler: Cesium.ScreenSpaceEventHandler | null = null;
const loaded = ref(false);
// 是否为编辑 编辑-false, 新增-true
const editAirLineID = ref<LocationQueryValue[] | string>();
// 机场列表
const airportDeviceList = reactive<Api.Airport.AirportDeviceInfo[]>([])
// 航点设置栏的展示
const airlineSetModalShow = ref(false);
const relativeHeight = ref(100);
let lengthArr = ref(0)

// ↓↓↓↓↓↓↓↓↓↓↓↓↓↓航点航线绘制相关↓↓↓↓↓↓↓↓↓↓↓↓↓↓

// 用于存储当前绘制的点坐标集合
const activeShapePoints = reactive<Cesium.Cartesian3[]>([]);
// 用于存储浮动点实体（即在鼠标移动时临时显示的点）
const floatingPoint = ref<Cesium.Entity | null>(null);
// 用于存储当前正在绘制的线实体
const activeShape = ref<Cesium.Entity | null>(null);
// 用于存储所有绘制的点实体
const pointEntities = ref<Cesium.Entity[]>([]);
// 用于计数绘制的点的数量
const pointCounter = ref<number>(1);
// 用于存储距离标签实体（标记两点之间的距离）
const distanceLabels = ref<Cesium.Entity[]>([]);
// 获取整个航线设置组件实例
const airLineInfoRef = ref()
let height: number = 100;
// ↑↑↑↑↑↑↑↑↑↑↑↑↑航点航线绘制相关↑↑↑↑↑↑↑↑↑↑↑↑↑↑

// 存储航点的经纬度数组
const waypointLatLngArray = ref<Api.AirLine.WayPointList[]>([]);
const currentWaypointIndex = ref<number>(0);
let heightArr: Array<number> = reactive([])
const heightArrParams = ref<number[]>([])

// 存储预计飞行里程
const sumFlyDistance = ref<number>(0);
const airLineCreateRef = ref();

const billImage = 'data:image/png;base64,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'
// 初始化地图
const childRef = ref();
const airlineConflg = {
  sceneMode: 2,
  // terrain: Cesium.Terrain.fromWorldTerrain(),
}
const initCesium = () => {
  viewer = new Cesium.Viewer('cesiumViewer', { ...cesiumConfig, ...airlineConflg });
  childRef.value?.childMethod(viewer);
  dashboardRef.value?.addEvent(viewer);
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; //修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  // viewer.scene.globe.depthTestAgainstTerrain = true;  //启用深度测试
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK); // 取消双击事件
  // loaded = true;
  // 修改图层
  // viewer.imageryLayers.removeAll();

  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider); // 添加注记图层
  // viewer.imageryLayers.raiseToTop(tdtNoteLayer);//将注记图层置顶

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置相机位置为当前定位点
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(position.coords.longitude, position.coords.latitude, 10000), // 设置目的地高度为10000米
    orientation: {
      // heading: Cesium.Math.ZERO,
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });

  // 加载禁飞区JSON文件并绘制禁飞区
  loadJSONAndDrawNoFlyZone();

  // 启动航线绘制
  drawLineRoad();

  // 启动键盘控制
  moveCamera()
  // 添加鼠标移动事件监听
  const mouseTooltip = new MouseTooltip(viewer, {
    content: is3D.value ? "右键删除该点，拖拽移动水平该点<br/>Alt+拖拽垂直移动该点" : "右键删除该点，拖拽移动水平该点",
    offset: [0, -5]
  });

  viewer.screenSpaceEventHandler.setInputAction((movement: any) => {
    const pick = viewer.scene.pick(movement.endPosition);
    if (pick && pick.id && pick.id.description?.getValue() === "waypoint") {
      mouseTooltip.showAt(movement.endPosition, is3D.value ? "右键删除该点，拖拽移动水平该点<br/>Alt+拖拽垂直移动该点" : "右键删除该点，拖拽移动水平该点");
    } else {
      mouseTooltip.hide();
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
}

// 处理航点的点击事件，展示该点的航点信息
function handlePointClick(clickEvent: Cesium.Event, pickedObject: any) {
  // 获取点击的 ID，index = ID - 1
  const index = Number((pickedObject.id as Cesium.Entity as { id: string }).id) - 1;
  currentWaypointIndex.value = index;
  // 显示航点设置弹窗
  handelAirlineSetModalShow(true);
}

// 可以在父组件添加深度watch来调试
watch(heightArrParams.value, (newVal: any) => {
}, { deep: true })
// 新增响应式变量控制绘制状态
const isDrawingEnabled = ref(true);
// 控制绘制状态+
const handleClickStatus = (status: boolean) => {
  isDrawingEnabled.value = status
}
let selectedPoint: Cesium.Entity | null = null;
// 绘制点和线路
function drawLineRoad() {
  handler = new Cesium.ScreenSpaceEventHandler(viewer!.canvas);

  let selectedPointIndex: number | null = null;
  let isDraggingPoint = false; // 新增：标记是否正在拖拽点
  let dragStartHeight = 0;
  let dragStartY = 0;
  let isAltDrag = false; // 区分普通拖拽和高度拖拽
  let waypointLatLng: Api.AirLine.WayPointList;
  // 鼠标左键点击事件
  handler.setInputAction((event: any) => {
    if (!isDrawingEnabled.value) return;
    // 获取点击位置的地球坐标
    const earthPosition = viewer!.scene.pickPosition(event.position);
    const cartographic = Cesium.Cartographic.fromCartesian(earthPosition);
    height = airLineInfoRef.value.surfaceRelativeHeight;
    cartographic.height = height;
    // 重新生成带高度的三维坐标
    const fixedHeightPosition = Cesium.Cartesian3.fromRadians(
      cartographic.longitude,
      cartographic.latitude,
      cartographic.height
    );
    // 判断点击是否为已有的实体
    const pick = viewer.scene.pick(event.position);
    if (Cesium.defined(fixedHeightPosition) && (!pick || pick.id.description._value !== "waypoint")) {
      heightArrParams.value.push(height)
      // 新增点
      if (activeShapePoints.length === 0) {
        // 如果是第一个点，创建浮动点并开始绘制线
        floatingPoint.value = createPoint(fixedHeightPosition);
        // 第一个点绘制无人机图标
        dashboardRef.value?.createDrone(fixedHeightPosition, viewer);
        activeShapePoints.push(fixedHeightPosition);
        // 动态更新线的坐标
        const dynamicPositions = new Cesium.CallbackProperty(() => {
          return activeShapePoints;
        }, false);
        // 绘制
        drawShape(dynamicPositions);

      } else {
        // 添加新的点并计算距离
        const lastPoint = activeShapePoints[activeShapePoints.length - 1];
        if (lastPoint) {
          const { distanceLabel, distanceNumber } = createDistanceLabel(lastPoint, fixedHeightPosition);
          sumFlyDistance.value = (sumFlyDistance.value + Number(distanceNumber));
          distanceLabels.value.push(distanceLabel); // 将距离标签添加到数组中
          activeShapePoints.push(fixedHeightPosition); // 添加新的点
          // const fixedHeightCallback = new Cesium.CallbackProperty(() => {
          //   return fixedHeightPosition;
          // }, false);
          // drawShape(fixedHeightCallback);

          updatelinePositions(); // 更新线的坐标
          createPoint(fixedHeightPosition); // 绘制新点
        }
      }
      // drawVerticalLine(fixedHeightPosition, activeShapePoints.length - 1);
    } else {
      // 点击已有的点，展示该点的航点信息
      handlePointClick(event, pick);
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  // 鼠标右键点击事件处理
  handler.setInputAction((event: any) => {
    if (!isDrawingEnabled.value) return;
    const pick = viewer.scene.pick(event.position);
    if (pick && pick.id && pick.id.description._value === "waypoint") {
      const index = Number((pick.id as Cesium.Entity as { id: string }).id) - 1;
      // 删除垂直线相关实体
      const verticalPrefix = `vertical_${index}_`;
      const toRemove = verticalLineEntities.value.filter(e =>
        e.id?.startsWith(verticalPrefix)
      );
      // 批量删除实体
      toRemove.forEach(e => viewer.entities.remove(e));
      // 更新数组
      verticalLineEntities.value = verticalLineEntities.value.filter(e =>
        !e.id?.startsWith(verticalPrefix)
      );
      const newVerticalLineEntities = ref<Cesium.Entity[]>([]);
      let newHeight = height;
      verticalLineEntities.value.forEach((entity, i) => {

        const verticalId = entity.id.split('_')[2];
        viewer.entities.remove(entity);

        if (verticalId === 'line' && entity.polyline && entity.polyline.positions) {
          const positions = entity.polyline.positions.getValue(Cesium.JulianDate.now());
          const cartographic = Cesium.Cartographic.fromCartesian(positions[0]);
          newHeight = cartographic.height;
          const newEntity = viewer.entities.add({
            id: `vertical_${Math.floor(i / 3)}_line`,
            show: is3D.value,
            polyline: {
              positions: positions,
              width: 1,
              material: Cesium.Color.WHITE
            }
          });
          newVerticalLineEntities.value.push(newEntity)
        } else if (verticalId === 'label') {
          const position = entity?.position?.getValue();
          const newEntity = viewer.entities.add({
            id: `vertical_${Math.floor(i / 3)}_label`,
            position: position,
            show: is3D.value,
            label: {
              text: `${newHeight.toFixed(1)}m`,
              font: 'bold 12px monospace',
              fillColor: Cesium.Color.WHITE,
              backgroundColor: Cesium.Color.BLACK.withAlpha(0.7)
            }
          })
          newVerticalLineEntities.value.push(newEntity)
        } else if (verticalId === 'point') {
          const position = entity?.position?.getValue();
          const newEntity = viewer.entities.add({
            id: `vertical_${Math.floor(i / 3)}_point`,
            position: position,
            show: is3D.value,
            point: {
              pixelSize: 4,
              color: Cesium.Color.WHITE,
              heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
            }
          });
          newVerticalLineEntities.value.push(newEntity)
        }
      });
      verticalLineEntities.value = newVerticalLineEntities.value;
      if (index >= 0 && index < activeShapePoints.length) {
        heightArrParams.value.splice(index, 1); // 删除索引的元素（修改原数组）
        const pointToRemove = activeShapePoints[index];

        if (pointToRemove) {
          const entityToRemove = pointEntities.value[index];
          viewer!.entities.remove(entityToRemove); // 移除点实体
          pointEntities.value.splice(index, 1); // 从数组中移除点实体
          // 删除第一个点
          if (index === 0) {
            console.log("删除最后一个点")
            // 删除第一个点
            if (activeShapePoints.length > 0) {
              const firstPointEntity = pointEntities.value.find(entity =>
                Cesium.Cartesian3.equals(entity?.position?.getValue(), activeShapePoints[0])
              );
              if (firstPointEntity) {
                viewer!.entities.remove(firstPointEntity); // 移除第一个点的实体
                activeShapePoints.splice(0, 1);
              }

              if (distanceLabels.value.length > 0) {
                const firstLabelToRemove = distanceLabels.value[0];
                if (firstLabelToRemove && firstLabelToRemove.label) {
                  const distanceNumber = Number(firstLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                  sumFlyDistance.value = parseFloat((sumFlyDistance.value - distanceNumber).toFixed(2));
                  viewer!.entities.remove(firstLabelToRemove); // 移除第一个距离标签
                  distanceLabels.value.splice(0, 1);
                }
              }
            }
          } else {
            if (index > 0) {
              const beforeLabelToRemove = distanceLabels.value[index - 1];
              if (beforeLabelToRemove && beforeLabelToRemove.label) {
                const distanceNumber = Number(beforeLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                sumFlyDistance.value = parseFloat((sumFlyDistance.value - distanceNumber).toFixed(2));
                viewer!.entities.remove(beforeLabelToRemove); // 移除 n-1 与 n 之间的距离标签
                distanceLabels.value.splice(index - 1, 1);
              }
            }

            if (index < activeShapePoints.length - 1) {
              const afterLabelToRemove = distanceLabels.value[index - 1];
              if (afterLabelToRemove && afterLabelToRemove.label) {
                const distanceNumber = Number(afterLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                sumFlyDistance.value = parseFloat((sumFlyDistance.value - distanceNumber).toFixed(2));
                viewer!.entities.remove(afterLabelToRemove); // 移除 n 与 n+1 之间的距离标签
                distanceLabels.value.splice(index - 1, 1);
              }
            }
          }
          pointCounter.value--;
          // 重新连接前一个点和后一个点
          if (index > 0 && index < activeShapePoints.length - 1) {
            const previousPoint = activeShapePoints[index - 1];
            const nextPoint = activeShapePoints[index + 1];
            if (previousPoint && nextPoint) {
              activeShapePoints.splice(index, 1); // 从数组中移除点
              const { distanceLabel, distanceNumber } = createDistanceLabel(previousPoint, nextPoint);
              sumFlyDistance.value = sumFlyDistance.value + Number(distanceNumber);
              distanceLabels.value.splice(index - 1, 0, distanceLabel); // 插入新的距离标签
            }
          } else {
            activeShapePoints.splice(index, 1); // 从数组中移除点
          }

          updatelinePositions(); // 更新线条位置
          waypointLatLngArray.value.splice(index, 1); // 从数组中移除点的经纬度

          // 创建一个新的实体数组
          const newPointEntities: Cesium.Entity[] = [];

          // 遍历现有的实体并重新创建
          pointEntities.value.forEach((entity, i) => {
            const position = entity?.position?.getValue();
            const label = `${i + 1}`;

            // 删除旧的实体
            viewer.entities.remove(entity);

            // 创建新的实体
            const newEntity = viewer.entities.add({
              id: `${i + 1}`,
              position: position,
              label: {
                text: label, // 标签内容为点的编号
                font: 'bold 14px sans-serif', // 字体加粗
                fillColor: Cesium.Color.WHITE, // 标签文本颜色为白色
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 标签水平对齐方式
                verticalOrigin: Cesium.VerticalOrigin.CENTER, // 标签垂直对齐方式
                backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'), // 背景颜色
                showBackground: true, // 不显示默认背景
                backgroundPadding: new Cesium.Cartesian2(2, 2), // 修改边距
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
              },
              description: 'waypoint',
              billboard: {
                image: billImage, // 圆形背景图像的路径
                width: 35, // 圆形的宽度
                height: 35, // 圆形的高度
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.CENTER, // 将背景的垂直对齐方式也改为 CENTER
                scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5), // 根据距离缩放
                disableDepthTestDistance: Number.POSITIVE_INFINITY,
              },
            });

            // 将新实体添加到新的数组中
            newPointEntities.push(newEntity);
          });

          // 替换旧的实体列表
          pointEntities.value = newPointEntities;
        }
      }
      if (verticalLineEntities.value.length === 0) {
        dashboardRef.value?.removeDrone(viewer);
      }
      // 3. 强制刷新场景
      viewer.scene.requestRender();
    }
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

  // 鼠标按下事件
  handler.setInputAction((event: any) => {
    if (!isDrawingEnabled.value) return;
    const pick = viewer.scene.pick(event.position);
    if (pick && pick.id && pick.id.description && pick.id.description._value === "waypoint") {
      const position = pick.id.position.getValue(Cesium.JulianDate.now());
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      dragStartHeight = cartographic.height;
      isDraggingPoint = true;
      selectedPoint = pick.id;
      selectedPointIndex = Number(pick.id.id) - 1;
      // 标记开始拖拽
      viewer.scene.screenSpaceCameraController.enableInputs = false; // 禁用地图拖拽
      // 隐藏被拖拽航点两侧的标签
      if (selectedPointIndex > 0) {
        distanceLabels.value[selectedPointIndex - 1].show = false;
      }
      if (selectedPointIndex < distanceLabels.value.length) {
        distanceLabels.value[selectedPointIndex].show = false;
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_DOWN);
  // 鼠标移动事件
  handler.setInputAction((movement: any) => {
    if (!isDrawingEnabled.value) return;
    if (isDraggingPoint && selectedPoint && selectedPointIndex !== null) {
      const newPosition = viewer.scene.pickPosition(movement.endPosition);
      if (newPosition) {
        // 获取新位置的经纬度和高度
        const cartographic = Cesium.Cartographic.fromCartesian(newPosition);
        const fixedPosition = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          dragStartHeight // 使用实际高度
        );

        // 更新航点位置
        selectedPoint.position = new Cesium.ConstantPositionProperty(fixedPosition);
        activeShapePoints[selectedPointIndex] = fixedPosition;

        // 更新经纬度数组
        waypointLatLngArray.value[selectedPointIndex] = {
          latitude: Cesium.Math.toDegrees(cartographic.latitude),
          longitude: Cesium.Math.toDegrees(cartographic.longitude),
          altitude: cartographic.height,
        };

        // 更新垂直线的位置
        const verticalLine = verticalLineEntities.value.find(e =>
          e.id === `vertical_${selectedPointIndex}_line`
        );
        if (verticalLine?.polyline) {
          verticalLine.polyline.positions = new Cesium.CallbackProperty(() => [
            fixedPosition,
            Cesium.Cartesian3.fromRadians(
              cartographic.longitude,
              cartographic.latitude,
              0 // 地面高度
            )
          ], false);
          verticalLine.show = true; // 确保垂直线显示
        }

        // 更新垂直线上的标签位置和内容
        const heightLabel = verticalLineEntities.value.find(e =>
          e.id === `vertical_${selectedPointIndex}_label`
        );
        console.log("cartographic.height", cartographic.height);
        if (heightLabel?.label) {
          heightLabel.position = new Cesium.ConstantPositionProperty(
            Cesium.Cartesian3.fromRadians(
              cartographic.longitude,
              cartographic.latitude,
              dragStartHeight / 2 // 标签位置在垂直线中间
            )
          );
          (heightLabel.label as Cesium.LabelGraphics).text = new Cesium.ConstantProperty(
            `${dragStartHeight.toFixed(0)}m`
          );
          heightLabel.show = true; // 确保标签显示
        }

        // 更新垂直地面的点位置
        const groundPoint = verticalLineEntities.value.find(e =>
          e.id === `vertical_${selectedPointIndex}_point`
        );
        if (groundPoint?.position) {
          groundPoint.position = new Cesium.ConstantPositionProperty(
            Cesium.Cartesian3.fromRadians(
              cartographic.longitude,
              cartographic.latitude,
              0 // 地面高度
            )
          );
          groundPoint.show = true; // 确保地面点显示
        }

        // 更新航点之间的连线
        updatelinePositions();

        // 强制触发场景渲染
        viewer.scene.requestRender();
      }
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
  // 鼠标释放事件
  handler.setInputAction((movement: any) => {
    if (!isDrawingEnabled.value) return;
    if (isDraggingPoint && selectedPoint && selectedPointIndex !== null) {
      isDraggingPoint = false; // 标记结束拖拽
      viewer.scene.screenSpaceCameraController.enableInputs = true; // 恢复地图拖拽

      // 显示被拖拽航点两侧的标签
      if (selectedPointIndex > 0) {
        distanceLabels.value[selectedPointIndex - 1].show = true;
      }
      if (selectedPointIndex < distanceLabels.value.length) {
        distanceLabels.value[selectedPointIndex].show = true;
      }

      // 更新标签的位置和内容
      const newPosition = selectedPoint.position?.getValue();
      if (selectedPointIndex > 0 && newPosition) {
        const prevPoint = activeShapePoints[selectedPointIndex - 1];
        const { distanceLabel, distanceNumber } = createDistanceLabel(prevPoint, newPosition);
        viewer.entities.remove(distanceLabels.value[selectedPointIndex - 1]);
        distanceLabels.value[selectedPointIndex - 1] = distanceLabel;
        sumFlyDistance.value += Number(distanceNumber);
      }

      if (selectedPointIndex < activeShapePoints.length - 1 && newPosition) {
        const nextPoint = activeShapePoints[selectedPointIndex + 1];
        const { distanceLabel, distanceNumber } = createDistanceLabel(newPosition, nextPoint);
        viewer.entities.remove(distanceLabels.value[selectedPointIndex]);
        distanceLabels.value[selectedPointIndex] = distanceLabel;
        sumFlyDistance.value += Number(distanceNumber);
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_UP);
  // 鼠标按下+键盘按住ALT事件
  handler.setInputAction((event: any) => {
    if (!isDrawingEnabled.value) return;
    const pick = viewer.scene.pick(event.position);
    if (pick?.id?.description?._value === "waypoint" && is3D.value) {
      selectedPoint = pick.id;
      selectedPointIndex = Number(pick.id.id) - 1;
      isAltDrag = true; // 标记为高度拖拽模式

      // 记录初始高度和鼠标位置
      const position = pick.id.position.getValue(Cesium.JulianDate.now());
      const cartographic = Cesium.Cartographic.fromCartesian(position);
      dragStartHeight = cartographic.height;
      dragStartY = event.position.y;

      // 禁用相机控制
      viewer.scene.screenSpaceCameraController.enableInputs = false;

      // 隐藏相关标签
      [selectedPointIndex - 1, selectedPointIndex].forEach(i => {
        if (i >= 0 && i < distanceLabels.value.length) {
          distanceLabels.value[i].show = false;
        }
      });
    }
  }, Cesium.ScreenSpaceEventType.LEFT_DOWN, Cesium.KeyboardEventModifier.ALT);
  // 鼠标释放+键盘按住ALT事件
  handler.setInputAction(() => {
    if (!isAltDrag) return;
    if (isAltDrag && selectedPoint && selectedPointIndex !== null) {
      isAltDrag = false; // 标记结束拖拽
      viewer.scene.screenSpaceCameraController.enableInputs = true; // 恢复地图拖拽
      // 显示被拖拽航点两侧的标签
      if (selectedPointIndex > 0) {
        distanceLabels.value[selectedPointIndex - 1].show = true;
      }
      if (selectedPointIndex < distanceLabels.value.length) {
        distanceLabels.value[selectedPointIndex].show = true;
      }

      // 更新标签的位置和内容
      const newPosition = selectedPoint.position?.getValue();
      if (selectedPointIndex > 0 && newPosition) {
        const prevPoint = activeShapePoints[selectedPointIndex - 1];
        const { distanceLabel, distanceNumber } = createDistanceLabel(prevPoint, newPosition);
        viewer.entities.remove(distanceLabels.value[selectedPointIndex - 1]);
        distanceLabels.value[selectedPointIndex - 1] = distanceLabel;
        sumFlyDistance.value += Number(distanceNumber);
      }

      if (selectedPointIndex < activeShapePoints.length - 1 && newPosition) {
        const nextPoint = activeShapePoints[selectedPointIndex + 1];
        const { distanceLabel, distanceNumber } = createDistanceLabel(newPosition, nextPoint);
        viewer.entities.remove(distanceLabels.value[selectedPointIndex]);
        distanceLabels.value[selectedPointIndex] = distanceLabel;
        sumFlyDistance.value += Number(distanceNumber);
      }
      if (selectedPointIndex !== null) {
        const lineIndex = verticalLineEntities.value.findIndex(line =>
          line.id === `verticalLine_${selectedPointIndex}`
        );
        if (lineIndex > -1) {
          viewer.entities.remove(verticalLineEntities.value[lineIndex]);
          verticalLineEntities.value.splice(lineIndex, 1);
        }
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_UP, Cesium.KeyboardEventModifier.ALT);
  // 按住ALT并移动鼠标事件
  handler.setInputAction((movement: any) => {
    if (isAltDrag && selectedPointIndex !== null) {
      // 计算高度变化
      const deltaY = movement.endPosition.y - dragStartY;
      const sensitivity = 1; // 高度变化灵敏度
      const newHeight = dragStartHeight - deltaY * sensitivity;

      // 更新点高度
      const cartographic = Cesium.Cartographic.fromCartesian(activeShapePoints[selectedPointIndex]);
      cartographic.height = Math.max(newHeight, 0); // 限制最小高度
      // 生成新坐标（保持经纬度不变）
      const newPosition = Cesium.Cartesian3.fromRadians(
        cartographic.longitude,
        cartographic.latitude,
        cartographic.height
      );

      // 更新航点位置
      activeShapePoints[selectedPointIndex] = newPosition;
      selectedPoint && (selectedPoint.position = new Cesium.ConstantPositionProperty(newPosition, Cesium.ReferenceFrame.FIXED));
      waypointLatLngArray.value[selectedPointIndex].altitude = cartographic.height;
      heightArrParams.value[selectedPointIndex] = cartographic.height;

      // 更新垂直线
      const verticalLine = verticalLineEntities.value.find(e =>
        e.id === `vertical_${selectedPointIndex}_line`
      );
      if (verticalLine?.polyline) {
        verticalLine.polyline.positions = new Cesium.CallbackProperty(() => [
          newPosition,
          Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            0
          )
        ], false);
      }

      // 更新高度标签
      const heightLabel = verticalLineEntities.value.find(e =>
        e.id === `vertical_${selectedPointIndex}_label`
      );
      if (heightLabel?.label) {
        (heightLabel.label as Cesium.LabelGraphics).text = new Cesium.ConstantProperty(
          `${cartographic.height.toFixed(0)}m`
        );
        (heightLabel.label as Cesium.LabelGraphics).text = new Cesium.ConstantProperty(
          `${cartographic.height.toFixed(0)}m`
        );
      }
      if (heightLabel) {
        heightLabel.position = new Cesium.CallbackProperty(() => {
          return Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            cartographic.height / 2
          );
        }, false) as unknown as Cesium.PositionProperty;
      }
      // **更新航点之间的连线**
      updatelinePositions();

      // 强制触发场景渲染
      viewer.scene.requestRender();
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE, Cesium.KeyboardEventModifier.ALT);
}
//获取高度
function getHeight(value: number[]) {
  heightArrParams.value = [...value] // 保持响应式
}
// 创建航点，支持在指定位置插入
function createPoint(worldPosition: Cesium.Cartesian3, index?: number) {
  const label = index !== undefined ? (index + 1).toString() : pointCounter.value.toString();
  const point = viewer!.entities.add({
    id: label, // 使用 label 作为 id
    position: Cesium.Cartesian3.fromRadians(
      Cesium.Cartographic.fromCartesian(worldPosition).longitude,
      Cesium.Cartographic.fromCartesian(worldPosition).latitude,
      Cesium.Cartographic.fromCartesian(worldPosition).height || height,
    ),
    point: { // 新增点属性配置
      heightReference: Cesium.HeightReference.NONE,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    label: {
      text: label,
      font: 'bold 14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'),
      showBackground: true,
      backgroundPadding: new Cesium.Cartesian2(2, 2),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    billboard: {
      image: billImage,
      width: 35,
      height: 35,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    description: 'waypoint',
  });
  drawVerticalLine(worldPosition, label);
  const cartographicPosition = Cesium.Cartographic.fromCartesian(worldPosition);
  const latitude = Cesium.Math.toDegrees(cartographicPosition.latitude);
  const longitude = Cesium.Math.toDegrees(cartographicPosition.longitude);
  const altitude = cartographicPosition.height;
  waypointLatLngArray.value.push({ latitude, longitude, altitude });
  pointEntities.value.push(point);
  pointCounter.value++;
  return point;
}
// 绘制航线
function drawShape(positionData: Cesium.CallbackProperty) {
  activeShape.value = viewer!.entities.add({
    polyline: {
      positions: positionData,
      width: 5,
      material: Cesium.Color.YELLOW,
      clampToGround: !is3D.value,
      arcType: Cesium.ArcType.RHUMB, // 支持三维空间曲线
      depthFailMaterial: Cesium.Color.RED.withAlpha(0.5),
      shadows: Cesium.ShadowMode.ENABLED,
    },
    description: 'wayline',
  });
  return activeShape.value;
}
// 更新航线位置
function updatelinePositions() {
  const clampToGroundValue = !is3D.value; // 动态变量，根据视角切换

  // 遍历点数组，逐对绘制线条
  for (let i = 0; i < activeShapePoints.length - 1; i++) {
    const start = activeShapePoints[i];
    const end = activeShapePoints[i + 1];
    const lineId = `line_${i}`;

    // 检查是否已有该线条
    let existingLine = viewer.entities.getById(lineId);
    if (!existingLine) {
      viewer.entities.add({
        id: lineId,
        polyline: {
          positions: new Cesium.CallbackProperty(() => [start, end], false),
          width: 5,
          material: Cesium.Color.YELLOW,
          clampToGround: clampToGroundValue,
          arcType: Cesium.ArcType.RHUMB,
          shadows: Cesium.ShadowMode.ENABLED,
        },
        description: 'wayline',
      });
    } else {
      existingLine.polyline!.positions = new Cesium.CallbackProperty(() => [start, end], false);
      existingLine.polyline!.clampToGround = new Cesium.ConstantProperty(clampToGroundValue);
    }
  }

  const totalLines = activeShapePoints.length - 1;
  let extraLineIndex = totalLines;
  while (true) {
    const extraLine = viewer.entities.getById(`line_${extraLineIndex}`);
    if (!extraLine) break;
    viewer.entities.remove(extraLine);
    extraLineIndex++;
  }
  viewer.scene.requestRender()
}
// 计算两点之间的距离
function calculateDistance(p1: Cesium.Cartesian3, p2: Cesium.Cartesian3): number {
  return Cesium.Cartesian3.distance(p1, p2);
}

// 创建距离标签，显示在两点之间，并将距离(number)返回出去
function createDistanceLabel(p1: Cesium.Cartesian3, p2: Cesium.Cartesian3) {
  const midpoint = Cesium.Cartesian3.add(p1, p2, new Cesium.Cartesian3());
  Cesium.Cartesian3.multiplyByScalar(midpoint, 0.5, midpoint);

  const distance = calculateDistance(p1, p2);
  const distanceNumber = distance.toFixed(2);
  const label = `${distanceNumber} m`;

  // 使用 CallbackProperty 动态更新标签位置
  const positionProperty = new Cesium.CallbackProperty(() => {
    const midpoint = Cesium.Cartesian3.add(p1, p2, new Cesium.Cartesian3());
    Cesium.Cartesian3.multiplyByScalar(midpoint, 0.5, midpoint);
    return midpoint;
  }, false) as unknown as Cesium.PositionProperty;
  const distanceLabel = viewer!.entities.add({
    position: positionProperty, // 动态更新位置
    label: {
      text: label,
      font: '12px sans-serif',
      fillColor: Cesium.Color.BLACK,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 1,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.TOP,
      eyeOffset: new Cesium.Cartesian3(0, 0, 1),
      showBackground: true,
      backgroundColor: Cesium.Color.WHITE.withAlpha(0.8),
      backgroundPadding: new Cesium.Cartesian2(2, 5),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
  });

  return { distanceLabel, distanceNumber };
}

// 生成机场范围椭圆的轮廓点
function computeEllipsePositions(
  center: { longitude: number; latitude: number },
  semiMajorAxis: number,
  semiMinorAxis: number,
  granularity: number
): Cesium.Cartesian3[] {
  const positions: Cesium.Cartesian3[] = [];
  for (let i = 0; i < 360; i += granularity) {
    const radians = Cesium.Math.toRadians(i);
    const x = semiMajorAxis * Math.cos(radians);
    const y = semiMinorAxis * Math.sin(radians);
    // 将米转换为经纬度
    const position = Cesium.Cartesian3.fromDegrees(center.longitude + x / 111319.9, center.latitude + y / 111319.9);
    positions.push(position);
  }
  // 闭合椭圆，使首尾相连
  positions.push(positions[0]);
  return positions;
}

// 绘制机场点位及范围
function drawAirportPoint(points: Api.Airport.MachineNestInfo[]) {
  points.forEach((point: Api.Airport.MachineNestInfo) => {
    const { longitude, latitude, distance, deviceName } = point;
    const center = { longitude, latitude }; // 明确指定中心点类型

    // 生成椭圆边界点
    const positions = computeEllipsePositions(center, distance, distance, 5);

    // 绘制机场范围（用Polygon模拟椭圆）
    viewer.entities.add({
      polygon: {
        hierarchy: positions,  // 多边形的顶点
        material: Cesium.Color.fromCssColorString('#1177fb').withAlpha(0.15), // 设置填充颜色
        perPositionHeight: false,  // 使多边形所有点贴地
      },
      description: 'airpoint',
    });

    // 绘制椭圆边框
    viewer.entities.add({
      polyline: {
        positions: positions,  // 线的顶点位置，与多边形顶点一致
        width: 5,  // 边框宽度
        material: Cesium.Color.fromCssColorString('#1177fb'),  // 边框颜色
        clampToGround: true,  // 使边框贴地
      },
      description: 'airpointborder',
    });

    // 绘制机场图标
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      billboard: {
        image: marker,
        scale: 0.13
      },
      description: 'airpointicon',
    });

    // 绘制机场标签
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      label: {
        text: deviceName,
        font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
        fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
        outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 8.0,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        pixelOffset: new Cesium.Cartesian2(0, -50), // 向上偏移50像素
      },
      description: 'airpointtext',
    });
  });
}

// 清除绘制的航点和航线
function clearEntities() {
  // 清除之前绘制的点
  pointEntities.value.forEach((entity) => {
    viewer!.entities.remove(entity);
  });
  pointEntities.value = []; // 清空点实体数组

  // 清除之前绘制的线
  if (activeShape.value) {
    viewer!.entities.remove(activeShape.value);
    activeShape.value = null;
  }

  // 清除距离标签
  distanceLabels.value.forEach((label) => {
    viewer!.entities.remove(label);
  });
  distanceLabels.value = [];
}

// 获取已激活的机场列表
async function getAirportDeviceList() {
  const { data } = await fetchAirportDeviceList();
  Object.assign(airportDeviceList, data.rows);
  drawAirportPoint(data.rows);
}

// 控制航点设置弹窗的展示
function handelAirlineSetModalShow(flag: boolean) {
  airlineSetModalShow.value = flag;
}

// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
  try {
    const response = await fetch('/defaultNoFlyZone.json');
    const data = await response.json();
    // 绘制
    addPolygons(data.features);
  } catch (error) {
    console.error('Error loading JSON data:', error);
  }
};

// 绘制禁飞区
function addPolygons(features: any[]) {
  features.forEach(feature => {
    const polygon = feature.geometry.coordinates[0];
    const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    viewer.entities.add({
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString('red').withAlpha(0.35),
      },
      rectangle: { zIndex: 1 },
      description: 'nofly',
    });
  });
};

// ceium截图功能
async function takeScreenshot(): Promise<{ url: string }> {
  let screenshot;

  return new Promise((resolve, reject) => {
    const postRenderHandler = async function () {
      //screenshot = viewer.scene.canvas.toDataURL();

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      canvas.width = viewer.scene.canvas.width / 2;
      canvas.height = viewer.scene.canvas.height / 2;

      ctx?.drawImage(viewer.scene.canvas, 0, 0, canvas.width, canvas.height);

      let compressedScreenshotUrl = canvas.toDataURL('image/png', 0.7);
      localStorage.setItem("newCoverBase64", compressedScreenshotUrl);
      viewer.scene.postRender.removeEventListener(postRenderHandler);
      const { data, error } = await uploadBase64Common(compressedScreenshotUrl);

      resolve({ url: data.newFileName });
    };

    viewer.scene.postRender.addEventListener(postRenderHandler);

    // 等待一帧渲染完成
    requestAnimationFrame(() => {
      viewer.scene.postRender.removeEventListener(postRenderHandler);
    });
  });
}

// 保存
const onSaveAirLine = async (lineInfo: { surfaceRelativeHeight: string, transitionalSpeed: string, flightName: string }) => {
  message.info("正在保存...");

  const { url } = await takeScreenshot()


  const { surfaceRelativeHeight, transitionalSpeed, flightName } = lineInfo;

  let flightPointDTOList: { geoJson: { type: "Point", coordinates: Api.AirLine.WayPointList }, flightPointActionDTOList: [] }[] = []
  let arr: any[] = JSON.parse(JSON.stringify(airLineCreateRef.value.wayActionArr.values));

  flightPointDTOList = arr.map((actions, index) => {
    const coordinates = waypointLatLngArray.value[index];
    const processedActions = actions.map((item: { actionActuatorFunc: string; params: { payloadLensIndex: any[] | string; }; }) => {
      if (item && (item.actionActuatorFunc === 'gimbalRotate1' || item.actionActuatorFunc === 'gimbalRotate2')) {
        item.actionActuatorFunc = 'gimbalRotate';
      }
      if (item.actionActuatorFunc === "takePhoto" || item.actionActuatorFunc === "startRecord") {
        // item.params.payloadLensIndex = item.params.payloadLensIndex.join(',')
        if (Array.isArray(item.params.payloadLensIndex)) {
          item.params.payloadLensIndex = item.params.payloadLensIndex.join(','); // 将数组转换为字符串
        }
      }
      return item;
    });
    const heightParams = [...heightArrParams.value][index]
    return {
      height: heightParams,
      geoJson: {
        type: "Point", coordinates
      },
      flightPointActionDTOList: processedActions
    };
  });
  const params = {
    flightType: 1,
    flightImage: url,
    surfaceRelativeHeight: surfaceRelativeHeight,
    transitionalSpeed: transitionalSpeed,
    flightName: flightName,
    flightPointDTOList: flightPointDTOList,
    ...(editAirLineID ? { flightId: editAirLineID.value } : {})
  };
  const { error } = await sendAirLineSaveInfo(params);
  if (!error) {
    message.success('保存成功');
    router.back();
  } else {
    airLineInfoRef.value.changeButtonLoading(false)
  }
}
// 创建节流包装器函数
const debouncedSave = (() => {
  let timer: ReturnType<typeof setTimeout> | null = null;
  let pendingExecution: (() => Promise<void>) | null = null;

  return async (lineInfo: Parameters<typeof onSaveAirLine>[0]) => {
    // 保存最新的调用
    pendingExecution = () => onSaveAirLine(lineInfo);

    // 清除之前的定时器
    if (timer) {
      clearTimeout(timer);
    }

    // 设置新的定时器
    timer = setTimeout(async () => {
      if (pendingExecution) {
        const exec = pendingExecution;
        pendingExecution = null;
        await exec();
      }
      timer = null;
    }, 500); // 500ms防抖延迟
  };
})();

// 使用方式
const handleClickThrottle = debouncedSave;
const airLineConfig = ref<Api.AirLine.AirLineConfig>({}); // 整条航线基本信息
const airPointActionArr = reactive<any[]>([]); // 各个航点的动作

// 修改更新航点动作的函数
function updateWayActions(newActions: any[]) {
  // 确保每个航点的动作数组都是独立的副本
  const updatedActions = newActions.map(actions => Array.isArray(actions) ? [...actions] : []);
  airPointActionArr.splice(0, airPointActionArr.length, ...updatedActions);
}

// getAirLine 函数
async function getAirLine(editAirLineID: any) {
  const { data } = await fetchAirLineSaveInfo(editAirLineID);
  const { surfaceRelativeHeight, transitionalSpeed, flightName, flightPointDTOList } = data;
  // 反显整条航线基本信息
  airLineConfig.value.surfaceRelativeHeight = surfaceRelativeHeight;
  airLineConfig.value.transitionalSpeed = transitionalSpeed;
  airLineConfig.value.flightName = flightName;

  // 反显 flightPointDTOList 数据
  const wayActionArr = flightPointDTOList.map((point: { flightPointActionDTOList: any[]; }) => {
    return point.flightPointActionDTOList.map(action => {
      const newAction = { ...action };
      if (newAction.actionActuatorFunc === 'gimbalRotate') {
        if (newAction.params.gimbalPitchRotateEnable !== undefined) {
          newAction.actionActuatorFunc = 'gimbalRotate1';
        } else if (newAction.params.gimbalYawRotateEnable !== undefined) {
          newAction.actionActuatorFunc = 'gimbalRotate2';
        }
      }
      return newAction;
    });
  });

  const arr = flightPointDTOList.map((item: { height: any; }) => item.height);
  heightArrParams.value = [...arr]

  // 使用深拷贝更新 airPointActionArr
  const updatedActions = wayActionArr.map((actions: any) => [...actions]);
  airPointActionArr.splice(0, airPointActionArr.length, ...updatedActions);

  // 更新坐标
  const waypointarr = flightPointDTOList.map((point: { geoJson: { coordinates: any; }, height: number; }) => {
    const { longitude, latitude } = point.geoJson.coordinates;
    return { longitude, latitude, altitude: point.height };
  });
  // 如果有航点，将地图中心跳转到第一个点
  if (waypointarr.length > 0) {
    const { latitude, longitude } = waypointarr[0];

    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, 8000), // 经度、纬度、高度
      duration: 1.5 // 动画持续时间（秒）
    });
  }

  redrawLineRoad(waypointarr); // 重新绘制点和线
}

// 根据更新后的 `waypointLatLngArray` 重新绘制点和线
function redrawLineRoad(waypointarr: Api.AirLine.WayPointList[]) {
  // 遍历 waypointLatLngArray，重新生成点和线
  waypointarr.forEach((point, index) => {
    const { latitude, longitude, altitude } = point;
    const cartesianPosition = Cesium.Cartesian3.fromDegrees(longitude, latitude, altitude);

    if (index === 0) {
      // 第一个点，创建浮动点并开始绘制线
      floatingPoint.value = createPoint(cartesianPosition);
      activeShapePoints.push(cartesianPosition);
    } else {
      // 添加新的点并计算距离
      const lastPoint = activeShapePoints[activeShapePoints.length - 1];
      if (lastPoint) {
        const { distanceLabel, distanceNumber } = createDistanceLabel(lastPoint, cartesianPosition);
        distanceLabels.value.push(distanceLabel); // 将距离标签添加到数组中
        sumFlyDistance.value = (sumFlyDistance.value + Number(distanceNumber));
      }
      activeShapePoints.push(cartesianPosition); // 添加新的点
      createPoint(cartesianPosition); // 绘制新点
    }
  });
  // 绘制线，动态更新线的坐标
  const dynamicPositions = new Cesium.CallbackProperty(() => {
    return activeShapePoints;
  }, false);
  drawShape(dynamicPositions); // 调用绘制线函数

}

//  ---------------3d相关   start-----------------------------
// 切换2d和3d视图
const is3D = ref(false);
const toggleViewMode = () => {
  // 获取屏幕中心点地理坐标
  const centerPixel = new Cesium.Cartesian2(
    viewer.canvas.clientWidth / 2,
    viewer.canvas.clientHeight / 2
  );
  const worldPosition = viewer.scene.camera.pickEllipsoid(centerPixel);
  if (worldPosition) {
    const cartographic = Cesium.Ellipsoid.WGS84.cartesianToCartographic(worldPosition);
    const centerLng = Cesium.Math.toDegrees(cartographic.longitude);
    const centerLat = Cesium.Math.toDegrees(cartographic.latitude);
    if (is3D.value) {
      viewer.scene.mode = Cesium.SceneMode.SCENE2D;
      const surfaceHeight = Cesium.Ellipsoid.WGS84.cartesianToCartographic(worldPosition).height;
      const adjustedAlt = Math.max(surfaceHeight * 1.5, 7000);
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(centerLng, centerLat, adjustedAlt),
        orientation: {
          pitch: -Cesium.Math.PI_OVER_TWO,
          heading: viewer.camera.heading
        },
        duration: 2
      });
      // 隐藏标签垂直线地面点信息
      verticalLineEntities.value.forEach(e => {
        e.show = false;
      });
    } else {
      viewer.scene.mode = Cesium.SceneMode.SCENE3D;
      const targetPitch = Cesium.Math.toRadians(-15);
      const surfaceHeight = Cesium.Ellipsoid.WGS84.cartesianToCartographic(worldPosition).height;
      const adjustedAlt = Math.max(surfaceHeight * 1.5, 1500);
      viewer.camera.flyTo({
        destination: Cesium.Cartesian3.fromDegrees(centerLng, centerLat - 0.03, adjustedAlt),
        orientation: {
          pitch: targetPitch,
          heading: viewer.camera.heading
        },
        duration: 2
      });
      // 展示标签垂直线地面点信息
      verticalLineEntities.value.forEach(e => {
        e.show = true;
      });
    }
  }
  is3D.value = !is3D.value;
  updatelinePositions();
  if (activeShape.value) {
    viewer.entities.remove(activeShape.value);
    const dynamicPositions = new Cesium.CallbackProperty(() => {
      return activeShapePoints;
    }, false);
    drawShape(dynamicPositions); // 重新调用 drawShape
  }
  viewer.scene.requestRender();
};
// 绑定反向盘组件
const dashboardRef = ref();
// 用于存储所有垂直线实体
const verticalLineEntities = ref<Cesium.Entity[]>([]);
// 绘制垂直线
function drawVerticalLine(position: Cesium.Cartesian3, index: number | string) {
  // 删除旧实体组
  removeVerticalLine(Number(index) - 1);

  // 获取位置的经纬度和高度
  const cartographic = Cesium.Cartographic.fromCartesian(position);
  const centerLng = Cesium.Math.toDegrees(cartographic.longitude);
  const centerLat = Cesium.Math.toDegrees(cartographic.latitude);
  const height = cartographic.height;

  // 创建垂直线的动态位置回调
  const positionsCallback = new Cesium.CallbackProperty(() => {
    return Cesium.Cartesian3.fromDegreesArrayHeights([
      centerLng,
      centerLat,
      height,
      centerLng,
      centerLat,
      0, // 地面高度
    ]);
  }, false);

  // 创建垂直线实体
  const verticalLine = viewer.entities.add({
    id: `vertical_${Number(index) - 1}_line`,
    show: is3D.value,
    polyline: {
      positions: positionsCallback,
      width: 1,
      material: Cesium.Color.WHITE,
    },
  });

  // 创建地面点实体
  const groundPoint = viewer.entities.add({
    id: `vertical_${Number(index) - 1}_point`,
    position: Cesium.Cartesian3.fromDegrees(centerLng, centerLat, 0),
    show: is3D.value,
    point: {
      pixelSize: 4,
      color: Cesium.Color.WHITE,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
    },
  });

  // 创建高度标签实体
  const heightLabel = viewer.entities.add({
    id: `vertical_${Number(index) - 1}_label`,
    position: Cesium.Cartesian3.fromDegrees(centerLng, centerLat, height / 2),
    show: is3D.value,
    label: {
      text: `${height.toFixed(0)}m`,
      font: 'bold 12px monospace',
      fillColor: Cesium.Color.WHITE,
      // backgroundColor: Cesium.Color.BLACK.withAlpha(0.7),
      // showBackground: true,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
    },
  });

  // 将实体添加到 `verticalLineEntities` 数组
  verticalLineEntities.value.push(verticalLine, groundPoint, heightLabel);
}
// 移除垂直线
function removeVerticalLine(index: number | string) {
  const oldGroup = verticalLineEntities.value.filter(e =>
    e.id?.startsWith(`vertical_${index}_`) // 匹配指定索引的实体
  );
  oldGroup.forEach(e => viewer.entities.remove(e)); // 从 Cesium Viewer 中移除实体
  verticalLineEntities.value = verticalLineEntities.value.filter(e =>
    !e.id?.startsWith(`vertical_${index}_`) // 从数组中移除实体
  );
}
// 修改垂直线高度
function updateVerticalLineHeight(index: number, inputHeight: number) {
  // 更新点高度
  const cartographic = Cesium.Cartographic.fromCartesian(activeShapePoints[index]);

  // 生成新坐标（保持经纬度不变）
  const newPosition = Cesium.Cartesian3.fromRadians(
    cartographic.longitude,
    cartographic.latitude,
    inputHeight
  );
  // 更新所有相关实体
  activeShapePoints[index] = newPosition;
  selectedPoint && (selectedPoint.position = new Cesium.ConstantPositionProperty(newPosition, Cesium.ReferenceFrame.FIXED));
  waypointLatLngArray.value[index].altitude = cartographic.height;
  // 更新航点间连线
  if (index > 0 && newPosition) {
    const prevPoint = activeShapePoints[index - 1];
    const { distanceLabel, distanceNumber } = createDistanceLabel(prevPoint, newPosition);
    viewer.entities.remove(distanceLabels.value[index - 1]);
    distanceLabels.value[index - 1] = distanceLabel;
    sumFlyDistance.value += Number(distanceNumber);
  }

  if (index < activeShapePoints.length - 1 && newPosition) {
    const nextPoint = activeShapePoints[index + 1];
    const { distanceLabel, distanceNumber } = createDistanceLabel(newPosition, nextPoint);
    viewer.entities.remove(distanceLabels.value[index]);
    distanceLabels.value[index] = distanceLabel;
    sumFlyDistance.value += Number(distanceNumber);
  }
  // 更新垂直线
  const verticalLine = verticalLineEntities.value.find(e =>
    e.id === `vertical_${index}_line`
  );
  if (verticalLine?.polyline) {
    verticalLine.polyline.positions = new Cesium.CallbackProperty(() => [
      newPosition,
      Cesium.Cartesian3.fromRadians(
        cartographic.longitude,
        cartographic.latitude,
        0
      )
    ], false);
    viewer.scene.requestRender();
  }
  // 更新高度标签
  const heightLabel = verticalLineEntities.value.find(e =>
    e.id === `vertical_${index}_label`
  );
  if (heightLabel?.label) {
    (heightLabel.label as Cesium.LabelGraphics).text = new Cesium.ConstantProperty(
      `${inputHeight?.toFixed(0)}m`
    );
  }
  if (heightLabel) {
    heightLabel.position = new Cesium.CallbackProperty(() => {
      return Cesium.Cartesian3.fromRadians(
        cartographic.longitude,
        cartographic.latitude,
        inputHeight / 2
      );
    }, false) as unknown as Cesium.PositionProperty;
  }
}
// 控制视角移动
const moveCamera = () => {
  document.addEventListener('keydown', function (e) {
    if (e.key === 'w') {
      dashboardRef.value.moveDrone(viewer, 'forward')
    } else if (e.key === 's') {
      dashboardRef.value.moveDrone(viewer, 'backward')
    } else if (e.key === 'a') {
      dashboardRef.value.moveDrone(viewer, 'left')
    } else if (e.key === 'd') {
      dashboardRef.value.moveDrone(viewer, 'right')
    } else if (e.key === 'q') {
      dashboardRef.value?.rotateDrone('delete', 1, viewer);
    } else if (e.key === 'e') {
      dashboardRef.value?.rotateDrone('add', 1, viewer);
    }
  });
};
//  ---------------3d相关   end-----------------------------
onMounted(() => {
  // 初始化地图
  initCesium();
  // 获取已激活的机场列表
  getAirportDeviceList();
  if (route.query.id) { // 存在航线id则为编辑
    editAirLineID.value = route.query.id;
    getAirLine(editAirLineID.value);
  }
  height = airLineInfoRef.value.surfaceRelativeHeight;
});
onBeforeUnmount(() => {
  // 卸载鼠标右键点击事件
  if (handler) {
    handler.destroy();
    handler = null;
  }
})
</script>

<style scoped>
#container {
  padding: 0;
}

.n-modal .n-card,
.n-drawer .n-card {
  background-color: var(--n-border-color);
}

.dashboard {
  width: 200px;
  height: 200px;
  position: fixed;
  bottom: 10px;
  left: calc(50% - 240px);
}
</style>
