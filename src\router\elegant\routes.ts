/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { GeneratedRoute } from '@elegant-router/types';

export const generatedRoutes: GeneratedRoute[] = [
  {
    name: '403',
    path: '/403',
    component: 'layout.blank$view.403',
    meta: {
      title: '403',
      i18nKey: 'route.403',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '404',
    path: '/404',
    component: 'layout.blank$view.404',
    meta: {
      title: '404',
      i18nKey: 'route.404',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: '500',
    path: '/500',
    component: 'layout.blank$view.500',
    meta: {
      title: '500',
      i18nKey: 'route.500',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'achievement',
    path: '/achievement',
    component: 'layout.base',
    meta: {
      title: 'achievement',
      i18nKey: 'route.achievement',
      icon: 'mdi:chart-line',
      order: 6
    },
    children: [
      {
        name: 'achievement_3dmaterial',
        path: '/achievement/3dmaterial',
        component: 'view.achievement_3dmaterial',
        meta: {
          title: 'achievement_3dmaterial',
          i18nKey: 'route.achievement_3dmaterial',
          order: 4
        }
      },
      {
        name: 'achievement_3dmaterialdetail',
        path: '/achievement/3dmaterialdetail',
        component: 'view.achievement_3dmaterialdetail',
        meta: {
          title: 'achievement_3dmaterialdetail',
          i18nKey: 'route.achievement_3dmaterialdetail',
          hideInMenu: true
        }
      },
      {
        name: 'achievement_overview',
        path: '/achievement/overview',
        component: 'view.achievement_overview',
        meta: {
          title: 'achievement_overview',
          i18nKey: 'route.achievement_overview',
          order: 4
        }
      },
      {
        name: 'achievement_photo',
        path: '/achievement/photo',
        component: 'view.achievement_photo',
        meta: {
          title: 'achievement_photo',
          i18nKey: 'route.achievement_photo',
          order: 1
        }
      },
      {
        name: 'achievement_upload',
        path: '/achievement/upload',
        component: 'view.achievement_upload',
        meta: {
          title: 'achievement_upload',
          i18nKey: 'route.achievement_upload',
          hideInMenu: true
        }
      },
      {
        name: 'achievement_video',
        path: '/achievement/video',
        component: 'view.achievement_video',
        meta: {
          title: 'achievement_video',
          i18nKey: 'route.achievement_video',
          order: 3
        }
      }
    ]
  },
  {
    name: 'airfield',
    path: '/airfield',
    component: 'layout.base$view.airfield',
    meta: {
      title: 'airfield',
      i18nKey: 'route.airfield',
      hideInMenu: true,
      activeMenu: 'homeversiontwo'
    }
  },
  {
    name: 'airspace',
    path: '/airspace',
    component: 'layout.base$view.airspace',
    meta: {
      title: 'airspace',
      i18nKey: 'route.airspace',
      icon: 'mdi:map-marker-path',
      order: 5
    }
  },
  {
    name: 'allairline',
    path: '/allairline',
    component: 'layout.base$view.allairline',
    meta: {
      title: 'allairline',
      i18nKey: 'route.allairline',
      hideInMenu: true,
      activeMenu: 'homeversiontwo'
    }
  },
  {
    name: 'device',
    path: '/device',
    component: 'layout.base',
    meta: {
      title: 'device',
      i18nKey: 'route.device',
      icon: 'mdi:quadcopter',
      order: 7
    },
    children: [
      {
        name: 'device_airport',
        path: '/device/airport',
        component: 'view.device_airport',
        meta: {
          title: 'device_airport',
          i18nKey: 'route.device_airport',
          order: 1
        }
      },
      {
        name: 'device_emergency',
        path: '/device/emergency',
        component: 'view.device_emergency',
        meta: {
          title: 'device_emergency',
          i18nKey: 'route.device_emergency',
          order: 4
        }
      },
      {
        name: 'device_health',
        path: '/device/health',
        component: 'view.device_health',
        meta: {
          title: 'device_health',
          i18nKey: 'route.device_health',
          order: 3
        }
      },
      {
        name: 'device_rc',
        path: '/device/rc',
        component: 'view.device_rc',
        meta: {
          title: 'device_rc',
          i18nKey: 'route.device_rc',
          order: 2
        }
      },
      {
        name: 'device_work',
        path: '/device/work',
        component: 'view.device_work',
        meta: {
          title: 'device_work',
          i18nKey: 'route.device_work',
          order: 5
        }
      }
    ]
  },
  {
    name: 'draw-airline',
    path: '/draw-airline',
    component: 'layout.base$view.draw-airline',
    meta: {
      title: 'draw-airline',
      i18nKey: 'route.draw-airline',
      hideInMenu: true,
      activeMenu: 'airspace'
    }
  },
  {
    name: 'flight',
    path: '/flight',
    component: 'layout.base$view.flight',
    meta: {
      title: 'flight',
      i18nKey: 'route.flight',
      icon: 'mdi:airplane',
      order: 2
    }
  },
  {
    name: 'fly-detail',
    path: '/fly-detail',
    component: 'layout.base$view.fly-detail',
    meta: {
      title: 'fly-detail',
      i18nKey: 'route.fly-detail',
      hideInMenu: true,
      activeMenu: 'flight'
    }
  },
  {
    name: 'flyablearea',
    path: '/flyablearea',
    component: 'layout.base$view.flyablearea',
    meta: {
      title: 'flyablearea',
      i18nKey: 'route.flyablearea',
      hideInMenu: true,
      activeMenu: 'homeversiontwo'
    }
  },
  {
    name: 'flyhistroy',
    path: '/flyhistroy',
    component: 'layout.base$view.flyhistroy',
    meta: {
      title: 'flyhistroy',
      i18nKey: 'route.flyhistroy',
      hideInMenu: true
    }
  },
  {
    name: 'history',
    path: '/history',
    component: 'layout.base$view.history',
    meta: {
      title: 'history',
      i18nKey: 'route.history',
      icon: 'mdi:history',
      order: 3
    }
  },
  {
    name: 'home',
    path: '/home',
    component: 'layout.base$view.home',
    meta: {
      title: 'home',
      i18nKey: 'route.home',
      icon: 'mdi:monitor-dashboard',
      order: 1,
      hideInMenu: true
    }
  },
  {
    name: 'homeversiontwo',
    path: '/homeversiontwo',
    component: 'layout.base$view.homeversiontwo',
    meta: {
      title: 'homeversiontwo',
      i18nKey: 'route.homeversiontwo',
      icon: 'mdi:monitor-dashboard',
      order: 1
    }
  },
  {
    name: 'iframe-page',
    path: '/iframe-page/:url',
    component: 'layout.base$view.iframe-page',
    props: true,
    meta: {
      title: 'iframe-page',
      i18nKey: 'route.iframe-page',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'login',
    path: '/login/:module(pwd-login|code-login|register|reset-pwd|bind-wechat)?',
    component: 'layout.blank$view.login',
    props: true,
    meta: {
      title: 'login',
      i18nKey: 'route.login',
      constant: true,
      hideInMenu: true
    }
  },
  {
    name: 'safety',
    path: '/safety',
    component: 'layout.base$view.safety',
    meta: {
      title: 'safety',
      i18nKey: 'route.safety',
      hideInMenu: true,
      activeMenu: 'homeversiontwo'
    }
  },
  {
    name: 'senior',
    path: '/senior',
    component: 'layout.base',
    meta: {
      title: 'senior',
      i18nKey: 'route.senior',
      hideInMenu: true
    },
    children: [
      {
        name: 'senior_config',
        path: '/senior/config',
        component: 'view.senior_config',
        meta: {
          title: 'senior_config',
          i18nKey: 'route.senior_config',
          hideInMenu: true
        }
      },
      {
        name: 'senior_overview',
        path: '/senior/overview',
        component: 'view.senior_overview',
        meta: {
          title: 'senior_overview',
          i18nKey: 'route.senior_overview',
          hideInMenu: true
        }
      }
    ]
  },
  {
    name: 'system',
    path: '/system',
    component: 'layout.base',
    meta: {
      title: 'system',
      i18nKey: 'route.system',
      icon: 'mdi:cog',
      order: 9
    },
    children: [
      {
        name: 'system_ai',
        path: '/system/ai',
        component: 'view.system_ai',
        meta: {
          title: 'system_ai',
          i18nKey: 'route.system_ai',
          order: 5
        }
      },
      {
        name: 'system_base',
        path: '/system/base',
        component: 'view.system_base',
        meta: {
          title: 'system_base',
          i18nKey: 'route.system_base'
        }
      },
      {
        name: 'system_custom',
        path: '/system/custom',
        component: 'view.system_custom',
        meta: {
          title: 'system_custom',
          i18nKey: 'route.system_custom'
        }
      },
      {
        name: 'system_organization',
        path: '/system/organization',
        component: 'view.system_organization',
        meta: {
          title: 'system_organization',
          i18nKey: 'route.system_organization'
        }
      },
      {
        name: 'system_user',
        path: '/system/user',
        component: 'view.system_user',
        meta: {
          title: 'system_user',
          i18nKey: 'route.system_user'
        }
      }
    ]
  },
  {
    name: 'task',
    path: '/task',
    component: 'layout.base$view.task',
    meta: {
      title: 'task',
      i18nKey: 'route.task',
      icon: 'mdi:list-box-outline',
      order: 4
    }
  },
  {
    name: 'user-center',
    path: '/user-center',
    component: 'layout.base$view.user-center',
    meta: {
      title: 'user-center',
      i18nKey: 'route.user-center',
      hideInMenu: true
    }
  }
];
