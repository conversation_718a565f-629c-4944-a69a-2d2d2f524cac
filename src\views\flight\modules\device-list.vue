<template>
  <n-card content-class="p-0!">
    <div class="font-size-18px text-light pl-10px pt-20px pb-20px  font-700">机场列表</div>
    <n-scrollbar style="max-height: calc(100vh - 80px)" >

    <div v-for="(item, index) in airportDeviceList">
      <n-card
        :class="[
          'device_item cursor-pointer pl-13px py-8px m-y-8px relative bg-dark! border-l-4px border-l-#1a1b1c hover-border-blue',
          isInVideoBoxes(item) ? 'border-blue' : ''
        ]" embedded :bordered="false" content-class="p-0!">
        <div @click="onClickAirPort(index)">
          <n-text class="font-size-16px text-light">{{ item.deviceName }}</n-text>
          <div class="mt-8px mb-8px">
            <n-tag size="small" v-if="item.host?.modeCode === '0'" type="success">空闲中</n-tag>
            <n-tag size="small" v-else-if="item.host?.modeCode === '1' || item.host?.modeCode === '2'" type="warning">
              {{ item.host?.modeCode === '1' ? '现场调试' : '远程调试' }}
            </n-tag>
            <n-tag size="small" v-else-if="item.host?.modeCode === '3'" type="error">固件升级中</n-tag>
            <n-tag size="small" v-else-if="item.host?.modeCode === '4'" type="info">作业中</n-tag>
            <n-tag size="small" v-else>离线</n-tag>
            <n-text class="text-light ml-5px"> {{ item.host?.taskName || '暂无任务' }}</n-text>
          </div>
          <div>
            <n-tag size="small" :bordered="false"
              :type="deviceStore.droneStatusList[Number(item.childDevice?.host?.modeCode)]?.listClass || 'default'">
              {{ deviceStore.droneStatusList[Number(item.childDevice?.host?.modeCode)]?.dictLabel || '离线' }}
            </n-tag>

            <n-text class="text-light pl-5px">{{ item?.childDevice?.droneName }}</n-text>
          </div>
        </div>
        <div @click="onFocusAirport(index)"
          class="locate_icon absolute bg-blue-5 w-15 h-100% top-0 right-0 grid place-items-center opacity-0">
          <n-icon class="cursor-pointer  line-height-100%" size="20" color="#ffffff" :component="Locate" />
        </div>
      </n-card>
    </div>
  </n-scrollbar>

  </n-card>
</template>

<script setup lang="ts">
import { PropType, Ref, computed, inject, nextTick, onActivated, onMounted, onUpdated, ref, watch, watchEffect } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { Locate } from '@vicons/ionicons5'
import { useAirportStore } from '@/store/modules/airport';
import { useSocketStore } from '@/store/modules/socket';
import { useDeviceStore } from '@/store/modules/device';
import { useMessage } from 'naive-ui';
// import useSocket from '@/service/websocket/useSocket';
import { localStg } from '@/utils/storage';
// import { router } from '@/router';
import { useRouter } from 'vue-router';

const socket = inject<{ chatMessage: any, message: any }>('useSocket');  // 通过 inject 获取 provide 的 socket 对象

const router = useRouter();
const airportStore = useAirportStore();

const deviceStore = useDeviceStore();
const socketStore = useSocketStore();
const message = useMessage();
const reload = inject("reload");


defineOptions({ name: 'DeviceList' });
const emits = defineEmits(['focus-airport']);


// 定义组件接受的属性
const props = defineProps<{
  airportDeviceList: Api.Airport.AirportDeviceInfo[];
}>();

const newDeviceList = ref<Api.Airport.AirportDeviceInfo[]>();

async function onClickAirPort(index: number) {
  if (props.airportDeviceList[index].host && props.airportDeviceList[index].host?.modeCode) {
    airportStore.addVideoBox(props.airportDeviceList[index]);
  } else {
    message.warning('设备离线中，请先开启设备');
  }
}

// 选择某个机场
function onFocusAirport(index: number) {
  emits('focus-airport', index);
  // airportStore.addVideoBox(props.airportDeviceList[index]);
}

// 处理socket消息
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  // console.log("onProcessMessage ~ { biz_code, data, sn }:", { biz_code, data, sn })
  if (biz_code === 'device_osd') { // 无人机消息
    const index = props.airportDeviceList.findIndex(item => item.childDevice && item.childDevice.droneSn === sn);
    // console.log("onProcessMessage ~ biz_code:", index)

    if (index !== -1 && newDeviceList.value) { // 替换找到的项
      const item = newDeviceList.value[index];
      // const { modeCode } = data.host;
      let newItem = { childDevice: { ...item.childDevice, host: data.host } };
      // newItem.childDevice = { ...newItem.childDevice, }
      newDeviceList.value[index] = { ...item, ...newItem };
    }
  } else if (biz_code === 'dock_osd') { // 机场消息
    const index = props.airportDeviceList.findIndex(item => item.deviceSn === sn);

    if (index !== -1 && newDeviceList.value) { // 替换找到的项
      // const { modeCode } = data.host;
      let newItem = { ...newDeviceList.value[index], host: data.host || {} };
      // console.log("onProcessMessage ~ index:", newItem)
      newDeviceList.value[index] = { ...newDeviceList.value[index], ...newItem };
    }
  } else if (biz_code === 'device_hms') { // 设备告警
    airportStore.setDeviceEmergency(msg.data);
  }
}

// 监视 WebSocket 消息变化
watch(() => socketStore?.chatMessage.value, (newVal, oldVal) => {
  if (newVal) {
    onProcessMessage(newVal);
    // console.log("page watch ~ newValnewValnewValnewVal:", newVal)
  }
}, { deep: true, immediate: true })


onMounted(async () => {
  newDeviceList.value = props.airportDeviceList;
  // await nextTick(); // 等待下一次 DOM 更新
})

// 判断机场是否已打开播放
const isInVideoBoxes = (item: Api.Airport.AirportDeviceInfo) => {
  return airportStore.videoBoxes.some(videoBox => videoBox.id === item.id);
};

</script>

<style scoped>
.device_item:hover {
  .locate_icon {
    opacity: 1;
  }
}
</style>
