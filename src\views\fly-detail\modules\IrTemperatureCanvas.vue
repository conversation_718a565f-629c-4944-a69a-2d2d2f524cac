<template>
  <div
    v-if="irMeteringMode && !screenSplitEnable"
    class="absolute inset-0 pointer-events-none"
    :style="{ zIndex: 4 }"
  >
    <!-- 红外测温画布 -->
    <canvas
      ref="canvasIRRef"
      class="absolute pointer-events-auto cursor-crosshair"
      :style="canvasStyle"
      @mousedown="onMouseDown"
      @mousemove="onMouseMove"
      @mouseup="onMouseUp"
      @contextmenu="onRightClick"
    />
    
    <!-- 拖选提示 -->
    <div 
      v-if="isDragging"
      class="absolute top-4 left-4 bg-black bg-opacity-70 text-white px-8px py-4px rd-4px text-12px"
    >
      拖选测温区域
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick, computed } from 'vue';

// 定义接口
interface TemperaturePoint {
  x: number; // 相对坐标 0-1
  y: number; // 相对坐标 0-1
  temperature: number;
}

interface IrMeteringArea {
  width: number; // 相对宽度 0-1
  height: number; // 相对高度 0-1
  maxTemperaturePoint: TemperaturePoint;
  minTemperaturePoint: TemperaturePoint;
}

interface Props {
  irMeteringMode: boolean;
  irMeteringArea?: IrMeteringArea;
  screenSplitEnable: boolean;
  containerWidth?: number;
  containerHeight?: number;
  currentScreenStatus?: number; // 当前屏幕状态：1=小窗口，2=全屏
  videoWidth?: number; // 实际视频宽度
  videoHeight?: number; // 实际视频高度
}

const props = withDefaults(defineProps<Props>(), {
  irMeteringMode: false,
  irMeteringArea: undefined,
  screenSplitEnable: false,
  containerWidth: 640,
  containerHeight: 480,
  currentScreenStatus: 1,
  videoWidth: 1280,
  videoHeight: 720
});

// 定义事件
const emit = defineEmits<{
  'area-selected': [area: { x: number; y: number; width: number; height: number }];
  'reset-fullscreen': [];
}>();

// 响应式数据
const canvasIRRef = ref<HTMLCanvasElement>();
const isDragging = ref(false);
const dragStart = ref({ x: 0, y: 0 });
const dragEnd = ref({ x: 0, y: 0 });

// Canvas上下文
let ctx: CanvasRenderingContext2D | null = null;

// 视频尺寸适配计算
const videoAdaptation = computed(() => {
  const originalWidth = props.videoWidth || 1280;
  const originalHeight = props.videoHeight || 720;

  // 计算目标显示区域的宽高比（16:9）
  const targetRatio = 1280 / 720;

  // 计算原始视频的宽高比
  const originalRatio = originalWidth / originalHeight;

  // 判断视频类型：基于宽高比而非绝对尺寸
  const ratio43 = 4 / 3; // 4:3宽高比
  const needsCentering = Math.abs(originalRatio - ratio43) < Math.abs(originalRatio - targetRatio);

  // 计算视频在720高度下的缩放宽度
  const scaledWidth = needsCentering ? Math.round(originalHeight * (originalWidth / originalHeight)) : 1280;

  // 计算居中偏移量
  const offsetX = needsCentering ? Math.floor((1280 - scaledWidth) / 2) : 0;

  return {
    needsCentering,
    scaledWidth,
    offsetX,
    originalWidth,
    originalHeight
  };
});

// Canvas样式计算属性 - 根据屏幕状态适配缩放
const canvasStyle = computed(() => {
  const baseStyle = {
    width: '1280px',
    height: '720px'
  };

  if (props.currentScreenStatus === 1) {
    // 小窗口状态 - 应用缩放
    const scale = 416 / 1280; // 与handelVideoScreen中的缩放比例保持一致
    return {
      ...baseStyle,
      position: 'absolute' as const,
      top: '0',
      left: '0',
      transform: `scale(${scale})`,
      transformOrigin: '0 0'
    };
  } else if (props.currentScreenStatus === 2) {
    // 大窗口状态 - 全屏显示，不缩放
    return {
      ...baseStyle,
      position: 'absolute' as const,
      top: '0',
      left: '0',
      transform: ''
    };
  } else {
    // 默认状态
    return {
      ...baseStyle,
      position: 'absolute' as const,
      top: '0',
      left: '0'
    };
  }
});

// 初始化Canvas
const initCanvas = () => {
  if (!canvasIRRef.value) return;

  const canvas = canvasIRRef.value;
  ctx = canvas.getContext('2d');

  // 设置Canvas尺寸为固定的1280x720，与视频尺寸保持一致
  canvas.width = 1280;
  canvas.height = 720;

  // 设置Canvas样式
  if (ctx) {
    ctx.imageSmoothingEnabled = false;
  }
};

// 获取鼠标在Canvas中的相对坐标 (0-1)
const getRelativeCoordinates = (event: MouseEvent) => {
  if (!canvasIRRef.value) return { x: 0, y: 0 };

  const canvas = canvasIRRef.value;
  const rect = canvas.getBoundingClientRect();
  const adaptation = videoAdaptation.value;

  // 计算鼠标在Canvas中的像素坐标
  let pixelX = event.clientX - rect.left;
  let pixelY = event.clientY - rect.top;

  console.log('红外测温坐标计算 - 屏幕状态:', props.currentScreenStatus, '视频尺寸:', props.videoWidth, 'x', props.videoHeight);

  // 根据屏幕状态调整坐标计算
  if (props.currentScreenStatus === 1) {
    // 小窗口状态，需要考虑缩放因素
    const scale = 416 / 1280;
    // 将缩放后的坐标转换回原始坐标
    pixelX = pixelX / scale;
    pixelY = pixelY / scale;
  } else if (props.currentScreenStatus === 2) {
    // 大窗口状态，直接使用像素坐标
    // 不需要额外处理，pixelX和pixelY已经是正确的
  }

  // 如果视频需要居中显示，需要减去偏移量
  if (adaptation.needsCentering) {
    pixelX = pixelX - adaptation.offsetX;
    // 确保坐标在有效的视频区域内
    if (pixelX < 0 || pixelX > adaptation.scaledWidth) {
      return { x: -1, y: -1 }; // 返回无效坐标，表示点击在视频区域外
    }
  }

  // 转换为相对坐标 (0-1)
  // 使用实际的视频尺寸进行计算
  let x = adaptation.needsCentering ? pixelX / adaptation.scaledWidth : pixelX / 1280;
  let y = pixelY / 720;

  // console.log('视频区域内坐标:', { pixelX, pixelY, x, y });

  // 分屏模式下的坐标调整
  if (props.screenSplitEnable) {
    // 假设分屏时左侧是红外画面，占一半宽度
    x = x * 2; // 将坐标映射到完整画面
    if (x > 1) x = 1; // 限制在有效范围内
  }

  // 确保坐标在有效范围内
  x = Math.max(0, Math.min(1, x));
  y = Math.max(0, Math.min(1, y));

  console.log('最终相对坐标:', { x, y });

  return { x, y };
};

// 将相对坐标转换为Canvas像素坐标
const relativeToPixel = (relativeX: number, relativeY: number) => {
  const adaptation = videoAdaptation.value;

  // 分屏模式下的坐标调整
  let adjustedX = relativeX;
  if (props.screenSplitEnable) {
    adjustedX = adjustedX / 2; // 映射到左半部分
  }

  // 根据视频适配情况计算像素坐标
  let x, y;
  if (adaptation.needsCentering) {
    // 视频居中显示时，需要考虑偏移量
    x = adjustedX * adaptation.scaledWidth + adaptation.offsetX;
    y = relativeY * 720;
  } else {
    // 视频填充整个区域
    x = adjustedX * 1280;
    y = relativeY * 720;
  }

  return { x, y };
};

// 鼠标按下事件
const onMouseDown = (event: MouseEvent) => {
  if (event.button !== 0) return; // 只处理左键

  const coords = getRelativeCoordinates(event);
  // 检查坐标是否有效（在视频区域内）
  if (coords.x < 0 || coords.y < 0) return;

  dragStart.value = coords;
  dragEnd.value = coords;
  isDragging.value = true;

  event.preventDefault();
};

// 鼠标移动事件
const onMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return;

  const coords = getRelativeCoordinates(event);
  // 检查坐标是否有效
  if (coords.x < 0 || coords.y < 0) return;

  dragEnd.value = coords;
  drawDragRect();

  event.preventDefault();
};

// 鼠标松开事件
const onMouseUp = (event: MouseEvent) => {
  if (!isDragging.value || event.button !== 0) return;

  isDragging.value = false;

  const start = dragStart.value;
  const end = dragEnd.value;

  // 计算拖选区域
  const x = Math.min(start.x, end.x);
  const y = Math.min(start.y, end.y);
  const width = Math.abs(end.x - start.x);
  const height = Math.abs(end.y - start.y);

  console.log('拖选区域计算结果:', { x, y, width, height });
  console.log('拖选起点:', start);
  console.log('拖选终点:', end);

  // 最小拖选区域限制
  if (width > 0.05 && height > 0.05) {
    console.log('拖选区域有效，发送事件');
    emit('area-selected', { x, y, width, height });
  } else {
    console.log('拖选区域太小，忽略');
  }

  clearCanvas();
  event.preventDefault();
};

// 右键恢复全屏
const onRightClick = (event: MouseEvent) => {
  event.preventDefault();
  emit('reset-fullscreen');
  clearCanvas();
};

// 绘制拖选矩形
const drawDragRect = () => {
  if (!ctx || !canvasIRRef.value) return;

  clearCanvas();

  // 先绘制视频区域边框
  // drawVideoAreaBorder();

  const start = relativeToPixel(dragStart.value.x, dragStart.value.y);
  const end = relativeToPixel(dragEnd.value.x, dragEnd.value.y);

  const x = Math.min(start.x, end.x);
  const y = Math.min(start.y, end.y);
  const width = Math.abs(end.x - start.x);
  const height = Math.abs(end.y - start.y);

  // 绘制拖选矩形框
  ctx.strokeStyle = '#ffff00';
  ctx.lineWidth = 3;
  ctx.setLineDash([5, 5]);
  ctx.strokeRect(x, y, width, height);

  // 绘制半透明填充
  ctx.fillStyle = 'rgba(255, 255, 0, 0.2)';
  ctx.fillRect(x, y, width, height);

  // 显示拖选区域信息
  ctx.fillStyle = '#ffff00';
  ctx.font = '14px Arial';
  ctx.fillText(`拖选区域: ${width.toFixed(0)}x${height.toFixed(0)} at (${x.toFixed(0)}, ${y.toFixed(0)})`, x, y - 10);
};

// 绘制温度点
const drawTemperaturePoints = () => {
  if (!ctx || !props.irMeteringArea) return;

  clearCanvas();

  // 绘制视频区域边框用于调试
  // drawVideoAreaBorder();

  const { maxTemperaturePoint, minTemperaturePoint } = props.irMeteringArea;

  // 绘制最高温度点
  if (maxTemperaturePoint) {
    drawTemperaturePoint(
      maxTemperaturePoint,
      '#ff4444',
      `最高: ${maxTemperaturePoint.temperature.toFixed(1)}°C`
    );
  }

  // 绘制最低温度点
  if (minTemperaturePoint) {
    drawTemperaturePoint(
      minTemperaturePoint,
      '#4444ff',
      `最低: ${minTemperaturePoint.temperature.toFixed(1)}°C`
    );
  }
};

// 绘制视频区域边框（调试用）
const drawVideoAreaBorder = () => {
  if (!ctx) return;

  const adaptation = videoAdaptation.value;

  // 绘制整个Canvas边框（红色）
  ctx.strokeStyle = '#ff0000';
  ctx.lineWidth = 3;
  ctx.setLineDash([]);
  ctx.strokeRect(0, 0, 1280, 720);

  // 绘制实际视频区域边框（绿色）
  if (adaptation.needsCentering) {
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 2;
    ctx.setLineDash([10, 5]);
    ctx.strokeRect(adaptation.offsetX, 0, adaptation.scaledWidth, 720);

    // 添加文字标注
    ctx.fillStyle = '#00ff00';
    ctx.font = '16px Arial';
    // ctx.fillText(`视频区域: ${adaptation.scaledWidth}x720, 偏移: ${adaptation.offsetX}`, adaptation.offsetX + 10, 30);
  } else {
    // 全屏模式，视频区域就是整个Canvas
    ctx.strokeStyle = '#00ff00';
    ctx.lineWidth = 2;
    ctx.setLineDash([10, 5]);
    ctx.strokeRect(0, 0, 1280, 720);

    ctx.fillStyle = '#00ff00';
    ctx.font = '16px Arial';
    // ctx.fillText('视频区域: 全屏 1280x720', 10, 30);
  }

  // 添加视频尺寸信息
  ctx.fillStyle = '#ffffff';
  ctx.font = '14px Arial';
  // ctx.fillText(`原始视频: ${props.videoWidth}x${props.videoHeight}`, 10, 60);
  // ctx.fillText(`屏幕状态: ${props.currentScreenStatus}`, 10, 80);
  // ctx.fillText(`分屏模式: ${props.screenSplitEnable}`, 10, 100);
};

// 绘制单个温度点
const drawTemperaturePoint = (point: TemperaturePoint, color: string, label: string) => {
  if (!ctx) return;

  const pixel = relativeToPixel(point.x, point.y);
  const adaptation = videoAdaptation.value;

  // 绘制十字准星
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.setLineDash([]);

  // 水平线
  ctx.beginPath();
  ctx.moveTo(pixel.x - 10, pixel.y);
  ctx.lineTo(pixel.x + 10, pixel.y);
  ctx.stroke();

  // 垂直线
  ctx.beginPath();
  ctx.moveTo(pixel.x, pixel.y - 10);
  ctx.lineTo(pixel.x, pixel.y + 10);
  ctx.stroke();

  // 绘制中心点
  ctx.fillStyle = color;
  ctx.beginPath();
  ctx.arc(pixel.x, pixel.y, 3, 0, 2 * Math.PI);
  ctx.fill();

  // 智能计算文字位置
  ctx.font = '12px Arial';
  const textMetrics = ctx.measureText(label);
  const textWidth = textMetrics.width;
  const textHeight = 12; // 字体大小

  // 确定有效的显示区域边界
  let leftBound = 0;
  let rightBound = 1280;
  let topBound = 0;
  let bottomBound = 720;

  // 如果视频居中显示，调整边界
  if (adaptation.needsCentering) {
    leftBound = adaptation.offsetX;
    rightBound = adaptation.offsetX + adaptation.scaledWidth;
  }

  // 计算文字位置，优先级：上方 -> 下方 -> 右方 -> 左方
  let textX = pixel.x;
  let textY = pixel.y - 15;
  let textAlign: CanvasTextAlign = 'center';

  // 检查上方是否有足够空间
  if (textY - textHeight < topBound) {
    // 上方空间不足，尝试下方
    textY = pixel.y + 25;

    // 检查下方是否有足够空间
    if (textY + textHeight > bottomBound) {
      // 下方也不足，尝试右方
      textX = pixel.x + 15;
      textY = pixel.y + 4; // 垂直居中
      textAlign = 'left';

      // 检查右方是否有足够空间
      if (textX + textWidth > rightBound) {
        // 右方也不足，使用左方
        textX = pixel.x - 15;
        textAlign = 'right';

        // 如果左方也不足，强制在点的右上角显示（缩短文字）
        if (textX - textWidth < leftBound) {
          textX = pixel.x + 15;
          textY = pixel.y - 5;
          textAlign = 'left';
        }
      }
    }
  }

  // 检查水平方向边界
  if (textAlign === 'center') {
    const halfWidth = textWidth / 2;
    if (textX - halfWidth < leftBound) {
      textX = leftBound + halfWidth;
    } else if (textX + halfWidth > rightBound) {
      textX = rightBound - halfWidth;
    }
  } else if (textAlign === 'left') {
    if (textX + textWidth > rightBound) {
      textX = rightBound - textWidth;
    }
  } else if (textAlign === 'right') {
    if (textX - textWidth < leftBound) {
      textX = leftBound + textWidth;
    }
  }

  // 绘制温度标签
  ctx.fillStyle = '#ffffff';
  ctx.strokeStyle = '#000000';
  ctx.lineWidth = 3;
  ctx.textAlign = textAlign;

  ctx.strokeText(label, textX, textY);
  ctx.fillText(label, textX, textY);
};

// 清除Canvas
const clearCanvas = () => {
  if (!ctx || !canvasIRRef.value) return;
  ctx.clearRect(0, 0, canvasIRRef.value.width, canvasIRRef.value.height);
};

// 监听irMeteringArea变化，重新绘制温度点
watch(() => props.irMeteringArea, () => {
  if (props.irMeteringMode && props.irMeteringArea) {
    nextTick(() => {
      drawTemperaturePoints();
    });
  }
}, { deep: true });

// 监听irMeteringMode变化
watch(() => props.irMeteringMode, (newVal) => {
  // console.log('红外测温模式变化:', newVal, '分屏状态:', props.screenSplitEnable);
  if (newVal && !props.screenSplitEnable) {
    nextTick(() => {
      initCanvas();
      if (props.irMeteringArea) {
        drawTemperaturePoints();
      }
    });
  } else {
    clearCanvas();
  }
});

// 监听分屏状态变化
watch(() => props.screenSplitEnable, (newVal) => {
  // console.log('分屏状态变化:', newVal, '测温模式:', props.irMeteringMode);
  if (newVal && props.irMeteringMode) {
    // 分屏开启时，如果测温模式开启，则清除Canvas
    clearCanvas();
    console.log('分屏模式开启，禁用测温功能');
  }
});

// 监听屏幕状态变化，重新初始化Canvas
watch(() => props.currentScreenStatus, () => {
  if (props.irMeteringMode) {
    nextTick(() => {
      initCanvas();
      if (props.irMeteringArea) {
        drawTemperaturePoints();
      }
    });
  }
});

// 监听视频尺寸变化，重新计算适配参数
watch([() => props.videoWidth, () => props.videoHeight], () => {
  if (props.irMeteringMode) {
    // console.log('视频尺寸更新:', { width: props.videoWidth, height: props.videoHeight });
    nextTick(() => {
      if (props.irMeteringArea) {
        drawTemperaturePoints();
      }
    });
  }
});

// 组件挂载
onMounted(() => {
  if (props.irMeteringMode) {
    initCanvas();
    if (props.irMeteringArea) {
      drawTemperaturePoints();
    }
  }
});

// 组件卸载
onUnmounted(() => {
  clearCanvas();
});
</script>

<style scoped>
canvas {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}
</style>
