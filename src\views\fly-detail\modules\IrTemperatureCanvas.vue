<template>
  <div 
    v-if="irMeteringMode"
    class="absolute inset-0 pointer-events-none"
    :style="{ zIndex: 10 }"
  >
    <!-- 红外测温画布 -->
    <canvas
      ref="canvasIRRef"
      class="absolute inset-0 w-full h-full pointer-events-auto cursor-crosshair"
      @mousedown="onMouseDown"
      @mousemove="onMouseMove"
      @mouseup="onMouseUp"
      @contextmenu="onRightClick"
    />
    
    <!-- 拖选提示 -->
    <div 
      v-if="isDragging"
      class="absolute top-4 left-4 bg-black bg-opacity-70 text-white px-8px py-4px rd-4px text-12px"
    >
      拖选测温区域
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue';

// 定义接口
interface TemperaturePoint {
  x: number; // 相对坐标 0-1
  y: number; // 相对坐标 0-1
  temperature: number;
}

interface IrMeteringArea {
  width: number; // 相对宽度 0-1
  height: number; // 相对高度 0-1
  maxTemperaturePoint: TemperaturePoint;
  minTemperaturePoint: TemperaturePoint;
}

interface Props {
  irMeteringMode: boolean;
  irMeteringArea?: IrMeteringArea;
  screenSplitEnable: boolean;
  containerWidth?: number;
  containerHeight?: number;
}

const props = withDefaults(defineProps<Props>(), {
  irMeteringMode: false,
  irMeteringArea: undefined,
  screenSplitEnable: false,
  containerWidth: 640,
  containerHeight: 480
});

// 定义事件
const emit = defineEmits<{
  'area-selected': [area: { x: number; y: number; width: number; height: number }];
  'reset-fullscreen': [];
}>();

// 响应式数据
const canvasIRRef = ref<HTMLCanvasElement>();
const isDragging = ref(false);
const dragStart = ref({ x: 0, y: 0 });
const dragEnd = ref({ x: 0, y: 0 });

// Canvas上下文
let ctx: CanvasRenderingContext2D | null = null;

// 初始化Canvas
const initCanvas = () => {
  if (!canvasIRRef.value) return;
  
  const canvas = canvasIRRef.value;
  ctx = canvas.getContext('2d');
  
  // 设置Canvas尺寸
  const rect = canvas.getBoundingClientRect();
  canvas.width = rect.width;
  canvas.height = rect.height;
  
  // 设置Canvas样式
  if (ctx) {
    ctx.imageSmoothingEnabled = false;
  }
};

// 获取鼠标在Canvas中的相对坐标 (0-1)
const getRelativeCoordinates = (event: MouseEvent) => {
  if (!canvasIRRef.value) return { x: 0, y: 0 };
  
  const rect = canvasIRRef.value.getBoundingClientRect();
  let x = (event.clientX - rect.left) / rect.width;
  let y = (event.clientY - rect.top) / rect.height;
  
  // 分屏模式下的坐标调整
  if (props.screenSplitEnable) {
    // 假设分屏时左侧是红外画面，占一半宽度
    x = x * 2; // 将坐标映射到完整画面
    if (x > 1) x = 1; // 限制在有效范围内
  }
  
  // 确保坐标在有效范围内
  x = Math.max(0, Math.min(1, x));
  y = Math.max(0, Math.min(1, y));
  
  return { x, y };
};

// 将相对坐标转换为Canvas像素坐标
const relativeToPixel = (relativeX: number, relativeY: number) => {
  if (!canvasIRRef.value) return { x: 0, y: 0 };
  
  const canvas = canvasIRRef.value;
  let x = relativeX * canvas.width;
  let y = relativeY * canvas.height;
  
  // 分屏模式下的坐标调整
  if (props.screenSplitEnable) {
    x = x / 2; // 映射到左半部分
  }
  
  return { x, y };
};

// 鼠标按下事件
const onMouseDown = (event: MouseEvent) => {
  if (event.button !== 0) return; // 只处理左键
  
  const coords = getRelativeCoordinates(event);
  dragStart.value = coords;
  dragEnd.value = coords;
  isDragging.value = true;
  
  event.preventDefault();
};

// 鼠标移动事件
const onMouseMove = (event: MouseEvent) => {
  if (!isDragging.value) return;
  
  dragEnd.value = getRelativeCoordinates(event);
  drawDragRect();
  
  event.preventDefault();
};

// 鼠标松开事件
const onMouseUp = (event: MouseEvent) => {
  if (!isDragging.value || event.button !== 0) return;
  
  isDragging.value = false;
  
  const start = dragStart.value;
  const end = dragEnd.value;
  
  // 计算拖选区域
  const x = Math.min(start.x, end.x);
  const y = Math.min(start.y, end.y);
  const width = Math.abs(end.x - start.x);
  const height = Math.abs(end.y - start.y);
  
  // 最小拖选区域限制
  if (width > 0.05 && height > 0.05) {
    emit('area-selected', { x, y, width, height });
  }
  
  clearCanvas();
  event.preventDefault();
};

// 右键恢复全屏
const onRightClick = (event: MouseEvent) => {
  event.preventDefault();
  emit('reset-fullscreen');
  clearCanvas();
};

// 绘制拖选矩形
const drawDragRect = () => {
  if (!ctx || !canvasIRRef.value) return;
  
  clearCanvas();
  
  const start = relativeToPixel(dragStart.value.x, dragStart.value.y);
  const end = relativeToPixel(dragEnd.value.x, dragEnd.value.y);
  
  const x = Math.min(start.x, end.x);
  const y = Math.min(start.y, end.y);
  const width = Math.abs(end.x - start.x);
  const height = Math.abs(end.y - start.y);
  
  // 绘制矩形框
  ctx.strokeStyle = '#00ff00';
  ctx.lineWidth = 2;
  ctx.setLineDash([5, 5]);
  ctx.strokeRect(x, y, width, height);
  
  // 绘制半透明填充
  ctx.fillStyle = 'rgba(0, 255, 0, 0.1)';
  ctx.fillRect(x, y, width, height);
};

// 绘制温度点
const drawTemperaturePoints = () => {
  if (!ctx || !props.irMeteringArea) return;
  
  clearCanvas();
  
  const { maxTemperaturePoint, minTemperaturePoint } = props.irMeteringArea;
  
  // 绘制最高温度点
  if (maxTemperaturePoint) {
    drawTemperaturePoint(
      maxTemperaturePoint,
      '#ff4444',
      `最高: ${maxTemperaturePoint.temperature.toFixed(1)}°C`
    );
  }
  
  // 绘制最低温度点
  if (minTemperaturePoint) {
    drawTemperaturePoint(
      minTemperaturePoint,
      '#4444ff',
      `最低: ${minTemperaturePoint.temperature.toFixed(1)}°C`
    );
  }
};

// 绘制单个温度点
const drawTemperaturePoint = (point: TemperaturePoint, color: string, label: string) => {
  if (!ctx) return;
  
  const pixel = relativeToPixel(point.x, point.y);
  
  // 绘制十字准星
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.setLineDash([]);
  
  // 水平线
  ctx.beginPath();
  ctx.moveTo(pixel.x - 10, pixel.y);
  ctx.lineTo(pixel.x + 10, pixel.y);
  ctx.stroke();
  
  // 垂直线
  ctx.beginPath();
  ctx.moveTo(pixel.x, pixel.y - 10);
  ctx.lineTo(pixel.x, pixel.y + 10);
  ctx.stroke();
  
  // 绘制中心点
  ctx.fillStyle = color;
  ctx.beginPath();
  ctx.arc(pixel.x, pixel.y, 3, 0, 2 * Math.PI);
  ctx.fill();
  
  // 绘制温度标签
  ctx.fillStyle = '#ffffff';
  ctx.strokeStyle = '#000000';
  ctx.lineWidth = 3;
  ctx.font = '12px Arial';
  ctx.textAlign = 'center';
  
  const textY = pixel.y - 15;
  ctx.strokeText(label, pixel.x, textY);
  ctx.fillText(label, pixel.x, textY);
};

// 清除Canvas
const clearCanvas = () => {
  if (!ctx || !canvasIRRef.value) return;
  ctx.clearRect(0, 0, canvasIRRef.value.width, canvasIRRef.value.height);
};

// 监听irMeteringArea变化，重新绘制温度点
watch(() => props.irMeteringArea, () => {
  if (props.irMeteringMode && props.irMeteringArea) {
    nextTick(() => {
      drawTemperaturePoints();
    });
  }
}, { deep: true });

// 监听irMeteringMode变化
watch(() => props.irMeteringMode, (newVal) => {
  if (newVal) {
    nextTick(() => {
      initCanvas();
      if (props.irMeteringArea) {
        drawTemperaturePoints();
      }
    });
  } else {
    clearCanvas();
  }
});

// 组件挂载
onMounted(() => {
  if (props.irMeteringMode) {
    initCanvas();
    if (props.irMeteringArea) {
      drawTemperaturePoints();
    }
  }
});

// 组件卸载
onUnmounted(() => {
  clearCanvas();
});
</script>

<style scoped>
canvas {
  image-rendering: pixelated;
  image-rendering: -moz-crisp-edges;
  image-rendering: crisp-edges;
}
</style>
