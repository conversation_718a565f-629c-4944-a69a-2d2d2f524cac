import * as Cesium from 'cesium';

export default class DynamicPointConnector {
    private viewer: Cesium.Viewer;
    private points: Cesium.Cartesian3[] = [];
    private lineEntity?: Cesium.Entity;
    private labels: Cesium.Entity[] = []; // 存储所有距离标签
    private totalDistance = 0; // 总距离累计
    private dataSource: Cesium.CustomDataSource;

    constructor(viewer: Cesium.Viewer) {
        this.viewer = viewer;
        // 创建独立数据源
        this.dataSource = new Cesium.CustomDataSource("DynamicPointConnector");
        viewer.dataSources.add(this.dataSource);
    }
    /*
    * 添加新点并生成连接线
    * @param lon 经度
    * @param lat 纬度
    * @param height 高度（可选）
    */
    addPoint(lon: number, lat: number, height?: number): boolean {
        const position = Cesium.Cartesian3.fromDegrees(lon, lat, height);
        const isDuplicate = this.points.some(existingPos =>
            Cesium.Cartesian3.equals(existingPos, position)
        );
        if (isDuplicate) return true;
        // 当存在前一个点时计算距离
        if (this.points.length >= 1) {
            const prevPos = this.points[this.points.length - 1];
            const segmentDistance = this._calculateDistance(prevPos, position);
            this.totalDistance += segmentDistance;
            // 添加分段距离标签
            this._addSegmentLabel(prevPos, position, segmentDistance);
        }

        this.points.push(position);
        this._updatePolyline();

        // 添加总距离标签
        this._updateTotalDistanceLabel();
        return false;
    }
    /* 计算两点间距离 */
    private _calculateDistance(pos1: Cesium.Cartesian3, pos2: Cesium.Cartesian3): number {
        const carto1 = Cesium.Cartographic.fromCartesian(pos1);
        const carto2 = Cesium.Cartographic.fromCartesian(pos2);
        const geodesic = new Cesium.EllipsoidGeodesic(carto1, carto2);
        return Math.sqrt(
            Math.pow(geodesic.surfaceDistance, 2) +
            Math.pow(carto2.height - carto1.height, 2)
        );
    }
    /* 添加分段距离标签 */
    private _addSegmentLabel(startPos: Cesium.Cartesian3, endPos: Cesium.Cartesian3, distance: number): void {
        // const midPoint = Cesium.Cartesian3.midpoint(startPos, endPos, startPos);
        const midPoint = new Cesium.Cartesian3();
        Cesium.Cartesian3.midpoint(startPos, endPos, midPoint);
        const label = this.dataSource.entities.add({
            position: midPoint,
            label: {
                text: `${(distance / 1000).toFixed(2)} km`,
                font: '14px Arial',
                fillColor: Cesium.Color.YELLOW,
                outlineColor: Cesium.Color.BLACK,
                pixelOffset: new Cesium.Cartesian2(0, 20), // 修正偏移方向
                showBackground: true,
                backgroundColor: new Cesium.Color(0.1, 0.1, 0.1, 0.9)
            }
        });
        this.labels.push(label);
    }
    /* 更新总距离标签 */
    private _updateTotalDistanceLabel(): void {
        if (this.labels.length > 0) {
            // 清除所有旧总距离标签
            this.labels
                .filter(l => l.label?.text?.getValue().includes('总距离'))
                .forEach(label => this.dataSource.entities.remove(label));
            // 设置最新标签
            const label = this.dataSource.entities.add({
                position: this.points[this.points.length - 1],
                label: {
                    text: `总距离：${(this.totalDistance / 1000).toFixed(2)} km`,
                    font: '16px Arial',
                    fillColor: Cesium.Color.GOLD,
                    pixelOffset: new Cesium.Cartesian2(0, 40),
                    style: Cesium.LabelStyle.FILL_AND_OUTLINE,
                    outlineWidth: 2
                }
            });
            this.labels.push(label);
        }
    }
    // 线段更新
    private _updatePolyline(): void {
        if (this.lineEntity) {
            this.dataSource.entities.remove(this.lineEntity);
        }
        if (this.points.length >= 2) {
            this.lineEntity = this.dataSource.entities.add({
                polyline: {
                    positions: new Cesium.CallbackProperty(() => {
                        return [...this.points.map(p => Cesium.Cartesian3.clone(p))];
                    }, false),
                    width: 4,
                    material: new Cesium.PolylineGlowMaterialProperty({
                        glowPower: 0.3,
                        color: Cesium.Color.AQUA.withAlpha(0.8)
                    }),
                    arcType: Cesium.ArcType.GEODESIC,
                    clampToGround: this.points.every(p =>
                        Cesium.Cartographic.fromCartesian(p).height === 0
                    )
                }
            });
            this.viewer.scene.requestRender(); // 强制刷新
        }
    }
    /*清空所有绘制 */
    clear(): void {
        this.dataSource.entities.removeAll();
        // 清除内存引用
        this.points = [];
        this.lineEntity = undefined;
        this.labels = [];
        this.totalDistance = 0;
    }
    /*自动调整视图范围 */
    zoomToPoints(): void {
        if (this.points.length === 0) return;
        const boundingSphere = Cesium.BoundingSphere.fromPoints(this.points);
        const MIN_RADIUS = 1000; // 设置最小半径
        Cesium.BoundingSphere.clone(boundingSphere, boundingSphere);
        boundingSphere.radius = Math.max(boundingSphere.radius * 1.5, MIN_RADIUS);
        // boundingSphere.radius *= 1.5;
        // 2. 调整视角
        this.viewer.camera.flyToBoundingSphere(boundingSphere, {
            offset: new Cesium.HeadingPitchRange(
                Cesium.Math.toRadians(0),
                Cesium.Math.toRadians(-30),
                boundingSphere.radius * 3.2
            ),
            duration: 2 // 添加过渡动画
        });
    }
}