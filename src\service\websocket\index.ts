import { ref } from 'vue';
import { getServiceBaseURL } from '@/utils/service';
import { localStg } from '@/utils/storage';
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';

// 获取服务的基础 URL
const serviceBaseURL = import.meta.env.VITE_SERVICE_BASE_URL;

// 获取 token
// const token = localStg.get('token') || '';
// console.log("websocket token:", token)

// 定义 WebSocket 配置接口
interface WebSocketOptions {
  url: string; // WebSocket URL
  heartBeatData: string; // 心跳数据
  heartBeatInterval: number; // 心跳间隔，单位 ms
  reconnectInterval: number; // 断线重连间隔，单位 ms
  maxReconnectAttempts: number; // 最大重连次数
}

// 默认的 WebSocket 配置
const DEFAULT_OPTIONS: WebSocketOptions = {
  url: '',
  heartBeatData: '',
  heartBeatInterval: 60 * 1000,
  reconnectInterval: 5000,
  maxReconnectAttempts: 10,
};

// 定义 WebSocket 状态枚举
export const SocketStatus = {
  Connecting: '正在连接...',
  Connected: '连接已建立',
  Disconnecting: '连接正在关闭',
  Disconnected: '连接已断开',
} as const;

// WebSocket 状态类型的类型别名
type SocketStatusType = typeof SocketStatus[keyof typeof SocketStatus];

// WebSocket 关闭代码
const SocketCloseCode = 1000;

// WebSocket 服务的组合式 API
export default function UseWebSocket(options: Partial<WebSocketOptions> = {}) {
  // 内部状态对象
  const state = {
    options: { ...DEFAULT_OPTIONS, ...options },
    socket: null as WebSocket | null,
    reconnectAttempts: 0,
    reconnectTimeout: null as NodeJS.Timeout | null,
    heartBetaSendTimer: null as NodeJS.Timeout | null,
    heartBetaTimeoutTimer: null as NodeJS.Timeout | null,
  };

  // 可响应的状态变量
  const status = ref<SocketStatusType>(SocketStatus.Disconnected);
  const message = ref<any>(null);
  const error = ref<any>(null);

  // 连接 WebSocket
  const connect = () => {
    disconnect();

    status.value = SocketStatus.Connecting;
    if (!window.navigator.onLine) {
      setTimeout(() => {
        status.value = SocketStatus.Disconnected;
      }, 500);
      return;
    }
    const token = localStg.get('token');
    // console.log("connect ~ token:", token)
    state.socket = new WebSocket(state.options.url + '?sec-websocket-protocol=' + token);
    // state.socket = new WebSocket(state.options.url, token);

    state.socket.onopen = (openEvent) => {
      // console.log('socket连接:', openEvent);
      state.reconnectAttempts = 0;
      status.value = SocketStatus.Connected;
      error.value = null;
      // console.log("connect ~ 'Authorization: ' + token:", 'Authorization: ' + token)
      startHeartBeat();
    };

    state.socket.onmessage = (msgEvent) => {
      // console.log('socket消息:', msgEvent);
      startHeartBeat();
      const { data } = msgEvent;
      const msg = JSON.parse(data);

      if (+msg.msg_id === 0) {
        return;
      }
      message.value = msg;
    };

    state.socket.onclose = (closeEvent) => {
      // console.log('socket关闭:', closeEvent);
      status.value = SocketStatus.Disconnected;
      if (closeEvent.code !== SocketCloseCode) {
        reconnect();
      }
    };

    state.socket.onerror = (errEvent) => {
      console.log('socket报错:', errEvent);
      status.value = SocketStatus.Disconnected;
      error.value = errEvent;
      reconnect();
    };
  };

  // 断开 WebSocket 连接
  const disconnect = () => {
    console.log('socket断开连接');
    if (state.socket && (state.socket.readyState === WebSocket.OPEN || state.socket.readyState === WebSocket.CONNECTING)) {
      status.value = SocketStatus.Disconnecting;
      state.socket.onmessage = null;
      state.socket.onerror = null;
      state.socket.onclose = null;
      state.socket.close(SocketCloseCode, 'normal closure');
      status.value = SocketStatus.Disconnected;
      state.socket = null;
    }
    stopHeartBeat();
    stopReconnect();
  };

  // 开始心跳检测
  const startHeartBeat = () => {
    stopHeartBeat();
    onHeartBeat(() => {
      if (status.value === SocketStatus.Connected) {
        state.socket!.send(state.options.heartBeatData);
        console.log('socket心跳发送:', state.options.heartBeatData);
      }
    });
  };

  // 心跳检测回调
  const onHeartBeat = (callback: () => void) => {
    state.heartBetaSendTimer = setTimeout(() => {
      callback && callback();
      state.heartBetaTimeoutTimer = setTimeout(() => {
        state.socket!.close(4444, 'heart timeout');
      }, state.options.heartBeatInterval);
    }, state.options.heartBeatInterval);
  };

  // 停止心跳检测
  const stopHeartBeat = () => {
    state.heartBetaSendTimer && clearTimeout(state.heartBetaSendTimer);
    state.heartBetaTimeoutTimer && clearTimeout(state.heartBetaTimeoutTimer);
  };

  // 重连 WebSocket
  const reconnect = () => {
    if (status.value === SocketStatus.Connected || status.value === SocketStatus.Connecting) {
      return;
    }
    stopHeartBeat();
    if (state.reconnectAttempts < state.options.maxReconnectAttempts) {
      console.log('socket重连:', state.reconnectAttempts);
      const interval = Math.max(state.options.reconnectInterval, state.reconnectAttempts * 1000);
      console.log('间隔时间：', interval);
      state.reconnectTimeout = setTimeout(() => {
        if (status.value !== SocketStatus.Connected && status.value !== SocketStatus.Connecting) {
          connect();
        }
      }, interval);
      state.reconnectAttempts += 1;
    } else {
      status.value = SocketStatus.Disconnected;
      stopReconnect();
    }
  };

  // 停止重连
  const stopReconnect = () => {
    state.reconnectTimeout && clearTimeout(state.reconnectTimeout);
  };

  // 添加发送消息的方法
  const send = (data: any): boolean => {
    if (!state.socket || state.socket.readyState !== WebSocket.OPEN) {
      console.warn('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      const messageData = typeof data === 'string' ? data : JSON.stringify(data);
      state.socket.send(messageData);
      return true;
    } catch (err) {
      console.error('发送消息失败:', err);
      return false;
    }
  };

  return {
    status,
    message,
    error,
    connect,
    disconnect,
    send,
  };
}
