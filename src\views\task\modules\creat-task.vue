<template>
  <n-modal :show="creatTaskModalShow">
    <n-card :title="props.taskInfoItemData.isCreat ? '新建机场任务' : '编辑机场任务'" class="w-410px" closable
      @close="handelCreatTaskModalShow">
      <n-form ref="formRef" :model="infoModel" :rules="infoRules" label-placement="left" label-width="auto"
        require-mark-placement="right-hanging">
        <n-form-item label="任务名称" path="flightJobName">
          <n-input v-model:value="infoModel.flightJobName" placeholder="任务名称" :maxlength="50" />
        </n-form-item>
        <n-form-item label="任务类型" path="flightJobType">
          <n-select v-model:value="infoModel.flightJobType" :disabled="!taskInfoItemData.isCreat" placeholder="请选择"
            :options="generaTaskTypelOptions" />
        </n-form-item>
        <n-form-item v-show="infoModel.flightJobType === 2" label="选择时间" path="execTime">
          <n-date-picker v-model:formatted-value="infoModel.execTime" :is-date-disabled="disablePreviousDate"
            value-format="yyyy-MM-dd HH:mm:ss" type="datetime" clearable class="w-100%" />
        </n-form-item>
        <n-form-item v-show="infoModel.flightJobType === 3" label="执行周期" path="periodUnit">
          <n-select v-model:value="infoModel.periodUnit" placeholder="请选择" :options="generalPeriodOptions" />
        </n-form-item>
        <n-form-item v-show="infoModel.flightJobType === 3 && infoModel.periodUnit === 2" label=""
          path="periodValueList">
          <n-checkbox-group v-model:value="infoModel.periodValueList">
            <n-space>
              <n-checkbox v-for="(item) in generalWeekOptions" :key="item.value" :value="item.value">{{ item.label
              }}</n-checkbox>
            </n-space>
          </n-checkbox-group>
        </n-form-item>
        <n-form-item v-show="infoModel.flightJobType === 3" label="周期时间" path="periodExecTimeList">
          <n-dynamic-tags v-model:value="infoModel.periodExecTimeList">
            <template #input="{ submit, deactivate }">
              <n-time-picker @confirm="(time) => handleTimeConfirm(time, submit, deactivate)" size="small"
                format="HH:mm" />
            </template>
            <template #trigger="{ activate, disabled }">
              <n-button size="small" type="primary" dashed :disabled="disabled" @click="activate()">
                <template #icon>
                  <n-icon>
                    <Add />
                  </n-icon>
                </template>
                添加
              </n-button>
            </template>
          </n-dynamic-tags>
        </n-form-item>
        <n-form-item label="执行机场" path="deviceSn">
          <n-select v-model:value="infoModel.deviceSn" placeholder="请选择"
            :options="infoModel.flightJobType === 1 ? onlineAirportDeviceList : airportDeviceList"
            value-field="deviceSn" label-field="deviceName" :render-label="renderLabel" />
        </n-form-item>
        <n-form-item label="选择航线" path="flightId">
          <n-select v-model:value="infoModel.flightId" placeholder="请选择" :options="airLineList" value-field="flightId"
            label-field="flightName" filterable />
        </n-form-item>
        <n-form-item label="返航高度" path="returnHomeHeight">
          <n-input-number v-model:value="infoModel.returnHomeHeight" placeholder="返航高度（m）" class="w-100%" :min="20"
            :max="500" />
        </n-form-item>
      </n-form>
      <template #action>
        <div class="text-right">
          <n-button @click="() => handelCreatTaskModalShow()" attr-type="button" class="mr2">取消</n-button>
          <n-button @click="handleValidateButtonClick" type="primary" attr-type="button">确定</n-button>
        </div>
      </template>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { PropType, computed, h, nextTick, onMounted, onUpdated, reactive, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { darkTheme, useMessage, NBadge } from 'naive-ui';
import type { AutoCompleteInst, DropdownOption, FormInst, SelectOption, } from 'naive-ui';
import { CloseCircleOutline, Add } from '@vicons/ionicons5';
import { useAirportStore } from '@/store/modules/airport';
import { fetchAirLineList, fetchAirportDeviceList, sendEditTaskSaveInfo, sendTaskSaveInfo } from '@/service/api';

defineOptions({ name: 'CreatTask' });

// 定义组件接受的属性
const props = defineProps<{
  creatTaskModalShow: boolean;
  taskInfoItemData: Api.Task.TaskItem;
}>();

const emits = defineEmits(['task-visible']);
const message = useMessage();
const airportStore = useAirportStore();
const formRef = ref<FormInst | null>(null)

const disablePreviousDate = (ts: number) => {
  return ts < Date.now() - 24 * 60 * 60 * 1000;
};


const infoModel = ref<Api.Task.TaskItem>({
  flightJobName: '', // 任务名称
  flightJobType: 1, // 1 立即执行，2 定时执行，3 周期执行
  deviceSn: undefined, // 设备SN
  flightId: 0, // 航线ID
  returnHomeHeight: undefined, // 返航高度
  execTime: null, // 定时执行-选择时间
  periodUnit: undefined, // 执行周期-1每天，2每周
  periodExecTimeList: [], // 执行周期-周期时间
  periodValueList: [] // 执行周期-每周周几
});

const infoRules: any = {
  flightJobName: [{ key: 'flightJobName', required: true, trigger: ['blur', 'input'], message: '请填写任务名称' }],
  flightJobType: [{ key: 'flightJobType', required: true, type: 'number', trigger: ['blur', 'input'], message: '请选择任务类型' }],
  deviceSn: [{ key: 'deviceSn', required: true, trigger: ['blur', 'input'], message: '请选择执行机场' }],
  flightId: [{ key: 'flightId', required: true, type: 'number', trigger: ['blur', 'input'], message: '请选择航线' }],
  returnHomeHeight: [{ key: 'returnHomeHeight', type: 'number', required: true, trigger: ['blur', 'input'], message: '请填写返航高度' }],
  execTime: [{ key: 'execTime', required: true, trigger: ['blur', 'input'], message: '请选择时间' }],
  periodUnit: [{ key: 'periodUnit', required: true, type: 'number', trigger: ['blur', 'input'], message: '请选择执行周期' }],
  periodExecTimeList: [{ key: 'periodExecTimeList', required: true, type: 'array', trigger: ['blur', 'input'], message: '请选择周期时间' }],
  periodValueList: [{ key: 'periodValueList', required: true, type: 'array', trigger: ['blur', 'input'], message: '请至少选择一个日期' }]
};


const generaTaskTypelOptions = ['立即执行', '定时执行', '周期执行'].map((v, i) => ({
  label: v,
  value: i + 1
}));
const generalPeriodOptions = ['每天', '每周'].map((v, i) => ({
  label: v,
  value: i + 1
}));
const generalWeekOptions = ['周一', '周二', '周三', '周四', '周五', '周六', '周日'].map((v, i) => ({
  label: v,
  value: i + 1
}));
const airLineList = ref([]); // 航线列表
interface AirportDevice {
  status: boolean;
  deviceSn: string;
}
const airportDeviceList = ref<AirportDevice[]>([]); // 机场列表
// 在线机场
const onlineAirportDeviceList = computed(() =>
  airportDeviceList.value.filter(item => item?.status)
);
// 确认保存
function handleValidateButtonClick(e: MouseEvent) {
  formRef.value?.validate(
    (errors) => {
      if (errors) { return }
      console.log("handleValidateButtonClick ~ subModel:", infoModel.value);
      handleSubmitCreatTask();
    },
    (rule) => {
      const jobType = infoModel.value.flightJobType;
      // console.log("handleValidateButtonClick ~ rule:", rule);
      const commonKeys = ['flightJobName', 'flightJobType', 'deviceSn', 'flightId', 'returnHomeHeight'];
      switch (jobType) {
        case 1: // 1 立即执行
          return commonKeys.includes(rule?.key as string);
        case 2: // 2 定时执行
          return [...commonKeys, 'execTime'].includes(rule?.key as string);
        case 3: // 3 周期执行
          if (infoModel.value.periodUnit === 1) { // 执行周期-天
            return [...commonKeys, 'periodUnit', 'periodExecTimeList'].includes(rule?.key as string);
          } else { // 执行周期-周
            return [...commonKeys, 'periodUnit', 'periodExecTimeList', 'periodValueList'].includes(rule?.key as string);
          }
        default:
          return rule?.key === 'flightJobType';
      }
    }
  );
}

// 上传任务
async function handleSubmitCreatTask() {
  // 格式化周期时间，添加":00"
  if (infoModel.value.periodExecTimeList && infoModel.value.periodExecTimeList.length) {
    infoModel.value.periodExecTimeList = infoModel.value.periodExecTimeList.map(
      time => time.includes(':00') ? time : `${time}:00`
    );
  }
  console.log("handleSubmitCreatTask ~ infoModel.value:", infoModel.value);

  let error = null;
  if (props.taskInfoItemData.isCreat) {
    error = (await sendTaskSaveInfo(infoModel.value)).error;
  } else {
    error = (await sendEditTaskSaveInfo(infoModel.value)).error;
  }
  if (!error) {
    message.success('操作成功');
    handelCreatTaskModalShow(true);
  }
}

// 获取航线列表
const getAirLineList = async () => {
  const { data, error } = await fetchAirLineList({ pageSize: 100000 });
  if (!error) {
    airLineList.value = data.rows;
  }
}
// 获取已激活的机场列表
async function getAirportDeviceList() {
  const { data } = await fetchAirportDeviceList();
  airportDeviceList.value = data.rows;
}

// 处理时间选择确认
function handleTimeConfirm(time: any, submit: (value: string) => void, deactivate: () => void) {
  const newTime = dayjs(time).format('HH:mm');
  if (infoModel.value.periodExecTimeList && infoModel.value.periodExecTimeList.includes(newTime)) {
    message.error('该时间已存在，请选择其他时间');
    deactivate();
    return;
  }
  submit(newTime);
}

function handelCreatTaskModalShow(isCreat: boolean = false) {
  console.log("handelCreatTaskModalShow ~ isCreat:", isCreat)
  emits('task-visible', isCreat);
}

const renderLabel: any = (option: SelectOption) => {
  return h('div', { style: { display: 'flex', alignItems: 'center' } }, [
    h(NBadge, {
      dot: true,
      color: option.status ? '#18a058' : '#999', // 在线状态绿色，离线灰色
      style: { marginRight: '10px' }
    }),
    h('span', { style: { dot: true, color: '#999' } }, `${option.deviceName}`)
  ])
}

onMounted(() => {
  getAirLineList();
  getAirportDeviceList();
})

onUpdated(() => {
  if (Object.keys(props.taskInfoItemData).length) {
    infoModel.value = props.taskInfoItemData;
  }
  console.log("onUpdated ~ infoModel:", infoModel)
})

// defineExpose({ wayActionArr })
const autoCompleteInstRef = ref<AutoCompleteInst | null>(null)
watch(autoCompleteInstRef, (value) => {
  if (value)
    nextTick(() => value.focus());
})

// 监听任务类型变化，更新表单规则
watch(infoModel, (value) => {
  const jobType = value.flightJobType;
  const deviceSn = value.deviceSn;
  const type = airportDeviceList.value.find(item => item.deviceSn === deviceSn)?.status; // 当前选中机场状态是否在线
  if (jobType === 1 && !type) {  // 立即执行且状态为离线则清除所选机场
    infoModel.value.deviceSn = null;
  }
}, { deep: true })

</script>

<style scoped></style>
