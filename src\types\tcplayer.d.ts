declare module 'tcplayer.js' {
  export interface TCPlayerConfig {
    sources: {
      src: string;
      type?: string;
    }[];
    licenseUrl?: string;
    ProgressMarker?: boolean;
    muted?: boolean;
    controls?: boolean;
    autoplay?: boolean;
    [key: string]: any;
  }

  export class TCPlayer {
    constructor(elementId: string, config: TCPlayerConfig);
    dispose(): void;
    unload(): void;
    play(): void;
    pause(): void;
    videoWidth(): number;
    videoHeight(): number;
    volume(level: number): void;
    currentTime(time?: number): number;
    on(event: string, callback: Function): void;
    off(event: string, callback: Function): void;
    width(value?: number): number;
    height(value?: number): number;
  }

  export default TCPlayer;
} 