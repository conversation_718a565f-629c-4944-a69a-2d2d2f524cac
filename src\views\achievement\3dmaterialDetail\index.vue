<template>
  <div class="h100vh position-relative">
    <!-- 回退按钮 -->
    <n-icon @click="handlePageBack" class="cursor-pointer back-icon" :component="ChevronBackOutline" size="24"
      color="#ffffff" />
    <div id="cesiumViewer" class="h100%"></div>
    <!-- 添加旋转控制按钮 -->
    <div class="rotation-controls">
      <button @click="rotateLeft"
        :style="{ backgroundColor: isRotating && rotationDirection === 'left' ? '#0056b3' : 'rgba(37, 37, 37, 0.8)' }">
        ↺ <!-- 向左旋转图标 -->
      </button>
      <button @click="rotateRight"
        :style="{ backgroundColor: isRotating && rotationDirection === 'right' ? '#0056b3' : 'rgba(37, 37, 37, 0.8)' }">
        ↻ <!-- 向右旋转图标 -->
      </button>
    </div>
  </div>
</template>
<script setup lang="ts">
import * as Cesium from 'cesium';
import { ref, onMounted } from 'vue';
import { cesiumConfig, josiah<PERSON><PERSON>ider, labelProvider } from '@/config/cesium_config';
import ZoomController from '@cesium-extends/zoom-control';
import { ChevronBackOutline } from '@vicons/ionicons5'

import {
  getDetail,
  uploadUrl
} from '@/service/api/results';
import { useRoute } from "vue-router";
import { useRouterPush } from '@/hooks/common/router';
const { routerBack } = useRouterPush();
const route = useRoute();
// cesium viewer 初始化
let viewer: Cesium.Viewer;
const loaded = ref(false);
let tileset: Cesium.Cesium3DTileset;
let initialHeading = 45; // 初始旋转角度（45 度）
const step = 0.1; // 每次旋转的步长（角度）
var longitudeCopy: number;
var latitudeCopy: number;
// 自动旋转状态
const isRotating = ref(false); // 是否正在旋转
const rotationDirection = ref<'left' | 'right' | null>(null); // 旋转方向
let rotateInterval: string | number | NodeJS.Timeout | null | undefined = null; // 旋转定时器
function handlePageBack() {
  routerBack();
}
// 初始化地图
const initCesium = async () => {
  const res = await getDetail(route.query.id || '');
  cesiumConfig.sceneMode = Cesium.SceneMode.SCENE3D;
  viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; // 修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  loaded.value = true;

  // 修改图层
  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider);

  new ZoomController(viewer, {
    container: document.getElementById('cesiumViewer') as HTMLElement,
    tips: {
      zoomIn: '放大',
      zoomOut: '缩小',
      refresh: '重置缩放',
    },
  });

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 100;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 1000000;

  // 设置相机位置为当前定位点
  // viewer.camera.setView({
  //   destination: Cesium.Cartesian3.fromDegrees(113.0751968698131, 28.15449223707023, 10000), // 设置目的地高度/米
  //   orientation: {
  //     heading: Cesium.Math.toRadians(45), // 水平旋转 45 度
  //     pitch: -Cesium.Math.toRadians(30), // 俯仰角度（30 度，向屏幕里倾斜）
  //     roll: 0,
  //   },
  // });

  // 允许相机旋转
  viewer.scene.screenSpaceCameraController.enableRotate = true; // 启用旋转
  viewer.scene.screenSpaceCameraController.enableTilt = true; // 启用俯仰
  viewer.scene.screenSpaceCameraController.enableLook = true; // 启用视角调整
  let proxy = res?.data?.proxy || ''
  let name = res?.data?.name || ''
  // 加载 B3DM 模型
  tileset = await Cesium.Cesium3DTileset.fromUrl(import.meta.env.VITE_SERVICE_BASE_URL + proxy + '/' + name + '/tileset.json');
  // 处理加载完成
  if (tileset) {
    // 将模型添加到场景中
    viewer.scene.primitives.add(tileset);
    // 启用深度测试
    viewer.scene.globe.depthTestAgainstTerrain = true;
    try {
      // 从tileset中获取模型的位置信息
      if (tileset.root && tileset.root.transform) {
        // // 提取模型的变换矩阵
        // const transform = tileset.root.transform;

        // // 提取位置坐标(Cartesian3)
        // const position = new Cesium.Cartesian3(0, 0, 0);

        // // 将局部坐标转换为世界坐标
        // const worldPosition = Cesium.Matrix4.multiplyByPoint(
        //   transform,
        //   position,
        //   new Cesium.Cartesian3()
        // );

        // // 将世界坐标转换为经纬度
        const cartographic = Cesium.Cartographic.fromCartesian(tileset.boundingSphere.center);
        const longitude = Cesium.Math.toDegrees(cartographic.longitude);
        const latitude = Cesium.Math.toDegrees(cartographic.latitude);
        const height = cartographic.height;

        // console.log('Longitude:', longitude);
        // console.log('Latitude:', latitude);
        // console.log('Height:', height);
        longitudeCopy = longitude
        latitudeCopy = latitude

        console.log('模型位置信息: ', longitudeCopy, {
          longitude: longitude,
          latitude: latitude,
          height: height
        });
        // viewer.camera.flyTo({
        //   destination: Cesium.Cartesian3.fromDegrees(
        //     longitude,
        //     latitude,
        //     height + 1000  // 稍微抬高相机视角
        //   ),
        //   orientation: {
        //     heading: Cesium.Math.toRadians(0),
        //     pitch: Cesium.Math.toRadians(-30),
        //     roll: 0
        //   }
        // });
        // 模型外包围盒，center:模型中心点,radius:包围盒半径
        let boundingSphere = tileset.boundingSphere;
        // 模型中心点
        let origin = boundingSphere.center;
        let offset = Cesium.Cartesian3.fromDegrees(
          longitude, latitude, 15 // 提高模型的高度, 防止飘移
        );
        // 计算世界坐标系下平移向量
        let translate = Cesium.Cartesian3.subtract(
          offset,
          origin,
          new Cesium.Cartesian3()
        );
        tileset.modelMatrix = Cesium.Matrix4.fromTranslation(translate);
        // 设置相机视角，将视角对准模型
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(
            longitude,
            latitude-0.010,
            height + 500  // 稍微抬高相机视角
          ), // 设置目的地高度/米
          orientation: {
            heading: Cesium.Math.toRadians(0), // 水平旋转 45 度
            pitch: -Cesium.Math.toRadians(30), // 俯仰角度（30 度，向屏幕里倾斜）
            roll: 0,
          },
        });
      } else {
        // 如果无法获取模型的根变换，使用默认方式缩放到模型
        viewer.zoomTo(tileset, new Cesium.HeadingPitchRange(
          Cesium.Math.toRadians(initialHeading),
          Cesium.Math.toRadians(-30),
          tileset.boundingSphere.radius * 2
        ));
      }
    } catch (error) {
      console.error('处理模型位置信息失败:', error);
      // 使用默认缩放方式
      viewer.zoomTo(tileset);
    }
  }

};
// 向左旋转
const rotateLeft = () => {
  if (isRotating.value && rotationDirection.value === 'left') {
    // 如果正在向左旋转，则停止
    stopRotation();
  } else {
    // 如果未旋转或正在向右旋转，则开始向左旋转
    startRotation(-step, 'left');
  }
};
// ceium截图功能
async function takeScreenshot(): Promise<{ url: string }> {
  let screenshot;

  return new Promise((resolve, reject) => {
    const postRenderHandler = async function () {
      //screenshot = viewer.scene.canvas.toDataURL();

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      canvas.width = viewer.scene.canvas.width / 2;
      canvas.height = viewer.scene.canvas.height / 2;

      ctx?.drawImage(viewer.scene.canvas, 0, 0, canvas.width, canvas.height);

      let compressedScreenshotUrl = canvas.toDataURL('image/png', 0.7);
      let json = {
        base64: compressedScreenshotUrl,
        id: route.query.id,
        longitude: longitudeCopy,
        latitude: latitudeCopy
      }
      localStorage.setItem("newCoverBase64", compressedScreenshotUrl);
      viewer.scene.postRender.removeEventListener(postRenderHandler);
      const { data, error } = await uploadUrl(json);
      resolve({ url: data });
    };

    viewer.scene.postRender.addEventListener(postRenderHandler);

    // 等待一帧渲染完成
    requestAnimationFrame(() => {
      viewer.scene.postRender.removeEventListener(postRenderHandler);
    });
  });
}
// 向右旋转
const rotateRight = () => {
  if (isRotating.value && rotationDirection.value === 'right') {
    // 如果正在向右旋转，则停止
    stopRotation();
  } else {
    // 如果未旋转或正在向左旋转，则开始向右旋转
    startRotation(step, 'right');
  }
};

// 开始旋转
const startRotation = (rotationStep: number, direction: 'left' | 'right') => {
  isRotating.value = true;
  rotationDirection.value = direction;
  rotateInterval = setInterval(() => {
    initialHeading += rotationStep; // 更新旋转角度
    if (initialHeading > 360) initialHeading -= 360; // 确保角度在 0-360 之间
    if (initialHeading < 0) initialHeading += 360; // 确保角度在 0-360 之间

    // 设置相机视角
    const offset = new Cesium.HeadingPitchRange(
      Cesium.Math.toRadians(initialHeading), // 水平方向的旋转角
      -Cesium.Math.toRadians(30), // 垂直平面俯仰角（30 度，向屏幕里倾斜）
      1000 // 相机距离地球球心的距离
    );
    viewer.zoomTo(tileset, offset);
  }, 50); // 每 200 毫秒旋转一次
};

// 停止旋转
const stopRotation = () => {
  if (rotateInterval) {
    clearInterval(rotateInterval); // 清除定时器
    rotateInterval = null;
  }
  isRotating.value = false;
  rotationDirection.value = null;
};

onMounted(async () => {
  // 初始化地图
  await initCesium();
  setTimeout(async () => {
    const { url } = await takeScreenshot();
    console.log('------------封面', url, document.getElementById('cesiumViewer'))
  }, 5000)
});

</script>

<style scoped>
#container {
  padding: 0;
}

/* 回退按钮样式 */
.back-icon {
  position: absolute;
  top: 20px;
  left: 20px;
  z-index: 1000;
  background-color: transparent !important;
  /* 确保背景透明 */
}

.rotation-controls {
  position: absolute;
  top: 50%;
  right: 20px;
  transform: translateY(-50%);
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.rotation-controls button {
  padding: 10px;
  background-color: rgba(37, 37, 37, 0.8);
  color: rgb(245, 242, 242);
  border: none;
  border-radius: 50%;
  cursor: pointer;
  transition: background-color 0.3s ease;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
}

.rotation-controls button:hover {
  background-color: #0056b3;
  color: white;
}
</style>