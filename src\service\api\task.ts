// 任务相关接口
import { request } from '../request';

// 发送新建机场任务保存信息
export function sendTaskSaveInfo(data: Api.Task.TaskItem) {
  return request({ url: '/job/add', method: 'post', data });
}

// 修改机场任务保存信息
export function sendEditTaskSaveInfo(data: Api.Task.TaskItem) {
  return request({ url: `/job/edit/${data.flightJobId}`, method: 'put', data });
}

// 发送机场任务状态修改信息
export function sendTaskStatusInfo(flightJobId: number, status: number) {
  return request({ url: `/job/status/${flightJobId}`, method: 'put', params: { status } });
}

// 获取任务列表
export function fetchTaskList(data: Api.List.Table) {
  return request({ url: '/job/list', method: 'get', params: data });
}

// 获取飞行记录列表
export function fetchFlyHistoryList(params: Api.Task.TaskScreen = { pageNum: 1, pageSize: 10 }) {
  return request({ url: '/job/fly-history', method: 'get', params });
}

// 获取飞行任务记录下拉框
export function fetchFlyRecordList() {
  return request({ url: '/job/fly-record', method: 'get' });
}

// 删除飞行记录列表
export function deleteTaskRecord(eventId: number) {
  return request({ url: `/job/record/${eventId}`, method: 'delete' });
}

// 获取无人机飞行历史轨迹(sn / taskId)
export function fetchTrackInfo(params: { sn: string, taskId?: string }) {
  return request({ url: '/line/flightTrack/trackInfo', method: 'get', params });
}

// 获取无人机飞行计划轨迹(taskId)
export function fetchJobTrackInfo(record_id: string) {
  return request({ url: `/line/flight/getJobById/${record_id}`, method: 'get' });
}

// 一键起飞
export function takeOffToPoint(deviceSN: string, data: object) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/jobs/takeoff-to-point`, method: 'post', data });
}
// 一键返航
export function returnHome(deviceSN: string) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/jobs/return_home`, method: 'post' });
}
export function recoveryJob(deviceSN: string) {
  return request({ url: `/job/recovery/${deviceSN}`, method: 'post' });
}
// 急停
export function emergencyStop(deviceSN: string) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/allStop`, method: 'post' });
}

// 指点飞行
export function takeOffToHere(deviceSN: string, data: object) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/jobs/takeoff-to-point`, method: 'post', data });
}

// 飞行记录历史视频
export function getVideo(recordId: number | undefined) {
  return request({ url: `/media/api/v1/files/flighthistory?recordId=${recordId}`, method: 'get' });
}

// 多点飞行
export function saveMultiFlyPath(deviceSN: string, data: object) {
  return request({ url: `/control/api/v1/devices/${deviceSN}/jobs/point_task`, method: 'post', data });
}

// 飞行报告
export function getFlyReport(recordId: number | undefined) {
  return request({ url: `/job/ir/record/report/${recordId}`, method: 'get' });
}
