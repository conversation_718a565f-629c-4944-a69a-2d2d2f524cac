<template>
  <div>
    <NCard title="设备告警" :bordered="false">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :cols="4" :x-gap="20">
          <NFormItemGi label="组织选择：" path="deptId">
            <NTreeSelect clearable v-model:value="formValue.deptId" filterable key-field="id" label-field="label"
              children-field="children" :options="treeData" :default-value="formValue.deptId" />
          </NFormItemGi>
          <NFormItemGi label="设备名称：" path="deviceName">
            <NInput v-model:value="formValue.deviceName" placeholder="输入设备名称" clearable />
          </NFormItemGi>
          <NFormItemGi label="告警等级：" path="level">
            <NSelect v-model:value="formValue.level" clearable :options="generalLevelOptions" />
          </NFormItemGi>
          <NFormItemGi label="告警类型：" path="module">
            <NSelect v-model:value="formValue.module" clearable :options="generalModuleOptions" />
          </NFormItemGi>
          <NFormItemGi :span="2" label="告警时间：" path="dateRange">
            <NDatePicker v-model:value="formValue.dateRange" type="daterange" :is-date-disabled="disablePreviousDate"
              clearable />
          </NFormItemGi>
          <NFormItemGi>
            <NButton attr-type="button" class="mr2" @click="handleResetClick">重置</NButton>
            <NButton type="primary" attr-type="button" @click="handleQueryClick">查询</NButton>
          </NFormItemGi>
        </NGrid>
      </NForm>
      <NScrollbar style="max-height: 70vh">
        <NDataTable ref="table" remote :columns="columns" :data="tableData" :pagination="pagination"
          :loading="tableLoading" @update:page="onPageChange" @update:page-size="onPageSizeChange" />
      </NScrollbar>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { NButton, NTag } from 'naive-ui';
import { h, onMounted, reactive, ref } from 'vue';
import dayjs from 'dayjs';
import { fetchDeviceHmsList, fetchStatusList } from '@/service/api';
import { deptTree } from "@/service/api/user";

interface TableItem {
  hmsKey: string;
  deviceName: string;
  level: string | null;
  module: string | null;
  messageZh: string;
  createTime: string;
}

const generalLevelOptions = ref<{ label: string; value: string; listClass: string }[]>([]);
const generalModuleOptions = ref<{ label: string; value: string; listClass: string }[]>([]);

const tableData = ref([]);

const columns = [
  {
    title: '组织名称',
    key: 'workspaceName'
  },
  {
    title: '告警码',
    key: 'hmsKey'
  },
  {
    title: '设备名称',
    key: 'deviceName'
  },
  {
    title: '告警等级',
    key: 'level',
    render(row: TableItem) {
      const tags = generalLevelOptions.value.find(v => v.value === String(row.level));
      const label = tags || { label: '未知', value: '-1', listClass: 'info' };
      return h(NTag, { type: label.listClass as Api.DictData.tagType },
        { default: () => label.label });
    }
  },
  {
    title: '告警类型',
    key: 'module',
    render(row: TableItem) {
      const tags = generalModuleOptions.value.find(v => v.value === String(row.module));
      const label = tags || { label: '未知', value: '-1', listClass: 'default' };
      return h(NTag, { type: label.listClass as Api.DictData.tagType }, { default: () => label.label });
    }
  },
  {
    title: '告警内容',
    key: 'messageZh'
  },
  {
    title: '告警时间',
    key: 'createTime'
  }
];

const tableLoading = ref(true);
const treeData = ref([]);
// 禁用日期
const disablePreviousDate = (ts: number) => {
  return ts > Date.now();
};
// 更改分页页数
const onPageChange = (page: number) => {
  pagination.page = page;
  tableLoading.value = true;
  getDeviceHms();
};

const onPageSizeChange = (pageSize: number) => {
  pagination.pageSize = pageSize;
  pagination.page = 1; // 重置页码为第一页
  tableLoading.value = true;
  getDeviceHms();
};
// 表单
const formValue = reactive<{ deptId: number | null; deviceName: string; level: string | null; module: string | null; dateRange: [number, number] | null }>({
  deptId: null,
  deviceName: '',
  level: null,
  module: null,
  dateRange: null
});

// 分页
const pagination = reactive({
  page: 1,
  pageSize: 10,
  itemCount: 0
});

// 查询
const handleQueryClick = () => {
  pagination.page = 1; // 重置页码为第一页
  tableLoading.value = true;
  getDeviceHms();
};

// 重置
const handleResetClick = () => {
  pagination.page = 1; // 重置页码为第一页
  tableLoading.value = true;
  formValue.deptId = null;
  formValue.deviceName = '';
  formValue.level = null;
  formValue.module = null;
  formValue.dateRange = null;
  getDeviceHms();
};

// 获取设备告警
async function getDeviceHms() {
  const { deviceName, level, module, dateRange, deptId } = formValue;
  const param = {
    deptId,
    deviceName,
    level,
    module,
    startDate: dateRange && dateRange[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : null,
    endDate: dateRange && dateRange[1] ? dayjs(dateRange[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null,
    pageNum: pagination.page,
    pageSize: pagination.pageSize
  };
  tableData.value = []; 
  const { error, data } = await fetchDeviceHmsList(param);
  if (!error) {
    tableLoading.value = false;
    tableData.value = data.rows;
    pagination.itemCount = data.total;
  }
}

// 获取告警等级
async function getLevelStatus(dataType: string) {
  const { data } = await fetchStatusList(dataType);
  generalLevelOptions.value = data.map((v: Api.List.StatusItem) => ({
    label: v.dictLabel,
    value: v.dictValue,
    listClass: v.listClass
  }));
}

// 获取告警类型
async function getModuleStatus(dataType: string) {
  const { data } = await fetchStatusList(dataType);
  generalModuleOptions.value = data.map((v: Api.List.StatusItem) => ({
    label: v.dictLabel,
    value: v.dictValue,
    listClass: v.listClass
  }));
}

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
};

onMounted(() => {
  getDeptTree();
  getLevelStatus('hms_level');
  getModuleStatus('hms_module');
  getDeviceHms();
});
</script>


<style scoped></style>
