import { App, nextTick } from 'vue';
import { createP<PERSON> } from 'pinia';
import { resetSetupStore } from './plugins';
import useSocket from '@/service/websocket/useSocket';
import useDetectSocket from '@/service/websocket/useDetectSocket';
/** Setup Vue store plugin pinia */
export async function setupStore(app: App) {
  const store = createPinia();

  store.use(resetSetupStore);

  // console.log("setupStore ~ store:", store)
  app.use(store);

  // 将 WebSocket 对象注入到应用程序的 provide 中
  app.provide('useSocket', useSocket());
  app.provide('useDetectSocket', useDetectSocket());
}
