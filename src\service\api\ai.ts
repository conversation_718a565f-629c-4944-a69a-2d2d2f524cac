// 机场相关接口
import { LocationQueryValue } from 'vue-router';
import { pRequest, request } from '../request';

// 获取AI 配置（是否开启AI识别）
export function fetchAllAIConfig() {
  return request({ url: '/ai/config', method: 'get' });
}

// 获取AI列表
// status - 1: 查询开启的算法 0: 查询关闭的算法 不传默认查询所有
export function fetchAIList(pageSize: number = 100, status: string = '') {
  return request({ url: '/ai/item/list', method: 'get', params: { pageSize, status } });
}

// 获取全局AI设置
export function fetchSysAIList(pageSize: number = 100, status: string = '') {
  return request({ url: '/sysAi/item/aiList', method: 'get', params: { pageSize, status } });
}

// 获取AI识别记录
export function fetchAiRecordList(params: Api.List.Table) {
  return request({
    url: '/ai/record/list',
    method: 'get',
    params
  });
}

// 获取AI识别记录
export function updateAiConfig(data: any) {
  return request({
    url: '/ai/config',
    method: 'put',
    data
  });
}

// 获取AI识别记录
export function updateAiItem(data: Api.AI.AIItem[]) {
  return request({
    url: '/ai/item',
    method: 'put',
    data
  });
}
export function checkAi(data: string) {
  return request({
    url: '/ai/checkAi?code='+data,
    method: 'get'
  });
}

// 更新全局AI设置
export function updateSysAiItem(data: Api.AI.AIItem[]) {
  return request({
    url: '/sysAi/item',
    method: 'put',
    data
  });
}

// 根据ID获取子公司AI配置
export function getDeptAiByID(workspaceId:number) {
  return request({
    url: `/sysAi/item/aiListByWid?workspaceId=${workspaceId}`,
    method: 'get'
  });
}

// 修改子公司AI配置
export function updateDeptAiItem(data: Api.AI.AIItem[]) {
  return request({
    url: `/sysAi/updateItemConfig`,
    method: 'put',
    data
  });
}

// 获取AI识别记录
export function updateRecordConfirm(data: any) {
  return request({
    url: '/ai/record/confirm',
    method: 'put',
    data
  });
}

/**
 * 开启AI直播 params.sn
 * @param input_stream rtmp流链接
 * @param class_ids AI识别类型，数组
 */
export function startAILivestream(data: { input_stream: string, class_ids: number[], dsn: LocationQueryValue | LocationQueryValue[] }) {
  return pRequest({ url: '/add_stream', method: 'post', data });
}

// 关闭AI直播 params.sn
export function stopAILivestream(data: { input_stream: string }) {
  return pRequest({ url: '/remove_stream', method: 'post', data });
}
