<script setup lang="ts">
import { h, onMounted, onBeforeUnmount, reactive, ref } from 'vue';
import { NButton, NText, NInput, useMessage } from 'naive-ui';
import { fetchDroneList } from '@/service/api';
import SvgIcon from '@/components/custom/svg-icon.vue';
import {
  updateNickName
} from '@/service/api';
import {deptTree} from "@/service/api/user";
const message = useMessage();

const tableData = ref([]);
const treeData = ref([]);
const editingRow = ref(null);
const columns = [
  {
    title: '设备序列号',
    key: 'deviceSn'
  },
  {
    title: '设备名称',
    key: 'nickName',
    render(row: any) {
      const nickname = row.nickName;
      const isEditing = editingRow.value === row;
      const displayContent = isEditing
        ? h(NInput, {
          value: nickname,
          class: 'editing-input',
          onBlur: (e: any) => {
            updateAirportName(row, e.target.value, "dock");
          },
          onUpdateValue: (value: string) => {
            row.nickName = value;
          }
        })
        : h('span', { style: { display: 'inline-flex', alignItems: 'center' } }, [
          nickname,
          h(
            'div',
            {
              class: 'ml-5px cursor-pointer edit-icon',
              onClick: (e: MouseEvent) => {
                e.stopPropagation();
                editAirportName(row, "dock");
              },
              style: { display: 'inline-flex', alignItems: 'center' }
            },
            [
              h(SvgIcon, {
                icon: 'mdi:rename-outline'
              })
            ]
          )
        ]);
      return displayContent;
    }


  },
  {
    title: '设备型号',
    key: 'deviceName'
  },
  {
    title: '最后在线时间',
    key: 'lastOnlineTime'
  },
  {
    title: '最后在线经度',
    key: 'latitude'
  },
  {
    title: '最后在线维度',
    key: 'longitude'
  },
  {
    title: '最后在线海波高度',
    key: 'height'
  },
  {
    title: '最后在线机场距离',
    key: 'homeDistance'
  }
];

function renderCell(value: string | number) {
  if (!value) {
    return h(NText, { depth: 3 }, { default: () => '---' });
  }
  return value;
}

const tableLoading = ref(true);

const formValue = reactive<{ deptId: number | null; nickName: string; deviceSn: string }>({
  deptId: null,
  nickName: '',
  deviceSn: ''
});

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  itemCount: 0
});

// 更改分页页数
const onPaginationChange = (page: number) => {
  pagination.pageNum = page;
  tableLoading.value = true;
  getDroneList();
};

// 查询
const handleQueryClick = () => {
  tableLoading.value = true;
  getDroneList();
};

// 重置
const handleResetClick = () => {
  formValue.deptId = null;
  formValue.nickName = '';
  formValue.deviceSn = '';
  tableLoading.value = true;
  getDroneList();
};

async function getDroneList() {
  const { nickName, deviceSn, deptId } = formValue;
  const param = {
    deptId,
    nickName,
    deviceSn,
    orderBy: false,
    isAsc: false,
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize
  };
  const { error, data } = await fetchDroneList(param);
  if (!error) {
    tableLoading.value = false;
    tableData.value = data.rows;
    pagination.itemCount = data.total;
  }
}


onBeforeUnmount(() => {
  document.removeEventListener('click', handleGlobalClick);
});
const handleGlobalClick = (e: MouseEvent) => {
  const target = e.target as HTMLElement;
  if (!target.closest('.editing-input') && !target.closest('.edit-icon')) {
    editingRow.value = null;
  }
};

const editAirportName = (row: any, deviceName: string) => {
  editingRow.value = row;
};
async function updateAirportName(row: any, newName: string, type: string) {
  var sn = '';
  if (type == 'dock') {
    sn = row.deviceSn
    row.nickname = newName;
  } else {
    sn = row.children.deviceSn
    row.nickname = newName;
  }
  const data = {
    sn,
    name: newName
  }
  editingRow.value = row;
  const error = await updateNickName(data);
    message.success(`已更新为: ${newName}`);

};

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
};

onMounted(() => {
  document.addEventListener('click', handleGlobalClick);
  getDeptTree();
  getDroneList();
});
</script>

<template>
  <div>
    <NCard title="无人机" :bordered="false">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :cols="4" :x-gap="36">
          <NFormItemGi label="组织选择：" path="deptId">
            <NTreeSelect
              clearable
              v-model:value="formValue.deptId"
              filterable
              key-field="id"
              label-field="label"
              children-field="children"
              :options="treeData"
              :default-value="formValue.deptId"
            />
          </NFormItemGi>
          <NFormItemGi label="设备名称：" path="nickName">
            <NInput v-model:value="formValue.nickName" placeholder="输入设备名称" clearable />
          </NFormItemGi>
          <NFormItemGi label="设备序列号：" path="deviceSn">
            <NInput v-model:value="formValue.deviceSn" placeholder="输入设备序列号" clearable />
          </NFormItemGi>
          <NFormItemGi>
            <NButton attr-type="button" class="mr2" @click="handleResetClick">重置</NButton>
            <NButton type="primary" attr-type="button" @click="handleQueryClick">查询</NButton>
          </NFormItemGi>
        </NGrid>
      </NForm>
      <NScrollbar style="max-height: 66vh">
        <NDataTable ref="table" remote :columns="columns" :data="tableData" :loading="tableLoading"
          :render-cell="renderCell" />
      </NScrollbar>
      <div class="mt-15px flex justify-end">
        <NPagination v-model:page="pagination.pageNum" :item-count="pagination.itemCount"
          @update:page="onPaginationChange" />
      </div>
    </NCard>
  </div>
</template>

<style scoped></style>
