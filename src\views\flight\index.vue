<template>
  <div class="h100vh position-relative">
    <div id="cesiumViewer" class="h100%"></div>
    <geoMeasureKit id="geoMeasureKit" :viewer="viewer"
    :objSpace="{ place: { top: 10, right: 100 }, width: 30, direction: 'right' }" ref="childRef"
    :style="{  position: 'fixed',
      top: '10px',
      right: '290px',
      zIndex: 1000 }"
    />
    <status-bar v-if="loaded" :viewer="viewer"></status-bar>
    <DeviceList @focus-airport="handleFocusAirport" :airportDeviceList="airportDeviceList"
      class="absolute bottom-0 right-0 w-70 h-100%" />
    <DeviceVideo v-for="(item, index) in airportVideoBoxesList" :key="item.id" :airportVideoIndex="index"
      :class="getDeviceVideoClass(index)" class="absolute w-30vw h-max" />
  </div>
</template>

<script setup lang="ts">
import * as Cesium from 'cesium';
import { computed, createApp, inject, nextTick, onMounted, reactive, ref, toRefs, watch, watchEffect } from 'vue';
import type { App, Ref } from 'vue';
import { cesiumConfig, josiahProvider, labelProvider, position } from '@/config/cesium_config';
import marker from "@/assets/imgs/airport.png";
import alternate from "@/assets/imgs/alternate_0.png"
import { color, number } from 'echarts';
import { fetchAirportDeviceList, fetchDroneStatusList } from '@/service/api';
import { Widget, DomUtil } from '@cesium-extends/common';
import ZoomController from '@cesium-extends/zoom-control';
import statusBar from '@/components/cesium/status-bar.vue'
import DeviceList from './modules/device-list.vue';
import DeviceVideo from './modules/device-video.vue';
import { useAirportStore } from '@/store/modules/airport';
import { useDeviceStore } from '@/store/modules/device';
import geoMeasureKit from '@/components/cesium/geo-measure-kit/index.vue';
type ChatMessageType = string | null;

const airportStore = useAirportStore();
const deviceStore = useDeviceStore();

// const { deviceOSD } = useAirportStore();
// const app = createApp(App)

const socket = inject('useSocket'); // 通过 inject 获取 provide 的 socket 对象

// cesium viewer 初始化
let viewer: Cesium.Viewer;
const loaded = ref(false);
// 右侧展示的机场列表
const airportDeviceList = reactive<Api.Airport.AirportDeviceInfo[]>([])
// 左侧的机场视频DeviceVideo列表
const airportVideoBoxesList = computed(() => airportStore.videoBoxes);

// const airportVideoBoxesList = airportStore.videoBoxes;
// 计算每个 DeviceVideo 的位置，最多4个
const getDeviceVideoClass = (index: number) => {
  switch (index) {
    case 0:
      return 'top-5vh left-10px';
    case 1:
      return 'bottom-5vh left-10px';
    case 2:
      return 'top-5vh right-310px';
    case 3:
      return 'bottom-5vh right-310px';
    default:
      return '';
  }
};
const childRef = ref();
// 初始化地图
const initCesium = () => {
  // // viewer.scene.globe.depthTestAgainstTerrain = true;  //启用深度测试
  viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
  childRef.value?.childMethod(viewer);
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; //修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  loaded.value = true;

  // 修改图层
  // viewer.imageryLayers.removeAll();
  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider);

  const zoomController = new ZoomController(viewer, {
    container: document.getElementById('cesiumViewer') as HTMLElement,
    // home: Cesium.Cartesian3.fromDegrees(position.coords.longitude, position.coords.latitude, 10000),
    tips: {
      zoomIn: '放大',
      zoomOut: '缩小',
      refresh: '重置缩放',
    },
  });
  // zoomController.show()


  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置相机位置为当前定位点
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(position.coords.longitude, position.coords.latitude, 10000), // 设置目的地高度/米
    orientation: {
      // heading: Cesium.Math.ZERO,
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });

  // viewer.entities.add(airportPoint)
  // const entity = viewer.entities.add(airportPoint);

  // viewer.zoomTo(entity);


  // 加载禁飞区JSON文件并绘制禁飞区
  loadJSONAndDrawNoFlyZone();

}

// 生成机场范围椭圆的轮廓点
function computeEllipsePositions(
  center: { longitude: number; latitude: number },
  semiMajorAxis: number,
  semiMinorAxis: number,
  granularity: number
): Cesium.Cartesian3[] {
  const positions: Cesium.Cartesian3[] = [];
  for (let i = 0; i < 360; i += granularity) {
    const radians = Cesium.Math.toRadians(i);
    const x = semiMajorAxis * Math.cos(radians);
    const y = semiMinorAxis * Math.sin(radians);
    // 将米转换为经纬度
    const position = Cesium.Cartesian3.fromDegrees(center.longitude + x / 111319.9, center.latitude + y / 111319.9);
    positions.push(position);
  }
  // 闭合椭圆，使首尾相连
  positions.push(positions[0]);
  return positions;
}

// 绘制机场点位及范围
function drawAirportPoint(points: Api.Airport.MachineNestInfo[]) {
  points.forEach((point: Api.Airport.MachineNestInfo) => {
    const { longitude, latitude, distance, deviceName, landPointLongitude, landPointLatitude } = point;
    const center = { longitude, latitude }; // 明确指定中心点类型

    // 生成椭圆边界点
    const positions = computeEllipsePositions(center, distance, distance, 5);

    // 绘制机场范围（用Polygon模拟椭圆）
    viewer.entities.add({
      polygon: {
        hierarchy: positions,  // 多边形的顶点
        material: Cesium.Color.fromCssColorString('#1177fb').withAlpha(0.15), // 设置填充颜色
        perPositionHeight: false,  // 使多边形所有点贴地
      },
      description: 'airpoint',
    });

    // 绘制椭圆边框
    viewer.entities.add({
      polyline: {
        positions: positions,  // 线的顶点位置，与多边形顶点一致
        width: 5,  // 边框宽度
        material: Cesium.Color.fromCssColorString('#1177fb'),  // 边框颜色
        clampToGround: true,  // 使边框贴地
      },
      description: 'airpointborder',
    });

    // 绘制机场图标
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      billboard: {
        image: marker,
        scale: 0.13
      },
      description: 'airpointicon',
    });

    if (landPointLatitude && landPointLongitude) {
      // 绘制机场备件点图标
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(landPointLongitude, landPointLatitude),
        billboard: {
          image: alternate,
          scale: 0.13
        },
        description: 'airpointbackupicon'
      });

      // 绘制机场备降点标签
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(landPointLongitude, landPointLatitude),
        label: {
          text: `${deviceName}-备降点`,
          font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
          fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
          outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 8.0,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(0, -50), // 向上偏移50像素
        },
        description: 'airpointtext',
      });
    }

    // 绘制机场标签
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      label: {
        text: deviceName,
        font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
        fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
        outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 8.0,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        pixelOffset: new Cesium.Cartesian2(0, -50), // 向上偏移50像素
      },
      description: 'airpointtext',
    });
  });
}

// 获取已激活的机场列表
async function getAirportDeviceList() {
  const { data } = await fetchAirportDeviceList();
  Object.assign(airportDeviceList, data.rows);
  drawAirportPoint(data.rows);
}

// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
  try {
    const response = await fetch('/defaultNoFlyZone.json');
    const data = await response.json();
    // 绘制
    addPolygons(data.features);
  } catch (error) {
    console.error('Error loading JSON data:', error);
  }
};

// 绘制禁飞区
function addPolygons(features: any[]) {
  features.forEach(feature => {
    const polygon = feature.geometry.coordinates[0];
    const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    viewer.entities.add({
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString('red').withAlpha(0.35),
      }
    });
  });
};

// 接收右侧机场列表“点击定位”事件
function handleFocusAirport(index: number) {
  // console.log("handleFocusAirport ~ index:", index);
  const airportItem = airportDeviceList[index];
  // 缩放至机场定位
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(airportItem.longitude || 0, airportItem.latitude || 0, 10000), // 设置目的地高度为10000米
    orientation: {
      // heading: Cesium.Math.ZERO,
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });
}

// 获取无人机设备状态码列表
async function getDroneStatusList() {
  if (deviceStore.droneStatusList.length <= 0) {
    const { data, error } = await fetchDroneStatusList();
    // console.log("getDroneStatusList ~ data:", data)
    if (error) return;
    deviceStore.setDroneStatusList(data);
  }
}

// 处理socket消息
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  if (biz_code === 'device_osd') { // 无人机消息
    const index = airportDeviceList.findIndex(item => item.childDevice && item.childDevice.droneSn === sn);

    if (index !== -1) { // 替换找到的项
      // const { modeCode } = data.host;
      let newItem = { childDevice: { ...airportDeviceList[index].childDevice, host: data.host } };
      // newItem.childDevice = { ...newItem.childDevice, }
      airportDeviceList[index] = { ...airportDeviceList[index], ...newItem };
    }
  } else if (biz_code === 'dock_osd') { // 机场消息
    const index = airportDeviceList.findIndex(item => item.deviceSn === sn);

    if (index !== -1) { // 替换找到的项
      // const { modeCode } = data.host;
      let newItem = { ...airportDeviceList[index], host: data.host || {} };
      console.log("onProcessMessage ~ index:", newItem)
      airportDeviceList[index] = { ...airportDeviceList[index], ...newItem };
    }
  } else if (biz_code === 'device_hms') { // 设备告警
    airportStore.setDeviceEmergency(msg.data);
  }
}

onMounted(async () => {
  // 初始化地图
  initCesium();
  // 获取已激活的机场列表
  getAirportDeviceList();
  getDroneStatusList();
})


</script>

<style scoped>
#container {
  padding: 0;
}
</style>
