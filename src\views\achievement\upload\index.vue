<template>
  <div class="upload-page">
    <!-- 三维模型  -->
    <section class="upload-section">
      <div class="upload-block" @click="handleUpload('3d')">
        <h2>三维模型</h2>
        <div class="file-types">
          <img src="https://www.kitebeam.com/sewmap/images/upload/b3dm.svg" alt="b3dm" class="file-icon" />
          <!-- <n-icon size="40" color="white">
            <FileTraySharp />
          </n-icon> -->
        </div>
        <button class="upload-button" id="btn">
          点击选择文件夹上传
        </button>
        <p class="upload-hint">
          三维模型支持 b3dm 格式
        </p>
      </div>
    </section>

    <!-- 文件选择输入 -->
    <!-- <input type="file" ref="fileInput" :accept="acceptTypes" @change="handleFileChange" style="display: none" /> -->

    <!-- 文件夹选择输入 -->
    <input type="file" ref="folderInput" webkitdirectory @change="handleFolderChange" style="display: none" />

    <!-- 整体文件夹上传信息表格 -->
    <div v-if="showTable" class="file-table-container">
      <n-data-table :columns="columns" :data="tableData" :bordered="false" class="file-table" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, h, watch } from 'vue';
import { NDataTable, NProgress } from 'naive-ui';
import COS from 'cos-js-sdk-v5'; // 引入腾讯云 COS SDK
import pLimit from 'p-limit';
import { getOssConfig, finish } from '@/service/api/results'; // 假设这是获取腾讯云配置的接口
import { FileTraySharp } from '@vicons/ionicons5'
import { off } from 'process';
import { useMessage } from 'naive-ui'
// 文件类型定义
interface FileEntry {
  file: File;
  relativePath: string; // 保留文件夹结构
  size: number;
  progress: number; // 上传进度
  status: string; // 上传状态
}

const files = ref<FileEntry[]>([]); // 文件列表
const uploading = ref(false); // 是否正在上传
const folderName = ref(''); // 文件夹名称
const totalSize = ref(0); // 文件夹总大小
const acceptTypes = ref(''); // 文件类型限制
const showTable = ref(false); // 是否展示表格
const fileInput = ref<HTMLInputElement | null>(null);
const folderInput = ref<HTMLInputElement | null>(null);
const cosConfig = ref<any>(null); // 存储腾讯云 COS 配置
const message = useMessage()
// 表格列定义
const columns = [
  {
    title: '名称',
    key: 'folderName',
  },
  {
    title: '大小',
    key: 'totalSize',
    render: () => formatFileSize(totalSize.value),
  },
  {
    title: '进度',
    key: 'progress',
    render: () =>
      h(NProgress, {
        type: 'line',
        percentage: overallProgress.value,
        status: overallProgress.value < 100 ? 'info' : 'success',
      }),
  },
  {
    title: '状态',
    key: 'status',
    render: () => (overallProgress.value < 100 ? '上传中...' : '上传完成'),
  },
];

// 表格数据
const tableData = computed(() => {
  return [
    {
      folderName: folderName.value,
      totalSize: totalSize.value,
      progress: overallProgress.value,
      status: overallProgress.value < 100 ? '上传中...' : '上传完成',
    },
  ];
});

// 计算整体上传进度
const overallProgress = computed(() => {
  if (files.value.length === 0) return 0;
  const totalProgress = files.value.reduce((sum, file) => sum + file.progress, 0);
  return (totalProgress / files.value.length);
});

let btn: HTMLElement | null = document.getElementById("btn")
if (btn) {
  btn.onclick = function (e) {
    //得到事件源对象
    e = e || window.event
    //阻止事件向上冒泡 stopPropagation 这个方法可以阻止事件冒泡 且遵从w3c规则 (兼容各大浏览器 ie9之前不兼容)
    e.stopPropagation()
    handleUploadSon('3d')
  }
}
const handleUploadSon = (type: '3d' | '2d') => {
  if (type === '3d') {
    acceptTypes.value = '.b3dm'; // 三维模型文件类型
    folderInput.value?.click(); // 触发文件夹选择
  } else if (type === '2d') {
    acceptTypes.value = '.geotiff,.tif,.trif,.jpg'; // 二维模型和全景照片文件类型
    fileInput.value?.click(); // 触发文件选择
  }
};
// 处理上传按钮点击
const handleUpload = (type: '3d' | '2d') => {
  if (type === '3d') {
    acceptTypes.value = '.b3dm'; // 三维模型文件类型
    folderInput.value?.click(); // 触发文件夹选择
  } else if (type === '2d') {
    acceptTypes.value = '.geotiff,.tif,.trif,.jpg'; // 二维模型和全景照片文件类型
    fileInput.value?.click(); // 触发文件选择
  }
};

// 处理文件选择
// const handleFileChange = (event: Event) => {
//   const input = event.target as HTMLInputElement;
//   if (input.files && input.files.length > 0) {
//     const file = input.files[0];
//     const fileType = file.name.split('.').pop()?.toLowerCase();
//     const fileSize = file.size; // 文件大小（字节）

//     // 检查文件类型和大小
//     if (acceptTypes.value.includes(fileType || '')) {
//       if (acceptTypes.value.includes('.b3dm') && fileSize > 100 * 1024 * 1024 * 1024) {
//         alert('文件大小不能超过 100GB');
//       } else if (acceptTypes.value.includes('.jpg') && fileSize > 2 * 1024 * 1024 * 1024) {
//         alert('文件大小不能超过 2GB');
//       } else {
//         files.value = [{ file, relativePath: file.name, size: file.size, progress: 0, status: '上传中...' }];
//         folderName.value = file.name;
//         totalSize.value = file.size;
//         showTable.value = true; // 显示表格
//         startUpload();
//       }
//     } else {
//       alert('文件类型不支持');
//     }
//   }
// };

// 处理文件夹选择
const handleFolderChange = async (event: Event) => {
  const input = event.target as HTMLInputElement;
  if (input.files) {
    const filesArray = Array.from(input.files);

    // 允许的文件扩展名
    const allowedExtensions = ['.b3dm', '.json'];

    // 检查文件夹中是否包含非允许的文件
    const invalidFiles = filesArray.filter(
      (file) => !allowedExtensions.some((ext) => file.name.toLowerCase().endsWith(ext))
    );

    if (invalidFiles.length > 0) {
      message.error('文件夹中包含不支持的文件类型，请仅上传 .b3dm 和 .json 文件！');
      return; // 终止上传
    }

    // 如果文件夹中只包含允许的文件，继续处理
    files.value = filesArray.map((file) => ({
      file,
      relativePath: (file as any).webkitRelativePath, // 保留文件夹路径
      size: file.size,
      progress: 0,
      status: '上传中...',
    }));

    folderName.value = input.files[0].webkitRelativePath.split('/')[0]; // 获取文件夹名称
    totalSize.value = files.value.reduce((sum, file) => sum + file.size, 0); // 计算文件夹总大小
    showTable.value = true; // 显示表格

    // 获取腾讯云 COS 配置
    const { data } = await getOssConfig();
    console.log('--------------', data);
    cosConfig.value = data;

    // 开始上传
    startUpload();
  }
};

// 开始上传
const startUpload = async () => {
  if (!files.value.length || !cosConfig.value) return;

  uploading.value = true;

  // 初始化腾讯云 COS 客户端
  const cos = new COS({
    SecretId: cosConfig.value.credentials.access_key_id,
    SecretKey: cosConfig.value.credentials.access_key_secret,
    SecurityToken: cosConfig.value.credentials.security_token,
    SliceSize: 1 * 1024 * 1024, // 设置分片大小为 5MB
  });

  // for (let i = 0; i < files.value.length; i++) {
  //   const fileEntry = files.value[i];
  //   uploadFileToCOS(cos, fileEntry);
  // }
  // 设置并发数为 5
  const limit = pLimit(5);
  const uploadPromises = files.value.map((fileEntry) =>
    limit(() => uploadFileToCOS(cos, fileEntry))
  );

  await Promise.all(uploadPromises);
  uploading.value = false;
};

// 上传单个文件到腾讯云 COS
const uploadFileToCOS = (cos: any, fileEntry: FileEntry) => {
  return new Promise((resolve, reject) => {
    const key = `${cosConfig.value.objectKeyPrefix}/${fileEntry.relativePath}`; // 保留文件夹结构

    cos.sliceUploadFile(
      {
        Bucket: cosConfig.value.bucket, // Bucket 名称
        Region: cosConfig.value.region, // 区域
        Key: key, // 文件在 COS 中的路径
        Body: fileEntry.file, // 文件内容
        onProgress: (progress: any) => {
          fileEntry.progress = Math.floor(progress.percent * 100); // 更新上传进度
        },
      },
      (err: any, data: any) => {
        if (err) {
          console.error('上传出错:', err);
          reject(err);
        } else {
          fileEntry.status = '上传完成';
          resolve(data);
        }
      }
    );
  });
};

// 监听 overallProgress 的变化
watch(overallProgress, (newValue) => {
  if (newValue === 100) {
    // 上传完成，调用后端接口
    notifyUploadComplete();
  }
});

// 通知后端上传完成
const notifyUploadComplete = async () => {
  try {
    const response = await finish({
      fileName: folderName.value,
      id: cosConfig.value.id,
      size: totalSize.value,
    });
    console.log('上传完成通知成功:', response.data);
  } catch (error) {
    console.error('上传完成通知失败:', error);
  }
};

// 格式化文件大小
const formatFileSize = (size: number) => {
  if (size < 1024) return `${size} B`;
  if (size < 1024 * 1024) return `${(size / 1024).toFixed(2)} KB`;
  if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(2)} MB`;
  return `${(size / (1024 * 1024 * 1024)).toFixed(2)} GB`;
};
</script>

<style scoped>
.upload-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  font-family: Arial, sans-serif;
  background-color: #333;
  /* 背景色 */
  color: white;
  /* 文字颜色 */
  height: 100%;
}

.upload-section {
  width: 100%;
  max-width: 600px;
  margin-bottom: 20px;
}

.upload-block {
  background-color: #444;
  /* 模块背景色 */
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  /* 内容水平居中 */
  text-align: center;
  /* 文字居中 */
  transition: box-shadow 0.3s ease;
  /* 添加过渡效果 */
}

.upload-block:hover {
  box-shadow: 0 0 0 2px #00aaff;
  /* 蓝色虚线边框效果 */
}

h2 {
  font-size: 18px;
  margin-bottom: 10px;
}

.file-types {
  display: flex;
  gap: 20px;
  /* 图标间距 */
  margin-bottom: 10px;
}

.file-icon {
  width: 40px;
  /* 图标宽度 */
  height: 40px;
  /* 图标高度 */
}

.upload-button {
  display: block;
  width: 100%;
  max-width: 300px;
  padding: 10px;
  background-color: #555;
  /* 按钮背景色 */
  color: white;
  /* 按钮文字颜色 */
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  margin-bottom: 10px;
}

.upload-button:hover {
  background-color: #666;
  /* 按钮悬停背景色 */
}

.upload-hint {
  font-size: 12px;
  color: #aaa;
  /* 提示文字颜色 */
  line-height: 1.5;
  text-align: center;
  /* 提示文字居中 */
}

.file-table-container {
  width: 100%;
  margin-top: 20px;
}

.file-table {
  width: 100%;
}
</style>