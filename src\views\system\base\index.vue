<template>
  <div>
    <NCard title="个人中心" :bordered="false">
      <!-- <n-divider /> -->
      <n-form ref="formRef" :model="tableVal" label-placement="left" label-width="100px"
        require-mark-placement="right-hanging" class="mt-5 w-25vw">

        <n-form-item label="用户名" path="tableVal.nickName">
          <n-input v-model:value="tableVal.nickName" :disabled="true" />
        </n-form-item>
        <n-form-item label="登录名" path="tableVal.userName">
          <n-input v-model:value="tableVal.userName" :disabled="true" />
        </n-form-item>
        <n-form-item label="所在组织" path="tableVal.deptName">
          <n-input v-model:value="tableVal.deptName" :disabled="true" />
        </n-form-item>
        <n-form-item label="手机号" path="tableVal.phonenumber">
          <n-input v-model:value="tableVal.phonenumber" :disabled="true" />
        </n-form-item>
        <!-- <n-form-item>
          <n-button attr-type="button" type="primary" @click="handleSaveClick">
            保存
          </n-button>
        </n-form-item> -->
      </n-form>
    </NCard>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { updateGetSysInfo } from '@/service/api';
import { useMessage } from 'naive-ui'
import type { UploadFileInfo } from 'naive-ui'


const appStore = useAppStore();
const authStore = useAuthStore();
const { userInfo, token } = authStore;
const serviceBaseURL = import.meta.env.VITE_SERVICE_BASE_URL;
const message = useMessage()

const tableVal = reactive({
  phonenumber: '',
  userName: '',
  deptName: '',
  nickName: ''
})


onMounted(() => {
  tableVal.phonenumber = userInfo.user.phonenumber || '';
  tableVal.userName = userInfo.user.userName || '';
  tableVal.deptName = userInfo.user.dept.deptName || '';
  tableVal.nickName = userInfo.user.nickName || '';
})

</script>


<style scoped></style>
