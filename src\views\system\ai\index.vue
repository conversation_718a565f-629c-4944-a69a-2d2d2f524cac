<template>
  <div>
    <n-dialog-provider>
      <NCard :bordered="false" title="全局AI设置">
        <NText class="pb-4 inline-block">修改此全局AI设置将对所有组织生效。</NText>
        <NGrid x-gap="12" :cols="5" y-gap="24">
          <NGi v-for="(item, index) in AIList" :key="index">
            <NCard hoverable>
              <template #cover>
                <div class="border-b-1px border-dark-100 pl-20px font-size-4.5 line-height-12">
                  <NText>{{ item.name }}</NText>
                </div>
                <div class="p-20px">
                  <NForm ref="formRef" :model="item" label-placement="left" label-width="auto" label-align="left">
                    <NFormItemGi :span="12" label="开启识别" path="switchValue">
                      <NSwitch :value="Boolean(item.status)" @update:value="val => handleSwitchUpdate(index, val)" />
                    </NFormItemGi>
                  </NForm>
                </div>
              </template>
            </NCard>
          </NGi>
        </NGrid>
        <div class="flex justify-end">
          <NButton type="primary" @click="saveSettings" class="mt-4">保存设置</NButton>
        </div>
      </NCard>
    </n-dialog-provider>
  </div>
</template>


<script setup lang="ts">
import { onMounted, ref } from 'vue';
import { useMessage, useDialog } from 'naive-ui';
import { fetchSysAIList, updateSysAiItem } from '@/service/api/ai';

const message = useMessage();
const dialog = useDialog();

const allAIWatch = ref(false);

const AIList = ref<Api.AI.AIItem[]>([]);

// 处理AI算法开关更新事件
async function handleSwitchUpdate(index: number, value: boolean) {
  // const { error } = await updateSysAiItem(AIList.value);
  // if (!error) {
  //   allAIWatch.value = value;
  // 更新开关状态
  AIList.value[index].status = value;
  // message.success('操作成功');
  // }
};

async function getAIList() {
  const { data } = await fetchSysAIList();
  AIList.value = data.rows;
}

async function saveSettings() {
  dialog.warning({
    title: '确认',
    content: '您确定要保存全局AI设置吗？',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const { error } = await updateSysAiItem(AIList.value);
      if (!error) {
        message.success('设置已保存');
      }
    },
  });

}

onMounted(() => {
  getAIList();
});

</script>

<style></style>
