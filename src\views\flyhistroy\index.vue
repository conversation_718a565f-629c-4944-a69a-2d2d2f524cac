<template>
  <div class="h100vh relative">
    <div id="cesiumViewer" class="h80%"></div>
    <!-- 新增：自定义视频控制条 -->
    <div class="custom-video-controls">
      <!-- 播放/暂停按钮 -->
      <button @click="togglePlay" class="play-pause-btn">
        <svg v-if="!isPlaying" viewBox="0 0 24 24" width="24" height="24">
          <path fill="white" d="M8 5v14l11-7z" />
        </svg>
        <svg v-if="isPlaying" viewBox="0 0 24 24" width="24" height="24">
          <path fill="white" d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" />
        </svg>
      </button>
      <!-- 进度条 -->
      <!-- <input type="range" v-model="progressC" min="0" max="1" step="0.01" @input="handleSeek"
                @change="handleSeekEnd"> -->
      <n-space vertical style="width: 86%;">
        <n-slider v-model:value="progressC" :step="0.0001" :min="0" :max="1" :on-dragend="handleSeekEnd"
          :disabled='dragstart' :tooltip="false" :on-update="handleSeek" style="width: 90%vw !important;" />
      </n-space>
      <!-- 时间显示 -->

      <span style="color: white;">{{ formattedCurrentTime }} / {{ formattedDuration }}</span>

      <!-- 倍速选择 -->
      <select v-model="playbackRate" @change="changePlaybackRate">
        <option value="0.5">0.5x</option>
        <option value="1">1x</option>
        <option value="1.5">1.5x</option>
        <option value="2">2x</option>
      </select>
    </div>
    <status-bar id="status-bar" class="w-300px h-max left-0" v-if="loaded" :viewer="viewer"></status-bar>

    <!-- 上侧状态栏 -->
    <flyAnchor :taskName='taskName' />

    <!-- 底部状态栏 -->
    <flyRecord ref="flyRecordRef" @cancel="cancel" :videoUrl='videoUrl' :trackUrl="trackUrl"
      @updateProgress="updateProgress" @updateProgressTrack="updateProgressTrack" :targetPointVlaue="targetPointVlaue"
      @pause="pause" @updateDuration="duration = $event" />

    <!-- 无人机负载操作 -->
    <dronePayload
      v-show="taskStore.ptzControlStoreStatus && CanHandelPTZModeList.some(mode => mode === airportDroneItem.host?.modeCode)" />
  </div>
</template>

<script setup lang="ts">
import * as Cesium from 'cesium';
import { Ref, computed, h, inject, onBeforeMount, onBeforeUnmount, onBeforeUpdate, onMounted, reactive, ref, toRefs, watch } from 'vue';
import { MouthPositionHandler, cesiumConfig, josiahProvider, labelProvider, position } from '@/config/cesium_config';
import marker from "@/assets/imgs/airport.png";
import alternate from "@/assets/imgs/alternate_0.png";
import arrow from "@/assets/imgs/arrow.png";
import drone_marker from "@/assets/imgs/drone-icon.png";
import { fetchAirportDeviceList, fetchDroneStatusList, fetchTrackInfo, takeOffToHere, getVideo } from '@/service/api';
import statusBar from '@/components/cesium/status-bar.vue'
import flyRecord from './modules/fly-record.vue';
import flyAnchor from './modules/fly-anchor.vue';
import dronePayload from './modules/drone-payload.vue';
import { useAirportStore } from '@/store/modules/airport';
import { useTaskStore } from '@/store/modules/task';
import { NButton, NotificationReactive, useModal, useNotification } from 'naive-ui';
import { LocationQueryValue, useRoute } from 'vue-router';
import { useDeviceStore } from '@/store/modules/device';
import { provide } from 'vue';
import { HeightReference } from '@/typings/Cesium';
// 可以手动控制云台的modeCode列表
const CanHandelPTZModeList = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '16', '17', '18', '19'];
const saveModeList = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '14', '16', '17', '18', '19'];
const airportStore = useAirportStore();
const deviceStore = useDeviceStore();
const taskStore = useTaskStore();
const route = useRoute();
const socket = inject<{ chatMessage: Ref<string> }>('useSocket');  // 通过 inject 获取 provide 的 socket 对象
const notification = useNotification();
// cesium viewer 初始化
let viewer: Cesium.Viewer;
let handler: Cesium.ScreenSpaceEventHandler | null = null;
let longitudeD = ref(0);
let latitudeD = ref(0);
let dragstart = ref(true);
let polyline: Cesium.Entity | null = null;
const trackData = ref<{
  date: string;
  longitude: number;
  latitude: number;
  altitude: number;
  time: Cesium.JulianDate;
}[]>([]);
const trackDataC = ref<{
  date: string;
  longitude: number;
  latitude: number;
  altitude: number;
  time: Cesium.JulianDate;
}[]>([]);
// 新增的响应式数据和引用
const flyRecordRef = ref(); // 用于访问子组件
const isPlaying = ref(false);
const progressC = ref(0);
const playbackRate = ref(1);
const currentTimeC = ref(0);
const duration = ref(0);
// 格式化时间显示
const formattedCurrentTime = computed(() => {
  return formatTime(currentTimeC.value);
});

const formattedDuration = computed(() => {
  return formatTime(duration.value);
});

function formatTime(seconds: number) {
  const date = new Date(seconds * 1000);
  return date.toISOString().substr(11, 8);
}
const videoControls = reactive({
  play: () => {
    flyRecordRef.value?.droneVideoRef?.videoRef.play();
    isPlaying.value = true;
  },
  pause: () => {
    flyRecordRef.value?.droneVideoRef?.videoRef.pause();
    isPlaying.value = false;
  },
  seek: (time: number) => {
    if (flyRecordRef.value.droneVideoRef.videoRef) {
      flyRecordRef.value.droneVideoRef.videoRef.currentTime = time;
    }
  },
  setPlaybackRate: (rate: number) => {
    if (flyRecordRef.value?.droneVideoRef?.videoRef) {
      flyRecordRef.value.droneVideoRef.videoRef.playbackRate = rate;
    }
  }
});
// 播放/暂停切换
const togglePlay = () => {
  console.log('bof', isPlaying.value, flyRecordRef.value?.droneVideoRef?.videoRef)
  if (isPlaying.value) {
    videoControls.pause();
  } else {
    videoControls.play();
  }
};

// 进度条拖动处理
const handleSeek = () => {
};

const handleSeekEnd = () => {
  console.log("跳转")
  videoControls.seek(progressC.value * duration.value);
};

// 倍速改变
const changePlaybackRate = () => {
  videoControls.setPlaybackRate(parseFloat(playbackRate.value + ''));
  viewer.clock.multiplier = playbackRate.value; // 关键：同步 Cesium 时钟速率
};
// 定义 trackController 的接口
interface TrackController {
  updateDronePosition: (lon: number, lat: number, alt: number) => void;
  getTrackData: () => any[]; // 假设 trackData 是数组类型，可根据实际情况修改
}

const loaded = ref(false);
const isFullScreen = ref(false)
// 机场列表
const airportDeviceList = reactive<Api.Airport.AirportDeviceInfo[]>([]);
const flyInfoDroneSN = ref();
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});

// 飞行轨迹信息
const trackPoints = ref<Api.AirLine.WayPointList[]>([]);
let droneEntity: Cesium.Entity; // 用于存储当前的无人机图标实体
// 存储所有添加的实体
const allEntities: Cesium.Entity[] = [];
const videoUrl = ref('')
const trackUrl = ref('')
const taskName = ref('')
const dockSn = ref('')
const recordId = ref()
const ready = ref(false)
// 初始化地图
const initCesium = () => {
  // popDialog.show = false;
  const viewerOptions = {
    ...cesiumConfig,
  };
  viewer = new Cesium.Viewer('cesiumViewer', viewerOptions);
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.scene.globe.depthTestAgainstTerrain = true;  //启用深度测试
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; //修改地图背景
  setTimeout(() => {
    viewer.scene.postProcessStages.fxaa.enabled = true; // 延迟开启
  }, 5000); // 5秒后启用
  loaded.value = true;

  // 修改图层
  // viewer.imageryLayers.removeAll();
  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider);
  // 初始化时钟设置
  viewer.clock.shouldAnimate = true;
  viewer.clock.multiplier = 1; // 动画速度（可调整）
  viewer.clock.clockRange = Cesium.ClockRange.CLAMPED; // 播放到结尾就停止
  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // **新增：根据 dockSn 查找机场坐标**
  let targetLongitude: number, targetLatitude: number, targetAltitude: number = 1200; // 默认海拔

  console.log(35465, longitudeD.value, latitudeD.value)
  if (longitudeD.value && latitudeD.value) {
    targetLongitude = longitudeD.value;
    targetLatitude = latitudeD.value;
    targetAltitude = 1200; // 可自定义机场定位海拔
  } else {
    // 无匹配时使用设备坞站坐标或默认坐标
    targetLongitude = deviceStore.dockInfo?.longitude || position.coords.longitude || 116.4810; // 示例：北京经度
    targetLatitude = deviceStore.dockInfo?.latitude || position.coords.latitude || 39.9219; // 示例：北京纬度
  }

  // **设置相机定位到目标坐标**
  const targetCartesian = Cesium.Cartesian3.fromDegrees(targetLongitude, targetLatitude, targetAltitude);
  viewer.camera.flyTo({
    destination: targetCartesian,
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-30), // 俯视30度，提升视觉效果
      roll: 0.0
    },
    duration: 1.0 // 延长飞行动画时长，提升体验
  });
  loadJSONAndDrawNoFlyZone();
}
function findPointByTime(time: Cesium.JulianDate, points: any[]): TrackPoint | null {
  if (points.length === 0) {
    return null;
  }
  let minDiff = Infinity;
  let closestPoint = null;
  const targetSeconds = time.secondsOfDay;
  for (let i = 0; i < points.length; i++) {
    const currentPoint = points[i];
    const diff = Math.abs(currentPoint.time.secondsOfDay - targetSeconds);
    if (diff < minDiff) {
      minDiff = diff;
      closestPoint = currentPoint;
    }
  }
  return closestPoint;
}
watch(() => progressC.value, (newVal) => {
  if (newVal == 1) {
    isPlaying.value = false
  }
})
// 解析并按时间排序轨迹数据
function parseTrackData(jsonData: any[], intervalSec = 0) {
  if (!jsonData || jsonData.length === 0) return [];
  let startIndex = -1;
  let endIndex = -1;
  if (videoUrl.value) {
    for (let i = 0; i < jsonData.length; i++) {
      if (jsonData[i].eventPoint === "视频录制开始") {
        startIndex = i;
        break;
      }
    }

    for (let i = jsonData.length - 1; i >= 0; i--) {
      if (jsonData[i].eventPoint === "视频录制结束") {
        endIndex = i;
        break;
      }
    }
  } else {
    startIndex = 0
    for (let i = jsonData.length - 1; i >= 0; i--) {
      if (jsonData[i].eventPoint === "视频录制结束") {
        endIndex = i;
        break;
      }
    }
  }

  if (startIndex === -1 || endIndex === -1 || startIndex > endIndex) {
    return [];
  }

  const relevantData = jsonData.slice(startIndex, endIndex + 2);

  const sampled = [relevantData[0]];
  let lastTimestamp = new Date(relevantData[0].date).getTime();

  for (let i = 1; i < relevantData.length; i++) {
    const currentTimestamp = new Date(relevantData[i].date).getTime();
    if (currentTimestamp - lastTimestamp >= intervalSec * 1000) {
      sampled.push(relevantData[i]);
      lastTimestamp = currentTimestamp;
    }
  }

  if (sampled[sampled.length - 1] !== relevantData[relevantData.length - 1]) {
    sampled.push(relevantData[relevantData.length - 1]);
  }


  // 转换格式
  return sampled.map(point => {
    try {
      return {
        ...point,
        time: Cesium.JulianDate.fromIso8601(point.date.replace(' ', 'T')),
        longitude: parseFloat(point.longitude),
        latitude: parseFloat(point.latitude),
        altitude: parseFloat(point.altitude)
      };
    } catch (error) {
      console.error('解析轨迹点时间时出错:', point, error);
      return {
        ...point,
        time: undefined,
        longitude: parseFloat(point.longitude),
        latitude: parseFloat(point.latitude),
        altitude: parseFloat(point.altitude)
      };
    }
  });
}

// 在 createTimeDynamicTrack 中存储轨迹总时间
let trackStartTime: Cesium.JulianDate | null = null;
let trackEndTime: Cesium.JulianDate | null = null;
// 加载轨迹数据
function loadTrackData(trackUrl: string, intervalSec = 0) {
  fetch(trackUrl)
    .then(response => response.json())
    .then(data => {
      // // 先进行1秒插值处理
      // const interpolatedData = interpolateTrackData(data);
      // trackData.value = interpolatedData;
      const sortedPoints = parseTrackData(data, intervalSec); // 保持原有解析逻辑
      trackDataC.value = sortedPoints;

      createTimeDynamicTrack(trackDataC.value);
    })
    .catch(error => console.error('轨迹数据加载失败:', error));
}
function cancel() {
  clearOldTrack();
  localStorage.removeItem('droneFlightState'); // 清除保存的飞行状态
}
interface TrackPoint {
  date: string;
  longitude: number;
  latitude: number;
  altitude: number;
  attitudeHead: string;
  attitudePitch: string;
  attitudeRoll: string;
  battery: string;
  batteryCapacityPercent: string;
  camerasZoomFactor: string;
  elevation: string;
  height: string;
  homeDistance: string;
  horizontalSpeed: string;
  modeCode: string;
  nickname: string;
  parentSn: string;
  payloadGimbalPitch: string;
  payloadGimbalRoll: string;
  payloadGimbalYaw: string;
  payloadPayloadIndex: string;
  positionStateGpsNumber: string;
  positionStateRtkNumber: string;
  recordId: string;
  storageTotal: string;
  storageUsed: string;
  totalFlightDistance: number;
  trackId: number;
  verticalSpeed: string;
  windDirection: string;
  windSpeed: string;
  time: Cesium.JulianDate
}
const targetPointVlaue = ref({});

const videoProgress = ref(0); // 视频进度（0-1）

const pause = (status: boolean, time: number) => {
  viewer.clock.shouldAnimate = status;
  viewer.clock.multiplier = time;
  console.log(66666, status, time)
}
// 更新 updateProgress 为时间驱动
function updateProgress(progress: number) {
  videoProgress.value = progress;
  progressC.value = progress
  currentTimeC.value = progress * duration.value
  if (!trackStartTime || !trackEndTime || trackDataC.value.length === 0) return;

  const totalSeconds = Cesium.JulianDate.secondsDifference(trackEndTime, trackStartTime);
  const currentSeconds = totalSeconds * progress;
  const currentTime = Cesium.JulianDate.addSeconds(trackStartTime, currentSeconds, new Cesium.JulianDate());
  // 使用优化后的查找方法
  const targetPoint = findPointByTime(currentTime, trackDataC.value);
  // console.log('数据点', trackDataC.value)
  if (targetPoint) {
    targetPointVlaue.value = { ...targetPoint }; // 确保新引用
  }
}
function updateProgressTrack(progress: number) {
  videoProgress.value = progress;
  progressC.value = progress
  currentTimeC.value = progress * duration.value
  if (!trackStartTime || !trackEndTime || trackDataC.value.length === 0) return;

  const totalSeconds = Cesium.JulianDate.secondsDifference(trackEndTime, trackStartTime);
  const currentSeconds = totalSeconds * progress;
  const currentTime = Cesium.JulianDate.addSeconds(trackStartTime, currentSeconds, new Cesium.JulianDate());
  viewer.clock.currentTime = currentTime

  // 使用优化后的查找方法
  const targetPoint = findPointByTime(currentTime, trackDataC.value);

  if (targetPoint) {
    targetPointVlaue.value = { ...targetPoint }; // 确保新引用
  }
}
// 创建动态时间轨迹
function createTimeDynamicTrack(sortedPoints: any[]) {
  console.log('shuju', sortedPoints[0].date, sortedPoints[sortedPoints.length - 1].date)

  // 1. 存储时间范围
  trackStartTime = sortedPoints[0].time;
  trackEndTime = sortedPoints[sortedPoints.length - 1].time;

  // 2. 创建静态轨迹线（灰白色）
  const staticTrack = viewer.entities.add({
    name: 'static-track',
    polyline: {
      positions: sortedPoints.map(p =>
        Cesium.Cartesian3.fromDegrees(p.longitude, p.latitude, p.altitude || 0)
      ),
      width: 5,
      clampToGround: true,
      material: Cesium.Color.LIGHTGRAY.withAlpha(0.7)
    }
  });
  allEntities.push(staticTrack);

  // 3. 创建动态轨迹线（黄色）
  const dynamicTrackPositions = new Cesium.CallbackProperty(() => {
    const currentTime = viewer.clock.currentTime;
    const passedPoints = sortedPoints.filter(p =>
      Cesium.JulianDate.compare(p.time, currentTime) <= 1
    );

    return passedPoints.map(p =>
      Cesium.Cartesian3.fromDegrees(p.longitude, p.latitude, p.altitude || 0)
    );
  }, false);

  const dynamicTrack = viewer.entities.add({
    name: 'dynamic-track',
    polyline: {
      positions: dynamicTrackPositions,
      width: 5,
      clampToGround: true,
      material: Cesium.Color.YELLOW
    }
  });
  allEntities.push(dynamicTrack);

  // 4. 创建无人机实体（使用SampledPositionProperty）
  if (!droneEntity) {
    // 创建位置属性
    const positionProperty = new Cesium.SampledPositionProperty();

    // 添加所有采样点
    sortedPoints.forEach(point => {
      positionProperty.addSample(
        point.time,
        Cesium.Cartesian3.fromDegrees(
          point.longitude,
          point.latitude,
          point.altitude || 0
        )
      );
    });
    // 2. 创建方向属性（关键修正点）
    const orientationProperty = new Cesium.VelocityOrientationProperty(positionProperty);
    droneEntity = viewer.entities.add({
      name: 'drone-entity',
      position: positionProperty,  // 使用SampledPositionProperty
      billboard: {
        image: drone_marker,
        scale: 0.16,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    });
    allEntities.push(droneEntity);
  }


  if (trackStartTime && trackEndTime) {
    // 6. 设置时钟范围
    viewer.clock.startTime = trackStartTime.clone();
    viewer.clock.stopTime = trackEndTime.clone();
    viewer.clock.currentTime = trackStartTime.clone();
  }
}
// 使用Cesium自带箭头效果的正确方式
// 安全计算向量方向（修复归一化错误）
function safeGetDirection(start: Cesium.Cartesian3, end: Cesium.Cartesian3): Cesium.Cartesian3 | null {
  const direction = Cesium.Cartesian3.subtract(end, start, new Cesium.Cartesian3());
  const distance = Cesium.Cartesian3.magnitude(direction);

  // 过滤距离过近的点（小于1米视为重复点）
  if (distance < 1.0) {
    return null;
  }

  try {
    return Cesium.Cartesian3.normalize(direction, direction);
  } catch (e) {
    console.warn('Normalization failed:', start, end);
    return null;
  }
}

// 增强版拐点检测（带数据校验）
function findKeyTurningPoints(positions: Cesium.Cartesian3[], minAngle = 15): number[] {
  const keyPoints: number[] = [];

  // 数据校验
  if (!positions || positions.length < 3) return keyPoints;

  for (let i = 1; i < positions.length - 1; i++) {
    const prev = positions[i - 1];
    const curr = positions[i];
    const next = positions[i + 1];

    // 跳过无效点
    if (!isValidPosition(prev) || !isValidPosition(curr) || !isValidPosition(next)) {
      continue;
    }

    // 计算两个方向向量
    const v1 = safeGetDirection(prev, curr);
    const v2 = safeGetDirection(curr, next);

    if (!v1 || !v2) continue;

    // 计算夹角
    const angle = Cesium.Cartesian3.angleBetween(v1, v2) * (180 / Math.PI);
    if (angle > minAngle) {
      keyPoints.push(i);
    }
  }
  return keyPoints;
}

// 坐标有效性检查
function isValidPosition(pos: Cesium.Cartesian3): boolean {
  return (
    pos &&
    !isNaN(pos.x) &&
    !isNaN(pos.y) &&
    !isNaN(pos.z) &&
    isFinite(pos.x) &&
    isFinite(pos.y) &&
    isFinite(pos.z)
  );
}

// 修复后的箭头添加函数
function addCesiumDirectionArrows(positions: Cesium.Cartesian3[]) {
  if (!positions || positions.length < 2) return;

  // 1. 预处理：过滤无效点
  const validPositions = positions.filter(p => isValidPosition(p));
  if (validPositions.length < 2) return;

  // 2. 创建箭头材质
  const arrowMaterial = new Cesium.PolylineArrowMaterialProperty(
    Cesium.Color.YELLOW.withAlpha(0.9)
  );

  // 3. 智能分段添加箭头
  const segmentSize = Math.max(Math.floor(validPositions.length / 15), 1);
  const turningPoints = findKeyTurningPoints(validPositions, 15);

  for (let i = 0; i < validPositions.length - 1; i += segmentSize) {
    const start = validPositions[i];
    const end = validPositions[i + 1];

    // 确保线段有效
    if (!isValidPosition(start) || !isValidPosition(end)) continue;
    if (Cesium.Cartesian3.distance(start, end) < 1.0) continue;

    // 特殊标记拐点
    const isTurningPoint = turningPoints.includes(i);

    viewer.entities.add({
      polyline: {
        positions: [start, end],
        width: isTurningPoint ? 12 : 8,
        material: isTurningPoint
          ? new Cesium.PolylineGlowMaterialProperty({
            glowPower: 0.7,
            color: Cesium.Color.RED
          })
          : arrowMaterial,
        clampToGround: true,
        zIndex: 2
      }
    });
  }
}
// 清除旧的飞行轨迹
function clearOldTrack() {
  if (polyline) {
    viewer.entities.remove(polyline); // 移除旧的飞行轨迹
    const index = allEntities.indexOf(polyline);
    if (index > -1) {
      allEntities.splice(index, 1);
    }
    polyline = null; // 重置 polyline 变量
  }
  trackPoints.value = []; // 清空轨迹点数组
}

// 生成机场范围椭圆的轮廓点
function computeEllipsePositions(
  center: { longitude: number; latitude: number },
  semiMajorAxis: number,
  semiMinorAxis: number,
  granularity: number
): Cesium.Cartesian3[] {
  const positions: Cesium.Cartesian3[] = [];
  for (let i = 0; i < 360; i += granularity) {
    const radians = Cesium.Math.toRadians(i);
    const x = semiMajorAxis * Math.cos(radians);
    const y = semiMinorAxis * Math.sin(radians);
    // 将米转换为经纬度
    const position = Cesium.Cartesian3.fromDegrees(center.longitude + x / 111319.9, center.latitude + y / 111319.9);
    positions.push(position);
  }
  // 闭合椭圆，使首尾相连
  positions.push(positions[0]);
  return positions;
}

// 绘制机场点位及范围
function drawAirportPoint(points: Api.Airport.MachineNestInfo[]) {
  points.forEach((point: Api.Airport.MachineNestInfo) => {
    const { longitude, latitude, distance, deviceName, landPointLongitude, landPointLatitude, deviceSn } = point;
    const center = { longitude, latitude }; // 明确指定中心点类型

    // 生成椭圆边界点
    const positions = computeEllipsePositions(center, distance, distance, 5);

    // 绘制机场范围（用Polygon模拟椭圆）
    const airportArea = viewer.entities.add({
      id: `airport_area_${deviceSn}`, // 使用机场编号作为id的一部分
      polygon: {
        hierarchy: positions,  // 多边形的顶点
        material: Cesium.Color.fromCssColorString('#1177fb').withAlpha(0.15), // 设置填充颜色
        perPositionHeight: false,  // 使多边形所有点贴地
      },
      description: 'airpoint',
    });
    allEntities.push(airportArea);

    // 绘制椭圆边框
    const airportBorder = viewer.entities.add({
      id: `airport_border_${deviceSn}`,
      polyline: {
        positions: positions,  // 线的顶点位置，与多边形顶点一致
        width: 5,  // 边框宽度
        material: Cesium.Color.fromCssColorString('#1177fb'),  // 边框颜色
        clampToGround: true,  // 使边框贴地
      },
      description: 'airpointborder',
    });
    allEntities.push(airportBorder);

    // 绘制机场图标
    const airportIcon = viewer.entities.add({
      id: `airport_icon_${deviceSn}`,
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      billboard: {
        image: marker,
        scale: 0.13
      },
      description: 'airpointicon',
    });
    allEntities.push(airportIcon);

    // 备降点图标
    if (landPointLatitude && landPointLongitude) {
      // 绘制机场备降点图标
      const airportAlternate = viewer.entities.add({
        id: `airport_alternate_${deviceSn}`,
        position: Cesium.Cartesian3.fromDegrees(landPointLongitude, landPointLatitude),
        billboard: {
          image: alternate,
          scale: 0.13
        },
        description: 'airpointbackupicon'
      });
      allEntities.push(airportAlternate);

      // 绘制机场备降点标签
      const airportAlternateLabel = viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(landPointLongitude, landPointLatitude),
        label: {
          text: `${deviceName}-备降点`,
          font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
          fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
          outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 8.0,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(0, -50), // 向上偏移50像素
        },
        description: 'airpointtext',
      });
      allEntities.push(airportAlternateLabel);
    }

    // 绘制机场标签
    const airportLabel = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      label: {
        text: deviceName,
        font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
        fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
        outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 8.0,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        pixelOffset: new Cesium.Cartesian2(0, -50), // 向上偏移50像素
      },
      description: 'airpointtext',
    });
    allEntities.push(airportLabel);
  });
}

// 获取已激活的机场列表
async function getAirportDeviceList() {
  const { data } = await fetchAirportDeviceList();
  // 过滤掉不在线的机场
  const activeAirports = data.rows.filter((item: Api.Airport.MachineNestInfo) => item.status);
  Object.assign(airportDeviceList, activeAirports);
  // airportDeviceList.forEach((item) => {
  //     if (item.deviceSn == dockSn.value) {

  //     }
  // })
  // 初始化地图
  drawAirportPoint(activeAirports);
}

// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
  try {
    const response = await fetch('/defaultNoFlyZone.json');
    const data = await response.json();
    // 绘制
    addPolygons(data.features);
  } catch (error) {
    console.error('Error loading JSON data:', error);
  }
};

// 绘制禁飞区
function addPolygons(features: any[]) {
  features.forEach(feature => {
    const polygon = feature.geometry.coordinates[0];
    const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    const noFlyZone = viewer.entities.add({
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString('red').withAlpha(0.35),
      }
    });
    allEntities.push(noFlyZone);
  });
};

// 获取无人机设备状态码列表
async function getDroneStatusList() {
  if (deviceStore.droneStatusList.length <= 0) {
    const { data, error } = await fetchDroneStatusList();
    if (error) {
      // popDialog.show = false;
      return
    };
    deviceStore.setDroneStatusList(data);
  }
}

watch(() => flyRecordRef.value?.droneVideoRef?.videoRef, (val) => {
  if (val) {
    togglePlay()
    dragstart.value = false
  }
})
onMounted(() => {

});

onBeforeMount(() => {
  // 初始化地图
  // 获取已激活的机场列表
  getDroneStatusList();
  if (route.query.recordId) { // 获取设备编码和下标
    recordId.value = route.query.recordId;
    getVideo(recordId.value).then((res) => {
      if (res.data) {
        taskName.value = res.data.flightJobName
        trackUrl.value = res.data.trackPath
        videoUrl.value = res.data.videoRecord
        dockSn.value = res.data.dockSn
        console.log('轨迹数据', trackUrl.value)
        longitudeD.value = res.data.dockLongitude
        latitudeD.value = res.data.dockLatitude
        initCesium();
        getAirportDeviceList();
        loadTrackData(trackUrl.value, 0); // 10秒采样间隔
      }
    })

  }
});

onBeforeUnmount(() => {
  // // 移除所有添加的实体
  allEntities.forEach(entity => {
    viewer.entities.remove(entity);
  });
  allEntities.length = 0;
})

</script>

<style scoped>
#container {
  padding: 0;
}

#status-bar {
  bottom: 65px;
  left: 0;
}

.n-modal .n-card,
.n-drawer .n-card {
  background-color: var(--n-border-color);
}

/* 调整 Toastr 样式以适应你的应用 */
.toast {
  font-family: '微软雅黑', Arial, sans-serif;
  font-size: 14px;
  opacity: 0 !important;
}

.toast-info {
  background-color: #1890ff;
}

.toast-warning {
  background-color: #faad14;
}

.toast-error {
  background-color: #ff4d4f;
}

/* 调整位置 */
.toast-top-right {
  top: 30px !important;
  right: 12px !important;
}

.custom-video-controls {
  position: absolute;
  bottom: 240px;
  /* flyRecord 高度是 h-60 (240px)，所以设置为 240px 避免重叠 */
  width: 100%;
  /* 位于 flyRecord 上方 */
  right: 0;
  background: rgba(0, 0, 0, 1);
  padding: 5px;
  display: flex;
  align-items: center;
  gap: 15px;
  z-index: 1;
}

.play-pause-btn {
  background: transparent;
  /* 完全透明背景 */
  border: none;
  /* 移除默认边框 */
  padding: 0;
  /* 移除内边距 */
  cursor: pointer;
  /* 保持手型指针 */
  outline: none;
  /* 移除点击时的轮廓线 */
}

.play-pause-btn:hover {
  opacity: 0.8;
  /* 悬停时轻微透明效果 */
}



.custom-video-controls select {
  padding: 3px;
  background: #333;
  color: white;
  border: none;
  border-radius: 4px;
}
</style>