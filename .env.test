# backend service base url, test environment
# VITE_SERVICE_BASE_URL=http://*************:8633 #tong
# VITE_SERVICE_BASE_URL=http://*************:8633 #gao
# VITE_SERVICE_BASE_URL=http://*************:8633 #wang
# VITE_SERVICE_BASE_URL=http://**************:8633 #tian
# VITE_SERVICE_BASE_URL=http://sahy.cloud:8040/prod-api/ #测试环境129
VITE_SERVICE_BASE_URL=https://fly.sahy.cloud/prod-api/ #测试环境183
# NODE_ENV=test
# other backend service base url, test environment
VITE_OTHER_SERVICE_BASE_URL= `{
  "demo": "http://localhost:9528",
  "pserver": "http://**************:5000/"
}`
# "pserver": "http://test.sahy.cloud:8050/"
# "pserver": "http://*************:5000/"