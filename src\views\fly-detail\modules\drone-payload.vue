<template>
  <n-config-provider :theme="darkTheme">
    <n-flex justify="space-between" align="center" :vertical="true" :size="[5, 12]"
      class="fixed right-0 top-64 -translate-y-1/2 z-50 px-2 py-2 bg-dark-7 text-light mt--4px h-max">

      <!-- 录像 -->
      <div>
        <n-badge v-if="recordStatus" dot>
          <n-button type="primary" :loading="recordStatusLoading" :disabled="cameraTypeAndRecordLoading">
            <template #icon>
              <n-icon @click="handleRecordClick">
                <Videocam />
              </n-icon>
            </template>
          </n-button>
        </n-badge>

        <n-button v-else type="primary" :loading="recordStatusLoading" :disabled="cameraTypeAndRecordLoading">
          <template #icon>
            <n-icon @click="handleRecordClick">
              <VideocamOff />
            </n-icon>
          </template>
        </n-button>
      </div>

      <!-- 摄像头类型 -->
      <div>
        <n-popselect v-model:value="cameraTypeValue" :options="cameraTypeOptions" @update:value="handleCameraTypeChange"
          trigger="click" placement="left" size="medium">
          <n-button>
            {{ cameraTypeOptions.find(opt => opt.value === cameraTypeValue)?.label }}
          </n-button>
        </n-popselect>
        
        <!-- 测温开启/关闭 -->
        <n-space v-show="cameraTypeValue == 'ir'" class="mt-5px" >
          <n-switch v-model:value="tempSwitchValue" @update:value="handleTempSwitchChange" :round="false" size="small">
            <template #checked>测温</template>
            <template #unchecked>测温</template>
          </n-switch>
        </n-space>
        <n-space v-show="cameraTypeValue == 'ir'" class="mt-5px" >
          <n-switch v-model:value="splitSwitchValue" @update:value="handleSplitSwitchChange" :round="false" size="small">
            <template #checked>分屏</template>
            <template #unchecked>分屏</template>
          </n-switch>
        </n-space>

      </div>
      <!-- 变焦 -->
      <n-popselect v-model:value="zoomTypeValue" :options="zoomTypeOptionsFiltered" :disabled="isZoomTypeDisabled"
        trigger="click" placement="left" size="medium" @update:value="handleZoomTypeChange">
        <n-button :disabled="cameraTypeValue !== 'zoom'" class="w-4em">
          {{ zoomTypeOptions.find(opt => opt.value === zoomTypeValue)?.label }}
        </n-button>
      </n-popselect>

      <!-- 拍照 -->
      <n-button @click="handlePhotoClick" :disabled="cameraDisabled" :loading="cameraTypeLoading" type="warning">
        <template #icon>
          <n-icon>
            <Camera />
          </n-icon>
        </template>
      </n-button>

      <!-- 摄像头方向控制按钮组 -->
      <div class="relative w-max h-max flex flex-col items-center gap-1">
        <!-- 上箭头 -->
        <div class="w-6 h-6 bg-dark-2 rounded flex items-center justify-center
            cursor-pointer transition-all duration-100 hover:bg-dark-4 active:bg-dark-5"
          @click="handleDirectionClick('ARROWUP')">
          <n-icon>
            <ChevronUp />
          </n-icon>
        </div>

        <!-- 左右箭头容器 -->
        <div class="flex gap-1">
          <!-- 左箭头 -->
          <div class="w-6 h-6 bg-dark-2 rounded flex items-center justify-center
              cursor-pointer transition-all duration-100 hover:bg-dark-4 active:bg-dark-5"
            @click="handleDirectionClick('ARROWLEFT')">
            <n-icon>
              <ChevronBack />
            </n-icon>
          </div>

          <!-- 右箭头 -->
          <div class="w-6 h-6 bg-dark-2 rounded flex items-center justify-center
              cursor-pointer transition-all duration-100 hover:bg-dark-4 active:bg-dark-5"
            @click="handleDirectionClick('ARROWRIGHT')">
            <n-icon>
              <ChevronForward />
            </n-icon>
          </div>
        </div>

        <!-- 下箭头 -->
        <div class="w-6 h-6 bg-dark-2 rounded flex items-center justify-center
            cursor-pointer transition-all duration-100 hover:bg-dark-4 active:bg-dark-5"
          @click="handleDirectionClick('ARROWDOWN')">
          <n-icon>
            <ChevronDown />
          </n-icon>
        </div>
      </div>

      <!-- 恢复 -->
      <n-dropdown trigger="click" placement="left" :options="recoverTypeOptions" @select="handleRecoverSelect">
        <n-icon size="30" class="cursor-pointer ">
          <ReloadCircle />
        </n-icon>
      </n-dropdown>

    </n-flex>
  </n-config-provider>
</template>

<script setup lang="ts">
import { PropType, Ref, computed, inject, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { Camera, VideocamOff, Videocam, ChevronUp, ChevronDown, ChevronBack, ChevronForward, ReloadCircle } from '@vicons/ionicons5'
import { useAirportStore } from '@/store/modules/airport';
import { useRoute } from 'vue-router';
import { darkTheme, useMessage } from 'naive-ui';
import { useTaskStore } from '@/store/modules/task';
import { fetchPayloadCameraControl, fetchPayloadCameraMode, fetchPayloadCameraPhotoTake, fetchPayloadCameraRecordStart, fetchPayloadCameraRecordStop, fetchPayloadCameraSwitch, fetchPayloadCameraZoom, fetchPayloadReset, fetchPayloadCameraSplitScreen, fetchPayloadCameraIRTemp } from '@/service/api';
import { useDeviceStore } from '@/store/modules/device';

const taskStore = useTaskStore();
const route = useRoute();
const message = useMessage();
// 通过 inject 获取 provide 的 socket 对象
const socket = inject<{ chatMessage: Ref<string>, sendMessage: (data: any) => boolean }>('useSocket');
const deviceStore = useDeviceStore();

defineOptions({ name: 'DronePayload' });
// const emits = defineEmits(['focus-airport']);

// 定义组件接受的属性
// const props = defineProps<{
// }>();

// 定义摄像头移动速度常量
const CAMERA_SPEED = {
  FORWARD: 7.215,
  BACKWARD: -7.215
};

const flyInfoSN = ref();
const flyInfoDroneSN = ref();
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});

const cameraTypeLoading = ref(false);
const cameraTypeValue = ref('wide');
const cameraTypeOptions = ref([
  { label: '广角', value: 'wide' },
  { label: '变焦', value: 'zoom' },
  { label: '红外', value: 'ir' },
]);

// 红外可变焦2-20，可见光可变焦2-200
const zoomTypeValue = ref(1);
const zoomTypeAllOptions = [
  { label: '2x', value: 2 },
  { label: '4x', value: 4 },
  { label: '5x', value: 5 },
  { label: '7x', value: 7 },
  { label: '10x', value: 10 },
  { label: '20x', value: 20 },
  { label: '50x', value: 50 },
  { label: '80x', value: 80 },
  // { label: '100x', value: 100 },
  // { label: '150x', value: 150 },
  // { label: '200x', value: 200 },
];

const zoomTypeOptions = ref([...zoomTypeAllOptions]);

// 计算变焦倍率
const zoomTypeOptionsFiltered = computed(() => {
  if (cameraTypeValue.value === 'zoom') {
    return zoomTypeAllOptions;
  } else if (cameraTypeValue.value === 'ir') {
    return zoomTypeAllOptions.filter(opt => [2, 4, 7, 10, 20].includes(opt.value));
  } else {
    return [];
  }
});

// 是否禁用变焦倍率
const isZoomTypeDisabled = computed(() => {
  return cameraTypeValue.value !== 'zoom' && cameraTypeValue.value !== 'ir';
});

// 测温开关（红外模式有效）
const tempSwitchValue = ref(true);
// 分屏开关（红外模式有效）
const splitSwitchValue = ref(false);

const recoverTypeValue = ref(null);
const recoverTypeOptions = ref([
  { label: '云台回中', key: '0' },
  { label: '云台向下', key: '1' },
  { label: '偏航回中', key: '2' },
  { label: '俯仰向下', key: '3' },
  { label: '俯仰向下30', key: '-30' },
  { label: '俯仰向下45', key: '-45' },
  { label: '俯仰向下60', key: '-60' },
]);

const cameraTypeAndRecordLoading = ref(false); // 摄像头类型和录像按钮是否正在加载: 2s内只能点击一次
const cameraDisabled = ref(false); // 拍照是否不可选中
const recordStatusLoading = ref(false);
const recordStatus = ref(false); // 是否开启录像 true-开启 false-关闭

// 处理恢复点击
const handleRecoverSelect = async (key: string) => {
  // console.log('key: ', key);
  // console.log('无人机value: ', airportDroneItem.value.host?.payloadGimbalPitch);
  if (Number(key) < 0) {
    let json = { pitchSpeed: 0, yawSpeed: 0 };
    const offsetPitch = Number(key) - Math.round(Number(airportDroneItem.value.host?.payloadGimbalPitch));
    json.pitchSpeed = offsetPitch * 2.41395;
    return await fetchPayloadCameraControl(flyInfoSN.value, json);
  }
  await fetchPayloadReset(flyInfoSN.value, { resetMode: key });
};

// 处理云台方向点击
const handleDirectionClick = async (direction: string) => {
  console.log('direction: ', direction);
  let json = { pitchSpeed: 0, yawSpeed: 0 };
  switch (direction) {
    case 'ARROWUP':
      json.pitchSpeed = CAMERA_SPEED.FORWARD;
      break;
    case 'ARROWLEFT':
      json.yawSpeed = CAMERA_SPEED.BACKWARD;
      break;
    case 'ARROWRIGHT':
      json.yawSpeed = CAMERA_SPEED.FORWARD;
      break;
    case 'ARROWDOWN':
      json.pitchSpeed = CAMERA_SPEED.BACKWARD;
      break;
  }
  const { error } = await fetchPayloadCameraControl(flyInfoSN.value, json);
};

// 处理拍照点击
const handlePhotoClick = async () => {
  cameraTypeLoading.value = true;
  cameraTypeAndRecordLoading.value = true;
  await fetchPayloadCameraSwitch(flyInfoSN.value, 0);

  setTimeout(async () => {
    const { error } = await fetchPayloadCameraPhotoTake(flyInfoSN.value);
    cameraTypeLoading.value = false;
    console.log('fetchPayloadCameraPhotoTake: ', cameraTypeAndRecordLoading.value);
    setTimeout(() => {
      cameraTypeAndRecordLoading.value = false;
    }, 2000);
  }, 1000);
};

// 处理录像点击
const handleRecordClick = async () => {
  recordStatusLoading.value = true;
  cameraDisabled.value = true;
  if (!recordStatus.value) { // 开始录像
    const { error: switchError } = await fetchPayloadCameraSwitch(flyInfoSN.value, 1);
    console.log('switchError: ', switchError);
    if (switchError) { // 切换摄像头失败，结束加载，恢复状态
      recordStatusLoading.value = false;
      cameraDisabled.value = false;
    } else {
      setTimeout(async () => {
        const { error: recordError } = await fetchPayloadCameraRecordStart(flyInfoSN.value);
        console.log('recordError: ', recordError);
        if (recordError) return;
        recordStatusLoading.value = false;
        recordStatus.value = true;
      }, 2000);
    }
  } else { // 停止录像
    const { error: stopError } = await fetchPayloadCameraRecordStop(flyInfoSN.value);
    console.log('stopError: ', stopError);
    if (stopError) {
      recordStatusLoading.value = false;
      recordStatus.value = true;
    } else {
      setTimeout(() => {
        recordStatusLoading.value = false;
        recordStatus.value = false;
        cameraDisabled.value = false;
      }, 3000);
    }
  }
};

// 处理相机类型变化-广角/变焦/红外
const handleCameraTypeChange = async (label: string) => {
  const oldLabel = label;
  switch (label) {
    case 'wide': // 广角
      zoomTypeValue.value = 1;
      break;
    case 'zoom': // 变焦
      zoomTypeValue.value = 2;
      break;
    case 'ir': // 红外
      zoomTypeValue.value = 2;
      break;
  }
  const { error } = await fetchPayloadCameraMode(flyInfoSN.value, label);
  if (error) {
    cameraTypeValue.value = oldLabel;
  } else if (!error && label === 'ir') {
    handleTempSwitchChange(true)
  }
};

// 开启/关闭测温功能
const handleTempSwitchChange = async (flag: boolean) => { 
  // console.log('flag: ', flag);
  // flag: true 开启区域测温 false 关闭区域测温
  if (flag) {
    await fetchPayloadCameraIRTemp(flyInfoSN.value, 2);
  } else {
    await fetchPayloadCameraIRTemp(flyInfoSN.value, 0);
  }
  
}

// 开启/关闭分屏功能
const handleSplitSwitchChange = async (flag: boolean) => {
  await fetchPayloadCameraSplitScreen(flyInfoSN.value, flag);
}

// 处理变焦变化
const handleZoomTypeChange = async (label: number) => {
  const oldLabel = label;
  const { error } = await fetchPayloadCameraZoom(flyInfoSN.value, { cameraType: cameraTypeValue.value, zoomFactor: label });
  if (error) {
    zoomTypeValue.value = oldLabel;
  }
};

// 同步socket消息里的无人机负载信息
const watchDronePayloadInfo = (data: any) => {
  const { camerasZoomFactor, cameraType, irMeteringMode} = data.host;
  // console.log('watchDronePayloadInfo data: ', Math.round(Number(camerasZoomFactor)));
  zoomTypeValue.value = Math.round(Number(camerasZoomFactor));
  cameraTypeValue.value = cameraType;
  if (Boolean(irMeteringMode)) { // 如果开启了红外测温
    
  }
};


// 处理socket消息
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  if (biz_code === 'device_osd' && flyInfoDroneSN.value === sn) { // 本次任务无人机消息
    airportDroneItem.value = data;
    if (data) { watchDronePayloadInfo(data); } // 同步无人机负载信息
  } else if (biz_code === 'dock_osd' && flyInfoSN.value === sn) { // 本次任务机场消息
    // airportDockItem.value = data;
  } else if (biz_code === 'device_hms') { // 设备告警
  } else if (biz_code === 'device_status' && data.sn === flyInfoDroneSN.value && data.host.status) { // 无人机上线
  }
}
// 监听Socket消息
watch(() => socket?.chatMessage?.value, (msg) => {
  onProcessMessage(msg); // 处理接收到的消息
});

// 监听 deviceStore 的变化，用于初始化，只监听一次
const stopWatching = watch(() => deviceStore.dockInfo, (newValue, oldValue) => {
  console.log('deviceStore 变化:', newValue);
  if (newValue && newValue.droneParam) {
    const { camerasZoomFactor, camerasRecordingState, cameraType } = newValue.droneParam;
    if (camerasRecordingState === '1') { // 录像模式
      recordStatus.value = true;
    }
    cameraTypeValue.value = cameraType;
    zoomTypeValue.value = Math.round(Number(camerasZoomFactor));
  }

  // 取消监听
  stopWatching();
});

// 在组件挂载时添加事件监听
onMounted(() => {
  if (route.query.sn) {
    flyInfoSN.value = route.query.sn;
    flyInfoDroneSN.value = route.query.dsn;
  }
});

onUnmounted(() => {
});

</script>

<style scoped>
/* :deep(.n-slider-rail) {
  width: 5px;
}
:deep(.n-slider-rail-active) {
  width: max-content
} */
</style>
