<template>
    <div :id="chartId" class="map-container"></div>
  </template>
  
  <script lang="ts" setup>
  import { onMounted, defineProps } from 'vue';
  import * as echarts from 'echarts';
  import 'echarts/map/js/china.js';
  
  const props = defineProps<{
    chartId: string;
  }>();
  
  onMounted(() => {
    const mapChart = echarts.init(document.getElementById(props.chartId) as HTMLElement);
    const mapOption = {
      tooltip: {
        trigger: 'item'
      },
      visualMap: {
        min: 0,
        max: 100,
        left: 'left',
        top: 'bottom',
        text: ['高', '低'],
        calculable: true,
        inRange: {
          color: ['#e0ffff', '#006edd']
        }
      },
      series: [
        {
          name: '数据分布',
          type: 'map',
          map: 'china',
          roam: false,
          label: {
            show: true,
            color: '#e6f1ff'
          },
          data: [
            { name: '北京', value: 80 },
            { name: '上海', value: 90 },
            { name: '广东', value: 70 }
          ]
        }
      ]
    };
    mapChart.setOption(mapOption);
  });
  </script>
  
  <style scoped>
  .map-container {
    width: 100%;
    height: 900px;
  }
  </style>    