import * as Cesium from 'cesium';
import { MouseTooltip } from '@cesium-extends/tooltip';
import { ref } from 'vue';
interface DrawOptions {
  pointStyle?: {
    pixelSize?: number;
    color?: Cesium.Color;
    outlineColor?: Cesium.Color;
    outlineWidth?: number;
  };
  lineStyle?: {
    width?: number;
    material?: Cesium.Material;
  };
}
const billImage = 'data:image/png;base64,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'
class DrawNoFlyArea {
  private _viewer: Cesium.Viewer;
  private _drawLayer: Cesium.CustomDataSource;
  private _positions: Cesium.Cartesian3[];
  private _eventHandler: Cesium.ScreenSpaceEventHandler | null;
  private _polygonEntity: Cesium.Entity | null;
  private _lineEntity: Cesium.Entity | null;
  public allPositions = ref<{ lng: number; lat: number}[] | null>(null);
  public centerPoint = ref<{ lng: number; lat: number } | null>(null);
  constructor(viewer: Cesium.Viewer) {
    this._viewer = viewer;
    this._drawLayer = new Cesium.CustomDataSource('noFlyAreaLayer');
    this._viewer.dataSources.add(this._drawLayer);
    this._positions = [];
    this._eventHandler = null;
    this.allPositions.value = null;
    this.centerPoint.value = null;
    this._polygonEntity = null;
    this._lineEntity = null;
  }

  startDrawing(options: DrawOptions = {}): void {
    this._eventHandler = new Cesium.ScreenSpaceEventHandler(this._viewer.scene.canvas);

    // 左键添加点
    this._eventHandler.setInputAction((movement: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
      const cartesian = this._viewer.scene.pickPosition(movement.position);
      if (cartesian) {
        this._positions.push(cartesian);
        this._addPoint(cartesian, options.pointStyle);
        this._updateLine(options.lineStyle);
        this._updatePolygon();
        this.returnPositionsAndCenter(); // 返回点坐标和中心点
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

    // 右键删除点
    this._eventHandler.setInputAction((movement: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
      const picked = this._viewer.scene.pick(movement.position);
      if (Cesium.defined(picked) && picked.id && this._drawLayer.entities.contains(picked.id)) {
        const index = this._positions.findIndex((pos) => pos.equals(picked.id.position.getValue(Cesium.JulianDate.now())));
        if (index !== -1) {
          this._positions.splice(index, 1);
          this._drawLayer.entities.remove(picked.id);
          this._updateLine(options.lineStyle);
          this._updatePolygon();
          this._updateLabels(); // 更新点的标签
          this.returnPositionsAndCenter(); // 返回点坐标和中心点
        }
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

    // 鼠标拖拽点
    this._addDragEvent();

    const mouseTooltip = new MouseTooltip(this._viewer, {
      content: "右键删除该点，拖拽移动该点",
      offset: [0, -5]
    });

    this._viewer.screenSpaceEventHandler.setInputAction((movement: any) => {
      const pick = this._viewer.scene.pick(movement.endPosition);
      if (pick && pick.id && pick.id.description?.getValue() === "waypoint") {
        mouseTooltip.showAt(movement.endPosition, "右键删除该点，拖拽移动该点");
      } else {
        mouseTooltip.hide();
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    
  }

  public returnPositionsAndCenter() {
    const allPositions = this._positions.map((cartesian) => {
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      return {
        lng: Cesium.Math.toDegrees(cartographic.longitude),
        lat: Cesium.Math.toDegrees(cartographic.latitude),
      };
    });
   
    const centerPoint = this._computeCenter(allPositions);
    this.allPositions.value = allPositions;
    this.centerPoint.value = centerPoint;
  }

  private _computeCenter(positions: { lng: number; lat: number }[]): { lng: number; lat: number } | null {
    if (positions.length === 0) return null;

    const sum = positions.reduce(
      (acc, pos) => {
        acc.lng += pos.lng;
        acc.lat += pos.lat;
        return acc;
      },
      { lng: 0, lat: 0 }
    );

    return {
      lng: sum.lng / positions.length,
      lat: sum.lat / positions.length,
    };
  }

  private _addDragEvent(): void {
    const handler = new Cesium.ScreenSpaceEventHandler(this._viewer.scene.canvas);
    let isDragging = false;
    let draggedEntity: Cesium.Entity | null = null;

    // 鼠标左键按下开始拖拽
    handler.setInputAction((movement: { position: Cesium.Cartesian2; }) => {
      const picked = this._viewer.scene.pick(movement.position);
      if (Cesium.defined(picked) && picked.id && this._drawLayer.entities.contains(picked.id)) {
        isDragging = true;
        draggedEntity = picked.id as Cesium.Entity;
        this._viewer.scene.screenSpaceCameraController.enableRotate = false; // 禁用场景旋转
        this._viewer.scene.screenSpaceCameraController.enableTranslate = false; // 禁用地图拖拽
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

    // 鼠标移动更新点位置
    handler.setInputAction((movement: { endPosition: Cesium.Cartesian2; }) => {
      if (isDragging && draggedEntity) {
        const cartesian = this._viewer.scene.pickPosition(movement.endPosition);
        if (cartesian) {
          const index = this._positions.findIndex((pos) => pos.equals(draggedEntity.position!.getValue(Cesium.JulianDate.now())));
          if (index !== -1) {
            this._positions[index] = cartesian;
            draggedEntity.position = new Cesium.ConstantPositionProperty(cartesian);
            this._updateLine();
            this._updatePolygon();
          }
        }
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // 鼠标左键释放结束拖拽
    handler.setInputAction(() => {
      if (isDragging) {
        isDragging = false;
        draggedEntity = null;
        this._viewer.scene.screenSpaceCameraController.enableRotate = true; // 恢复场景旋转
        this._viewer.scene.screenSpaceCameraController.enableTranslate = true; // 恢复地图拖拽
      }
    }, Cesium.ScreenSpaceEventType.LEFT_UP);
  }

  private _updateLabels(): void {
    // 遍历所有点并更新其标签
    this._drawLayer.entities.values.forEach((entity, index) => {
      if (entity.point) {
        entity.label!.text = new Cesium.ConstantProperty((index + 1).toString()); // 更新标签为新的序号
      }
    });
  }

  private _addPoint(position: Cesium.Cartesian3, style?: DrawOptions['pointStyle']): void {
    const pointIndex = this._positions.length; // 点的序号
    const pointEntity = new Cesium.Entity({
      position,
      point: {
        heightReference: Cesium.HeightReference.NONE,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
      label: {
        text: (pointIndex).toString(), // 标签显示点的序号
        font: 'bold 14px sans-serif',
        fillColor: Cesium.Color.WHITE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'),
        showBackground: true,
        backgroundPadding: new Cesium.Cartesian2(2, 2),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
      billboard: {
        image: billImage,
        width: 35,
        height: 35,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
      description: 'waypoint',
    });
    this._drawLayer.entities.add(pointEntity);
  }

  private _updateLine(style?: DrawOptions['lineStyle']): void {
    if (this._lineEntity) {
      this._drawLayer.entities.remove(this._lineEntity);
    }
    if (this._positions.length > 1) {
      const positions = [...this._positions];
      if (this._positions.length > 2) {
        positions.push(this._positions[0]); // 闭合多边形，连接最后一个点和第一个点
      }
      this._lineEntity = new Cesium.Entity({
        polyline: {
          positions: positions,
          width: style?.width || 2,
          material: style?.material instanceof Cesium.Material ? new Cesium.ColorMaterialProperty(style.material.uniforms.color) : style?.material || Cesium.Color.YELLOW,
          clampToGround: true,
        },
      });
      this._drawLayer.entities.add(this._lineEntity);
    }
  }

  private _updatePolygon(): void {
    if (this._polygonEntity) {
      this._drawLayer.entities.remove(this._polygonEntity);
    }
    if (this._positions.length > 2) {
      this._polygonEntity = new Cesium.Entity({
        polygon: {
          hierarchy: new Cesium.PolygonHierarchy(this._positions),
          material: Cesium.Color.RED.withAlpha(0.3), // 修改填充颜色为红色
        },
      });
      this._drawLayer.entities.add(this._polygonEntity);
    }
  }

  stopDrawing(): void {
    if (this._eventHandler) {
      this._eventHandler.destroy();
      this._eventHandler = null;
    }
  }

  clear(): void {
    this._positions = [];
    this._drawLayer.entities.removeAll();
    this._polygonEntity = null;
    this._lineEntity = null;
  }
}

export default DrawNoFlyArea;
