<template>
  <div>
    <NCard :bordered="false" title="照片">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :cols="24" :x-gap="24">
          <NFormItemGi :span="4" label="组织选择：" path="deptId">
            <NTreeSelect clearable v-model:value="formValue.deptId" filterable key-field="id" label-field="label"
              children-field="children" :options="treeData" :default-value="formValue.deptId" />
          </NFormItemGi>
          <NFormItemGi :span="4" label="任务名称：" path="flightJobId">
            <NSelect v-model:value="formValue.flightJobId" filterable placeholder="请选择任务"
              :options="generalFlyRecordOptions" clearable />
          </NFormItemGi>
          <NFormItemGi :span="4" label="航线名称：" path="flightId">
            <NSelect v-model:value="formValue.flightId" :options="airLineList" :loading="loadingA" filterable clearable
              :reset-menu-on-options-change="false" @scroll="handleScroll" />
          </NFormItemGi>
          <NFormItemGi :span="4" label="照片类别：" path="subFileType">
            <NSelect v-model:value="formValue.subFileType" filterable placeholder="请选择任务" :options="photosOptions"
              clearable />
          </NFormItemGi>
          <NFormItemGi :span="6" label="拍摄日期：" path="range">
            <NDatePicker v-model:value="formValue.range" type="daterange" :is-date-disabled="disablePreviousDate" />
          </NFormItemGi>
          <NFormItemGi :span="6">
            <NButton attr-type="button" class="mr2" @click="handleResetClick">重置</NButton>
            <NButton type="primary" attr-type="button" @click="handleQueryClick">查询</NButton>
          </NFormItemGi>
        </NGrid>
      </NForm>
      <NDivider />

      <NSpin :show="loading">
        <NInfiniteScroll style="height: 70vh" :distance="2" @load="handleLoad">
          <NEmpty v-if="!loading && photoList.length === 0" description="暂无照片数据" size="large">
            <template #extra>
              <NButton size="small" @click="handleQueryClick">重新加载</NButton>
            </template>
          </NEmpty>
          <NGrid v-else x-gap="12" y-gap="12" cols="400:2 600:3 900:4 1200:5 1500:6">
            <NGi v-for="(i, index) in photoList" :key="i.id">
              <NCard hoverable @click="handlePhotoClick(index)">
                <template #cover>
                  <NImage :src="i.cover" :preview-disabled="true" class="h-40 w-100%" />
                </template>
                <h4 class="mt-6px" style="font-weight: bold">{{ i.address ? i.address : '---' }}</h4>
                <h6 class="mt-6px" style="font-weight: lighter">{{ '文件大小：' + ((i.size ? i.size : 0) / 1024 /
                  1024).toFixed(2) +
                  'MB | ' + i.payload }}</h6>
                <h6 class="mt-6px" style="font-weight: lighter">{{ '拍摄时间：' + i.createTime }}</h6>
                <n-tooltip trigger="hover">
                  <template #trigger>
                    <h6 class="mt-6px font-light truncate w-full" style="max-width: 100%">
                      {{ '飞行任务：' + i.jobName }}
                    </h6>
                  </template>
                  {{ i.jobName }}
                </n-tooltip>
                <h6 class="mt-6px" style="font-weight: lighter">上传渠道：{{ i.flightType == 0 ? '航线任务' : i.flightType == 1
                  ? '一键起飞任务' :
                  '本地上传' }}</h6>
              </NCard>
            </NGi>
          </NGrid>
          <NSpin v-if="rollLoading" size="small" class="w-full flex justify-center my-2" />
        </NInfiniteScroll>
      </NSpin>
    </NCard>

    <PhotoGalleryPreview v-model:show="previewVisible" :photos="photoList" :item-count="paginationReactive.itemCount"
      :current-index="previewIndex" :query-params="{ flightJobId: formValue.flightJobId, range: formValue.range }" />
  </div>
</template>
<script setup lang="ts">
import { onMounted, reactive, ref } from "vue";
import dayjs from "dayjs";
import { NEmpty, useMessage } from "naive-ui";
import { fetchFilesList, fetchFlyRecordList, fetchAirLineList } from "@/service/api";
import { deptTree } from "@/service/api/user";
interface FlyRecordItem {
  flightJobId: string;
  remark: string;
  flightJobName: string;
}
const photoList = ref<Api.File.PhotoListItem[]>([]);
const treeData = ref([]);
const formValue = reactive<{
  deptId: number | null;
  flightJobId: string | null;
  range: [number, number] | null;
  subFileType: number | null;
  flightId: string | null
}>({
  flightJobId: null,
  deptId: null,
  range: null,
  flightId: null,
  subFileType: null
});

const disablePreviousDate = (ts: number) => {
  return ts > Date.now();
};

const rollLoading = ref(false);

const flyRecordList = ref<FlyRecordItem[]>([]);

const generalFlyRecordOptions = ref<{ label: string; value: string }[]>([]);
const photosOptions = ref<{ label: string; value: string }[]>([{
  label: '全彩',
  value: '0'
}, {
  label: '全景',
  value: '1'
}, {
  label: '红外',
  value: '2'
}]);

const paginationReactive = reactive({
  page: 1,
  pageSize: 20,
  itemCount: 0
});

const loading = ref(true);
const airLineList = ref<Api.AirLine.AirLineItem[]>([]);

// 查询
const handleQueryClick = () => {
  getPhotoList();
};
const loadingA = ref(false)
const pagination = ref({
  pageNum: 1,
  pageSize: 20,
  total: 0,
  keyword: ''
})
const hasMore = ref(true)
const message = useMessage();

// 加载选项数据
const loadOptions = async (isSearch = false) => {
  if (loadingA.value) return;
  loadingA.value = true;
  try {
    if (isSearch) {
      pagination.value.pageNum = 1;
      airLineList.value = [];
      hasMore.value = true;
    }
    if (!hasMore.value) return;

    const response = await fetchAirLineList({
      pageNum: pagination.value.pageNum,
      pageSize: pagination.value.pageSize,
    });
    if (response.data.rows.length === 0) {
      hasMore.value = false;
      if (pagination.value.pageNum === 1) {
        message.info('暂无数据');
      } else {
        message.info('没有更多数据了');
      }
      return;
    }

    // 转换数据格式为 NSelect 需要的格式
    const newOptions = response.data.rows.map((item: Api.AirLine.AirLineItem) => ({
      label: item.flightName, // 假设 flightName 是显示文本
      value: item.flightId,   // 假设 flightId 是值
    }));

    airLineList.value = [...airLineList.value, ...newOptions];
    pagination.value.total = response.data.total;

    if (response.data.rows.length < pagination.value.pageSize) {
      hasMore.value = false;
    } else {
      pagination.value.pageNum++;
    }
  } catch (error) {
    message.error('加载数据失败');
    console.error(error);
  } finally {
    loadingA.value = false;
  }
};

// 滚动事件处理
const handleScroll = (e: any) => {
  const { scrollTop, scrollHeight, clientHeight } = e.target
  // 接近底部时加载更多
  if (scrollHeight - (scrollTop + clientHeight) < 50) {
    loadOptions()
  }
}
// 获取航线列表
const getAirLineList = async () => {
  const { data, error } = await fetchAirLineList({ pageSize: 100000 });
  if (!error) {
    airLineList.value = data.rows;
  }
}
// 重置
const handleResetClick = () => {
  formValue.deptId = null;
  formValue.flightJobId = null;
  formValue.range = null;
  formValue.flightId = null;
  formValue.subFileType = null;
  getPhotoList();
};

const handleLoad = () => {
  const { page, pageSize, itemCount } = paginationReactive;
  if (page * pageSize < itemCount && !rollLoading.value) {
    paginationReactive.page += 1;
    rollPhotoList();
  }
};

async function getPhotoList() {
  loading.value = true;
  const { flightJobId, range, deptId, flightId, subFileType } = formValue;
  const record = flyRecordList.value.find(
    item => flightJobId === String(item.flightJobId)
  );
  const remark = record ? record.remark : null;
  const json: Api.File.FileQuery = {
    recordIds: remark || "",
    deptId: deptId || undefined,
    startDate: range && range[0] ? dayjs(range[0]).format("YYYY-MM-DD") : null,
    endDate:
      range && range[1]
        ? dayjs(range[1] + 24 * 60 * 60 * 1000).format("YYYY-MM-DD")
        : null,
    pageNum: 1,
    pageSize: paginationReactive.pageSize,
    fileType: 0,
    flightId: flightId,
    subFileType: subFileType
  };
  // console.log('查询结果：', json);
  const { data, error } = await fetchFilesList(json);
  if (!error) {
    photoList.value = data.rows;
    paginationReactive.itemCount = data.total;
  }
  loading.value = false;
}

async function rollPhotoList() {
  const { flightJobId, range } = formValue;
  const { page, pageSize, itemCount } = paginationReactive;
  const record = flyRecordList.value.find(
    item => flightJobId === String(item.flightJobId)
  );
  const remark = record ? record.remark : null;
  const json: Api.File.FileQuery = {
    recordIds: remark || "",
    fileType: 0,
    startDate: range && range[0] ? dayjs(range[0]).format("YYYY-MM-DD") : null,
    endDate:
      range && range[1]
        ? dayjs(range[1] + 24 * 60 * 60 * 1000).format("YYYY-MM-DD")
        : null,
    pageNum: page,
    pageSize
  };
  // console.log("滑动查询结果：", json);
  rollLoading.value = true;
  const { data, error } = await fetchFilesList(json);
  if (!error) {
    paginationReactive.itemCount = data.total;
    if ((page - 1) * pageSize < itemCount) {
      photoList.value.push(...data.rows);
    } else {
      paginationReactive.page = 1;
      photoList.value = data.rows;
    }
    rollLoading.value = false;
  }
}

async function getTaskList() {
  const { error, data } = await fetchFlyRecordList();
  if (!error) {
    flyRecordList.value = data.rows;
    generalFlyRecordOptions.value = data.rows.map((v: FlyRecordItem) => ({
      label: v.flightJobName,
      value: String(v.flightJobId)
    }));
  }
}

const previewVisible = ref(false);
const previewIndex = ref(0);

const handlePhotoClick = (index: number) => {
  console.log(index);
  previewIndex.value = index;
  previewVisible.value = true;
};

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
}

onMounted(() => {
  loadOptions();
  getTaskList();
  getDeptTree();
  getPhotoList();
});
</script>


<style scoped>
.n-spin-container {
  min-height: 200px;
}

.n-empty {
  margin: 32px 0;
}
</style>
