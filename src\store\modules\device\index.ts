import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import { useEventListener } from '@vueuse/core';
import { SetupStoreId } from '@/enum';
import { localStg } from '@/utils/storage';

// store/device.ts
export const useDeviceStore = defineStore(SetupStoreId.Device, () => {
  const droneStatusList = ref<Api.Device.DeviceStatusList[]>([]); // 无人机状态码
  const dockInfo = ref<Api.Device.DockInfo>(); // 飞行详情页面机场详细信息

  // 添加无人机设备状态码列表
  function setDroneStatusList(list: Api.Device.DeviceStatusList[]) {
    droneStatusList.value = list;
  }

  // 获取无人机设备状态
  function getDeviceStatusList() {
    return droneStatusList.value;
  }

  // 添加飞行详情页面机场和无人机的详细信息（用于恢复设备状态）
  function setDockInfo(info: Api.Device.DockInfo) {
    // console.log('deviceStore setDockInfo info: ', info);
    dockInfo.value = {
      latitude: parseFloat(Number(info.latitude).toFixed(7)),
      longitude: parseFloat(Number(info.longitude).toFixed(7)),
      altitude: parseFloat(Number(info.altitude).toFixed(7)) || 0,
      droneParam: info.droneParam,
    };
    // 更新响应式数据
    localStorage.setItem('device_dockInfo', JSON.stringify(dockInfo.value));
  }

  // 初始化时读取持久化数据
  const initDockInfo = () => {
    const saved = JSON.parse(<string>localStorage.getItem('device_dockInfo'));
    if (saved) dockInfo.value = saved;
  };

  initDockInfo();
  return {
    droneStatusList,
    setDroneStatusList,
    getDeviceStatusList,
    dockInfo,
    setDockInfo,
  };
});
