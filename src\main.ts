import { createApp } from 'vue';
import './plugins/assets';
import 'cesium/Build/Cesium/Widgets/widgets.css';
import { setupAppVersionNotification, setupDayjs, setupIconifyOffline, setupLoading, setupNProgress } from './plugins';
import { setupStore } from './store';
import { setupRouter } from './router';
import { setupI18n } from './locales';
import App from './App.vue';
// import TCPlayer from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
// import "vis3d/vis3d.css"

// import vis3d from "vis3d/vis3d.js"

// import vis3d_export from "../packages/vis3d/vis3d.js"
// import vis3d from "@/views/map3d/lib/vis3d/vis3d.export.js"


// const app = createApp(App)
// app.config.globalProperties.vis3d = window.vis3d = vis3d // 为了方便调用，同时绑定到window对象上

// app.mount('#app')

import '@/styles/css/fonts.css';

async function setupApp() {
  setupLoading();

  setupNProgress();

  setupIconifyOffline();

  setupDayjs();

  const app = createApp(App);

  setupStore(app);

  await setupRouter(app);

  setupI18n(app);

  setupAppVersionNotification();


  app.mount('#app');

}

setupApp();
