/* eslint-disable */
/* prettier-ignore */
// Generated by elegant-router
// Read more: https://github.com/soybeanjs/elegant-router

import type { RouteComponent } from "vue-router";
import type { LastLevelRouteKey, RouteLayout } from "@elegant-router/types";

import BaseLayout from "@/layouts/base-layout/index.vue";
import BlankLayout from "@/layouts/blank-layout/index.vue";

export const layouts: Record<RouteLayout, RouteComponent | (() => Promise<RouteComponent>)> = {
  base: BaseLayout,
  blank: BlankLayout,
};

export const views: Record<LastLevelRouteKey, RouteComponent | (() => Promise<RouteComponent>)> = {
  403: () => import("@/views/_builtin/403/index.vue"),
  404: () => import("@/views/_builtin/404/index.vue"),
  500: () => import("@/views/_builtin/500/index.vue"),
  "iframe-page": () => import("@/views/_builtin/iframe-page/[url].vue"),
  login: () => import("@/views/_builtin/login/index.vue"),
  achievement_3dmaterial: () => import("@/views/achievement/3dmaterial/index.vue"),
  achievement_3dmaterialdetail: () => import("@/views/achievement/3dmaterialDetail/index.vue"),
  achievement_overview: () => import("@/views/achievement/overview/index.vue"),
  achievement_photo: () => import("@/views/achievement/photo/index.vue"),
  achievement_upload: () => import("@/views/achievement/upload/index.vue"),
  achievement_video: () => import("@/views/achievement/video/index.vue"),
  airfield: () => import("@/views/airfield/index.vue"),
  airspace: () => import("@/views/airspace/index.vue"),
  allairline: () => import("@/views/allairline/index.vue"),
  device_airport: () => import("@/views/device/airport/index.vue"),
  device_emergency: () => import("@/views/device/emergency/index.vue"),
  device_health: () => import("@/views/device/health/index.vue"),
  device_rc: () => import("@/views/device/rc/index.vue"),
  device_work: () => import("@/views/device/work/index.vue"),
  "draw-airline": () => import("@/views/draw-airline/index.vue"),
  flight: () => import("@/views/flight/index.vue"),
  "fly-detail": () => import("@/views/fly-detail/index.vue"),
  flyablearea: () => import("@/views/flyablearea/index.vue"),
  flyhistroy: () => import("@/views/flyhistroy/index.vue"),
  history: () => import("@/views/history/index.vue"),
  home: () => import("@/views/home/<USER>"),
  homeversiontwo: () => import("@/views/homeVersionTwo/index.vue"),
  safety: () => import("@/views/safety/index.vue"),
  senior_config: () => import("@/views/senior/config/index.vue"),
  senior_overview: () => import("@/views/senior/overview/index.vue"),
  system_ai: () => import("@/views/system/ai/index.vue"),
  system_base: () => import("@/views/system/base/index.vue"),
  system_custom: () => import("@/views/system/custom/index.vue"),
  system_organization: () => import("@/views/system/organization/index.vue"),
  system_user: () => import("@/views/system/user/index.vue"),
  task: () => import("@/views/task/index.vue"),
  "user-center": () => import("@/views/user-center/index.vue"),
};
