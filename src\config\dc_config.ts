import DC from '@dvgis/vite-plugin-dc';
// import DC from '@dvgis/vite-plugin-dc';

// import DC from "@/typings/dc.d.ts"
// import type { DC as DCType } from "@/typings/dc.d.ts";


// import '@dvgis/dc-sdk-types';


const key = '3a798e034f12e37aef2884c0505e7971'

const CvaLayerConfig = {
  key,
  style: 'cva'
}

const ImgLayerConfig = {
  key,
  style: 'img'
}

export {
  CvaLayerConfig,
  ImgLayerConfig
}