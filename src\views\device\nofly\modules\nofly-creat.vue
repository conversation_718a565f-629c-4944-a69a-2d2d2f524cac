<template>
  <div class="absolute zindex-1 bg-dark-8 right-340px top-0 h-100% w-300px text-light">
    <n-card class="form-container">
      <n-form-item label="禁飞区名称">
        <n-input v-model:value="formValue.noFlyName" placeholder="请输入标题" />
      </n-form-item>

      <!-- 禁飞区类型选择 -->
      <n-form-item label="禁飞区类型">
        <n-select disabled v-model:value="formValue.noFlyType" :options="noflyTypeOptions" placeholder="请选择禁飞区类型" />
      </n-form-item>

      <!-- 多边形禁飞区表单 -->
      <div v-if="formValue.noFlyType === 'polygon'">
        <n-form-item label="中心点">
          <n-input disabled v-model:value="formValue.centerGeoJson" placeholder="" />
        </n-form-item>
        <n-form-item label="单点设置">
          <n-select v-model:value="currentPointIndex" :options="pointOption" @update:value="changeCurrentPoint" />
        </n-form-item>
        <n-form-item label="经度">
          <n-input v-model:value="formValue.lngValue" placeholder="经度" @update:value="updateLngValue" />
        </n-form-item>
        <n-form-item label="纬度">
          <n-input v-model:value="formValue.latValue" placeholder="纬度" @update:value="updateLngValue" />
        </n-form-item>
      </div>

      <!-- 圆形禁飞区表单 -->
      <div v-if="formValue.noFlyType === 'circle'">
        <n-form-item label="中心点">
          <n-input v-model:value="formValue.circleCenter" disabled />
        </n-form-item>
        <!-- <n-form-item label="半径"> -->
        <div style="margin-bottom: 6px; margin-left: 2px;">半径</div>
        <NInputNumber v-model:value="formValue.circleRadius" placeholder="半径（米）" :min="0" :max="20000"
          @update:value="updateDistance" />
        <NSlider v-model:value="formValue.circleRadius" :min="1" :max="20000" :on-update-value="updateDistance" />
        <!-- </n-form-item> -->

      </div>
    </n-card>
    <div class="bottom-button">
      <n-button class="cancel-button" @click="clickCancel(true)">
        取消
      </n-button>
      <n-button type="info" class="add-button" @click="saveNoFlyArea()"
        :disabled="formValue.noFlyType === 'polygon' && (allPositionsItem.length < 3 && nowPosition.length < 3)">
        确认
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, toRef, toRefs, watch, onBeforeUnmount } from 'vue';
import { NInput, NSelect, NFormItem, NButton, NCard, NInputNumber, useMessage } from 'naive-ui';
import { saveOrUpdate } from '@/service/api';
import * as Cesium from 'cesium';
const message = useMessage();
defineOptions({ name: 'AirLineCreate' });

// 定义组件接受的属性
const props = defineProps<{
  noFlyList: Api.Device.NoflyInfo[];
  currentAreaIndex: number;
  changeCreate: (status: boolean) => void;
  drawCirCle: (radius: number) => void;
  computeEllipsePositions: (
    center: { longitude: number; latitude: number },
    semiMajorAxis: number,
    semiMinorAxis: number,
    granularity: number
  ) => Cesium.Cartesian3[];
  fetchAllNoFlyList: () => void;
  clearNowAndReDraw: () => void;
  drawPolygon: (positions: { longitude: number; latitude: number }, pointIndex: number) => void;
  changeCurrentPointIndex: (index: number) => void;
  changeIsEdit: (edit: boolean) => void;
}>();
const { changeCreate, drawCirCle, computeEllipsePositions, fetchAllNoFlyList, clearNowAndReDraw, drawPolygon, changeCurrentPointIndex, changeIsEdit } = props;
const { noFlyList, currentAreaIndex } = toRefs(props);
const getDateTimeString = () => {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0');
  const day = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0');
  return `${year}${month}${day}${hours}${minutes}${seconds}${milliseconds}`;
};
// 定义表单属性
const formValue = ref({
  noFlyName: `禁飞区${getDateTimeString()}`,
  noFlyType: '',
  geoJson: '',
  centerGeoJson: '',
  latValue: '',
  lngValue: '',
  circleCenter: '',
  circleRadius: 0,
});
const currentPointIndex = ref<number>(0);
// 禁飞区类型
const noflyTypeOptions = ref([
  { label: '多边形禁飞区', value: 'polygon' },
  { label: '圆形禁飞区', value: 'circle' }
]);
const pointOption = ref<{ label: string, value: number }[]>([])
const changeNoFlyType = (value: string) => {
  formValue.value.noFlyType = value;
  if (value === 'polygon') {
  } else if (value === 'circle') {
  }
};
const allPositionsItem = ref<{ lng: number; lat: number }[]>([]);
const centerPointItem = ref<number[]>([]);
const getPointData = ({ allPositions, centerPoint }: { allPositions: { lng: number; lat: number }[]; centerPoint: { lng: number; lat: number } }, runIndex?: number) => {
  pointOption.value = allPositions.map((item, index) => ({
    label: `${index + 1}`,
    value: index,
  }));
  if (allPositions && allPositions.length >= 3) {
    formValue.value.centerGeoJson = `${centerPoint.lng.toFixed(8)},${centerPoint.lat.toFixed(8)}`;
  }
  if (runIndex === currentPointIndex.value && allPositions.length > 0) {
    currentPointIndex.value = 0
    formValue.value.lngValue = allPositions[currentPointIndex.value].lng.toFixed(8);
    formValue.value.latValue = allPositions[currentPointIndex.value].lat.toFixed(8);
  }
  if (allPositions.length > 0) {
    formValue.value.lngValue = allPositions[currentPointIndex.value].lng.toFixed(8);
    formValue.value.latValue = allPositions[currentPointIndex.value].lat.toFixed(8);
  } else {
    currentPointIndex.value = 0
    formValue.value.lngValue = '';
    formValue.value.latValue = '';
  }
  allPositionsItem.value = allPositions;
  centerPointItem.value = [centerPoint.lng, centerPoint.lat];
  changeIsEdit(true);
};
const changeCurrentPoint = (index: number) => {
  currentPointIndex.value = index;
  if (allPositionsItem.value.length > 0) {
    formValue.value.lngValue = allPositionsItem.value[currentPointIndex.value].lng.toFixed(8);
    formValue.value.latValue = allPositionsItem.value[currentPointIndex.value].lat.toFixed(8);
  } else {
    formValue.value.lngValue = nowPosition.value[currentPointIndex.value][0].toFixed(8);
    formValue.value.latValue = nowPosition.value[currentPointIndex.value][1].toFixed(8);
  }

};
const saveNoFlyArea = async () => {
  if (formValue.value.noFlyType === 'circle') {
    const centerPoint = formValue.value.circleCenter.split(',')
    let value = computeEllipsePositions({
      longitude: parseFloat(centerPoint[0]),
      latitude: parseFloat(centerPoint[1]),
    }, formValue.value.circleRadius, formValue.value.circleRadius, 6)
    allPositionsItem.value = value.map((item) => ({
      lng: Cesium.Math.toDegrees(Cesium.Cartographic.fromCartesian(item).longitude),
      lat: Cesium.Math.toDegrees(Cesium.Cartographic.fromCartesian(item).latitude),
    }));
    const { error } = await saveOrUpdate({
      id: currentAreaIndex.value === -1 ? undefined : currentAreaIndex.value,
      noFlyName: formValue.value.noFlyName,
      noFlyType: 1,
      properties: {},
      type: "Feature",
      radius: formValue.value.circleRadius,
      geoJson: {
        type: formValue.value.noFlyType,
        geometry: {
          type: formValue.value.noFlyType,
          coordinates: allPositionsItem.value.map((item) => [item.lng, item.lat]),
        }
      },
      centerGeoJson: {
        type: 'Point',
        coordinates: formValue.value.circleCenter.split(',').map(Number)
      },
    })
    if (!error) {
      message.success('禁飞区保存成功');
      fetchAllNoFlyList();
      clickCancel(false);
      changeCurrentPointIndex(-1);
    }
  } else {
    const { error } = await saveOrUpdate({
      noFlyName: formValue.value.noFlyName,
      id: currentAreaIndex.value === -1 ? undefined : currentAreaIndex.value,
      noFlyType: 2,
      properties: {},
      type: "Feature",
      geoJson: {
        type: formValue.value.noFlyType,
        geometry: {
          type: formValue.value.noFlyType,
          coordinates: allPositionsItem.value.length > 0 ? allPositionsItem.value.map((item) => [item.lng, item.lat]) : nowPosition.value,
        }
      },
      centerGeoJson: {
        type: 'Point',
        coordinates: formValue.value.centerGeoJson.split(',').map(Number)
      },
    })
    if (!error) {
      message.success('禁飞区保存成功');
      fetchAllNoFlyList();
      clickCancel(false);
      changeCurrentPointIndex(-1);
    }
  }

}
// 清空表单值
const clearFormValue = () => {
  nowPosition.value = [];
  formValue.value = {
    noFlyName: '',
    noFlyType: '',
    geoJson: '',
    centerGeoJson: '',
    latValue: '',
    lngValue: '',
    circleCenter: '',
    circleRadius: 0,
  };
  currentPointIndex.value = 0;
  pointOption.value = [];
}
const nowPosition = ref<number[][]>([]);
// 监听currentAreaIndex变化
watch(currentAreaIndex, (value) => {
  if (value >= 0) {
    clearFormValue();
    const currentArea = noFlyList.value.filter((item) => item.id === value)[0];
    formValue.value.noFlyName = currentArea.noFlyName;
    formValue.value.noFlyType = currentArea.noFlyType === 2 ? 'polygon' : 'circle';
    if (currentArea.noFlyType === 1) {
      formValue.value.circleRadius = currentArea.radius || 0;
      formValue.value.circleCenter = `${currentArea.centerGeoJson.coordinates[0]},${currentArea.centerGeoJson.coordinates[1]}`;
    } else {
      formValue.value.lngValue = currentArea.geoJson.geometry.coordinates[0][0].toFixed(8);
      formValue.value.latValue = currentArea.geoJson.geometry.coordinates[0][1].toFixed(8);
      nowPosition.value = currentArea.geoJson.geometry.coordinates;
      pointOption.value = currentArea.geoJson.geometry.coordinates.map((item, index) => ({
        label: `${index + 1}`,
        value: index,
      }));
    }
    formValue.value.centerGeoJson = currentArea.centerGeoJson.coordinates.join(',');
  } else {
    nowPosition.value = [];
  }
}, { immediate: true });
// 实时修改
const setFormValue = (type: string, value: string | number[]) => {
  if (type === 'circleCenter' && Array.isArray(value)) {
    formValue.value.circleCenter = value.join(',');
  } else if (typeof value === 'string') {
    formValue.value.circleRadius = parseFloat(value);
  }

}
function updateDistance() {
  drawCirCle(formValue.value.circleRadius);
  changeIsEdit(true);
}
const updateLngValue = () => {
  if (nowPosition.value.length >= currentPointIndex.value) {
    nowPosition.value[currentPointIndex.value][0] = parseFloat(formValue.value.lngValue);
    nowPosition.value[currentPointIndex.value][1] = parseFloat(formValue.value.latValue);
  }
  drawPolygon({ longitude: parseFloat(formValue.value.lngValue), latitude: parseFloat(formValue.value.latValue) }, currentPointIndex.value);
  changeIsEdit(true);
}
// 点击取消时
const clickCancel = (review?: boolean) => {
  if (review) {
    clearNowAndReDraw();
  } else {
    setTimeout(() => {
      clearNowAndReDraw();
    }, 500)
  }
  clearFormValue();
  changeCreate(false);
  changeCurrentPointIndex(-1);
}
defineExpose({
  changeNoFlyType,
  getPointData,
  changeCurrentPoint,
  setFormValue,
});
</script>

<style scoped>
.n-form-item {
  margin-bottom: 12px;
}

.bottom-button {
  width: 100%;
  height: 50px;
  line-height: 40px;
  text-align: center;
  background: #111;
  position: absolute;
  bottom: 20px;
  border-top: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px 0;
}

.add-button {
  width: 100px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  /* background: #8CC9FF; */
  border-radius: 5px;
  padding: 10px 0;
  margin-left: 30px;
}

.cancel-button {
  width: 100px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  /* background: #777; */
  border-radius: 5px;
  padding: 10px 0;
}
</style>
