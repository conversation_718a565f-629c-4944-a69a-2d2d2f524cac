<script setup lang="ts">
import type { Ref } from 'vue';
import { PropType, computed, inject, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { Locate } from '@vicons/ionicons5';
import { useRoute } from 'vue-router';
import AgoraRTC, { IAgoraRTCClient, IAgoraRTCRemoteUser } from 'agora-rtc-sdk-ng';
import { useMessage } from 'naive-ui';
import TCPlayer, { type TCPlayerConfig } from 'tcplayer.js';
import { $t } from '@/locales';
import { useAirportStore } from '@/store/modules/airport';
import { fetchLivestreamInfo, startLivestream, stopLivestream } from '@/service/api';
import { fetchAIList } from '@/service/api/ai';
import { useDeviceStore } from '@/store/modules/device';
import 'tcplayer.js/dist/tcplayer.min.css';

const airportStore = useAirportStore();
const deviceStore = useDeviceStore();
const route = useRoute();
const socket = inject<{ chatMessage: Ref<string> }>('useSocket'); // 通过 inject 获取 provide 的 socket 对象
const AIList = ref<Api.AI.AIItem[]>([]);

defineOptions({
  name: 'FlyInfo'
});
const emits = defineEmits(['focus-airport']);
const active = ref(false);

const message = useMessage();

const flyInfoDockSN = ref();
const flyInfoDroneSN = ref();
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});

const livePara = reactive({ url: '' });

// 定义组件接受的属性
// const props = defineProps<{
//   airportFlyInfo: Api.Airport.AirportDeviceInfo[];
// }>();

function onClickAirPort(index: number) {
  console.log('onClickAirPort ~ index:', index);
  // airportStore.addVideoBox(props.airportFlyInfo[index]);
}

function onFocusAirport(index: number) {
  console.log('onFocusAirport ~ index:', index);
  emits('focus-airport', index);
  // airportStore.addVideoBox(props.airportFlyInfo[index]);
}

async function getAIList() {
  const { data } = await fetchAIList();
  console.log('getAIList ~ data:', data.rows);
  // data.rows.forEach((item:Api.AI.AIItem) => { item.status = Boolean(item.status) });
  AIList.value = data.rows;
}

// 视频相关设置
const videoWidth = ref(1280);
const videoHeight = ref(720);

// 直播播放器
const playerState = reactive({
  instance: null as InstanceType<typeof TCPlayer> | null,
  config: {
    licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1329835514_1/v_cube.license',
    ProgressMarker: false,
    muted: true,
    controls: false,
    autoplay: true,
    width: '320px',  // 初始宽度
    height: '180px', // 初始高度
  }
});

function refreshStart(){
  onStreamStart();
}

// 开始推流
const onStreamStart = async () => {
  const { data } = await startLivestream({ sn: flyInfoDockSN.value });
  if(!data || !data.url){
    return;
  }
  livePara.url = data.url;
  // 创建新的播放器实例
  playerState.instance = new TCPlayer('player-container-2', {
    ...playerState.config,
    webrtcConfig: {
      connectRetryCount: 3,
      connectRetryDelay: 3,
      receiveAudio: false,
      receiveSEI: false,
      showLog: false
    },
    sources: [
      {
        src: livePara.url
      }
    ]
  });

  // 获取视频宽高
  playerState.instance.on('loadedmetadata', () => {
    if (playerState.instance) {
      videoWidth.value = playerState.instance.videoWidth();
      videoHeight.value = playerState.instance.videoHeight();
      console.log('playerState.instance.videoWidth(): ', playerState.instance.videoWidth());
      console.log('playerState.instance.videoHeight(): ', playerState.instance.videoHeight());
    }
  })
};

// 发送直播关闭信号
const onStreamStop = async () => {
  if (playerState.instance) {
    playerState.instance.dispose();
    playerState.instance = null;
  }
  const { error } = await stopLivestream({ sn: flyInfoDockSN.value });
};

const isMinimized = ref(false); // 是否最小化
const toggleMinimize = () => {
  console.log('toggleMinimize: ', isMinimized.value);
  isMinimized.value = !isMinimized.value;
};

const currentTime = ref("");
const currentDate = ref("");

const updateTime = () =>{
  const now = new Date();
  currentTime.value = now.toLocaleTimeString();
  currentDate.value = now.toLocaleDateString();
}

// 处理socket消息
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  if (biz_code === 'device_osd' && flyInfoDroneSN.value === sn) {
    // 本次任务无人机消息
    airportDroneItem.value = data;
  } else if (biz_code === 'dock_osd' && flyInfoDockSN.value === sn) {
    // 本次任务机场消息
    airportDockItem.value = data;

  } else if (biz_code === 'device_hms') {
    // 设备告警
  }
}

// 监听Socket消息
watch(
  () => socket?.chatMessage?.value,
  msg => {
    // console.log('Received chat message:', msg);
    // 处理接收到的消息
    onProcessMessage(msg);
  }
);

onMounted(() => {
  if (route.query.sn) {
    // 获取设备编码和下标
    flyInfoDockSN.value = route.query.sn;
    flyInfoDroneSN.value = route.query.dsn;
    onStreamStart();
  }
  getAIList();
  updateTime();
  setInterval(updateTime, 1000);
});

onBeforeUnmount(() => {
  onStreamStop();
});


</script>

<template>
  <div class="absolute right-[calc(50vw+5vw-45px)] bottom-1 h-max w-max  text-light zindex-1 rd-t">

    <n-flex justify="end">
      <div class="">
        <!-- 机场数据 -->
        <div class="absolute top-0 right-450px w-max">
          <!-- 经纬度 -->
          <div class="flex items-center p-[0.47vh]">
            <SvgIcon icon="mdi:crosshairs-gps" class="mr-[0.27vw] text-lg text-gray" size="24" />
            <NText class="pr-[0.82vw] color-light">
              纬度：{{ Number(deviceStore.dockInfo?.latitude || airportDockItem.host?.latitude)?.toFixed(6) }}
            </NText>
            <NText class="color-light">
              经度：{{ Number(deviceStore.dockInfo?.longitude || airportDockItem.host?.longitude)?.toFixed(6) }}
            </NText>
          </div>

          <!-- 详细信息栏 -->
          <n-flex class="p-[0.47vh] justify-between">
            <!-- GNSS -->
            <div class="flex items-center w-40% text-left">
              <SvgIcon icon="mdi:satellite-uplink" class="mr-[0.27vw] text-lg text-gray" size="24" />
              <NText class="text-14px text-gray">GNSS：</NText>
              <NText class="text-12px color-light font-black">{{ airportDockItem.host?.gpsNumber || 0 }}</NText>
            </div>
            <!-- RTK -->
            <div class="flex items-center text-left">
              <SvgIcon icon="mdi:alpha-r-box" class="mr-[0.27vw] text-lg text-gray" size="24" />
              <NText class="text-14px text-gray font-700 font-mono">RTK：</NText>
              <NText class="text-12px color-light font-black">{{ airportDockItem.host?.rtkNumber || '--' }} FIX
              </NText>
            </div>
          </n-flex>

          <n-flex class="p-[0.47vh] justify-between">
            <!-- 图传 -->
            <div class="flex items-center w-40% text-left">
              <SvgIcon icon="mdi:cloud-upload" class="mr-[0.27vw] text-lg text-gray" size="24" />
              <NText class="text-14px text-gray">图传：</NText>
              <NText class="text-12px color-light font-black">4G</NText>
            </div>
            <!-- 海拔 -->
            <div class="flex items-center w-54% text-left whitespace-nowrap">
              <SvgIcon icon="mdi:image-filter-hdr" class="mr-[0.27vw] text-lg text-gray" size="24" />
              <NText class="text-14px text-gray">海拔高度：</NText>
              <NText class="text-12px color-light font-black">{{ (Number(airportDockItem.host?.height || 0) ?? 0).toFixed(1) }} m</NText>
            </div>
          </n-flex>

          <n-flex class="p-[0.47vh]  justify-between">
            <!-- 电池 -->
            <div class="flex items-center flex-1 text-left">
              <SvgIcon :icon="airportDockItem.host?.batteryState === 'true' ? 'mdi:battery-charging' : 'mdi:battery-80'"
                class="mr-[0.27vw] text-lg"
                :class="[airportDockItem.host?.batteryState === 'true' ? 'text-green-500' : 'text-gray']" size="24" />
              <NText class="text-14px text-gray" style="cursor: pointer">电池：</NText>
              <NText class="text-12px text-#ffffff font-black pr-5px"> {{ airportDockItem.host?.capacityPercent || 0 }}%
              </NText>
              <span
                v-if="airportDroneItem.host?.battery?.batteries && airportDroneItem.host.battery.batteries.length > 0">
                <span v-for="item in airportDroneItem.host.battery.batteries" :key="item.temperature">
                  <NText class="text-12px text-gray">
                    {{ item.voltage ? (item.voltage / 1000).toFixed(1) : '--' }}V
                    {{ item.temperature ? item.temperature : '--' }}℃
                  </NText>
                </span>
              </span>
            </div>
          </n-flex>
        </div>
      </div>


      <div class="relative h-234px w-416px" >
        <!-- 机场视频窗口 -->
        <div v-show="!isMinimized" class="h-234px w-416px bg-dark-theme relative ">
          <video id="player-container-2" class="h-234px w-416px object-fill pt-0" preload="auto" playsinline
            webkit-playsinline></video>
          <div @click="toggleMinimize" class="absolute bottom-0 right-0 p-5px bg-black bg-opacity-50">
            <SvgIcon icon="mdi:arrow-bottom-right-thick"
              class="cursor-pointer text-lg text-gray hover:text-gray-300 transition-colors duration-200" size="24" />
          </div>
<!--          <div @click="refreshStart"-->
<!--               class="absolute top-0 right-0 p-5px bg-black bg-opacity-50">-->
<!--              <SvgIcon icon="humbleicons:refresh"-->
<!--                       class="cursor-pointer text-lg text-gray hover:text-gray-300 transition-colors duration-200"-->
<!--                       size="24" />-->
<!--          </div>-->
          <div @click="refreshStart"
               class="absolute top-0 right-0 p-5px  bg-opacity-50">
            <p>{{currentDate}}</p>
            <p>{{currentTime}}</p>
          </div>
        </div>

        <div v-show="isMinimized" class="h-234px w-416px">
          <div @click="toggleMinimize" class="absolute bottom-0 right-0 text-center cursor-pointer p-5px bg-dark-theme rounded-lg shadow-lg">
            <SvgIcon icon="mdi:train-car-box-full" size="34"
              class="text-gray-300 hover:text-gray-400 transition-colors duration-200 h-12 w-12" />
            <n-text>机场</n-text>
          </div>
        </div>
      </div>


      <!-- 经纬度 -->
      <!-- <div class="flex items-center bg-dark-theme p-5px">
        <SvgIcon icon="mdi:crosshairs-gps" class="mr-5px text-lg text-gray" size="24" />
        <NText class="pr-15px color-light">
          纬度：{{ airportDroneItem.host?.latitude || deviceStore.dockInfo?.latitude }}
        </NText>
        <NText class="color-light">经度：{{ airportDroneItem.host?.longitude || deviceStore.dockInfo?.longitude }}</NText>
      </div> -->


      <!-- GNSS/RTK -->
      <!-- <n-flex class="p-5px justify-between">
        <div class="flex items-center w-40% text-left">
          <SvgIcon icon="mdi:satellite-uplink" class="mr-5px text-lg text-gray" size="24" />
          <NText class="text-14px text-gray">GNSS：</NText>
          <NText class="text-12px color-light font-black">{{ airportDockItem.host?.gpsNumber || 0 }}</NText>
        </div>
        <div class="flex items-center text-left">
          <SvgIcon icon="mdi:alpha-r-box" class="mr-5px text-lg text-gray" size="24" />
          <NText class="text-14px text-gray font-700 font-mono">RTK：</NText>
          <NText class="text-12px color-light font-black">{{ airportDockItem.host?.rtkNumber || '--' }} FIX</NText>
        </div>
      </n-flex> -->

      <!-- 图传/海拔 -->
      <!-- <n-flex class="p-5px justify-between">
        <div class="flex items-center w-40% text-left">
          <SvgIcon icon="mdi:cloud-upload" class="mr-5px text-lg text-gray" size="24" />
          <NText class="text-14px text-gray">图传：</NText>
          <NText class="text-12px color-light font-black">4G</NText>
        </div>
        <div class="flex items-center w-54% text-left">
          <SvgIcon icon="mdi:image-filter-hdr" class="mr-5px text-lg text-gray" size="24" />
          <NText class="text-14px text-gray">海拔高度：</NText>
          <NText class="text-12px color-light font-black"> {{ (Number(airportDockItem.host?.height || 0) ??
            0).toFixed(2) }} m </NText>
        </div>
      </n-flex> -->

      <!-- 电池 -->
      <!-- <n-flex class="p-5px justify-between">
        <div class="flex items-center flex-1 text-left">
          <SvgIcon :icon="airportDockItem.host?.batteryState === 'true' ? 'mdi:battery-charging' : 'mdi:battery-80'"
            class="mr-5px text-lg"
            :class="[airportDockItem.host?.batteryState === 'true' ? 'text-green-500' : 'text-gray']" size="24" />
          <NText class="text-14px text-gray" style="cursor: pointer">电池：</NText>
          <NText class="text-12px text-#ffffff font-black"> {{ airportDockItem.host?.capacityPercent || 0 }}% </NText>
          <span v-if="airportDroneItem.host?.battery?.batteries && airportDroneItem.host.battery.batteries.length > 0">
            <span v-for="item in airportDroneItem.host.battery.batteries" :key="item.temperature">
              <NText class="text-12px text-gray">
                {{ item.voltage ? (item.voltage / 1000).toFixed(1) : '--' }}V
                {{ item.temperature ? item.temperature : '--' }}℃
              </NText>
            </span>
          </span>
        </div>
      </n-flex> -->
      <!-- AI识别 -->
      <!-- <div class="my-4px bg-dark px-15px py-6px h-39vh">
      <div class="flex items-center pb-10px text-18px">
        AI识别
        <SvgIcon icon="mdi:help-circle" class="ml-5px" />
      </div>
      <div>
        <NScrollbar class="myscrollbar">
          <NFlex justify="space-between" cols="2">
            <span v-for="item in AIList" :key="item.code" class="w-45%">
              <NSwitch v-model:value="item.status" />
              {{ item.name }}
            </span>
          </NFlex>
        </NScrollbar>
      </div>
    </div> -->

    </n-flex>
  </div>
</template>

<style>
/* .myscrollbar {
  max-height: calc(100vh - 610px);
  overflow: hidden;
} */
</style>

<style scoped>
#player_dock>div:not(:last-child) {
  display: none;
}
</style>
