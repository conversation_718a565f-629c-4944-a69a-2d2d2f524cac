<script setup lang="ts">
import { useEcharts } from '@/hooks/common/echarts';
import {fetchDroneBatteryList} from "@/service/api";

defineOptions({
  name: 'DataBatteryProportion'
});

const { domRef, updateOptions } = useEcharts(() => ({
  tooltip: {
    trigger: 'item'
  },
  series: [
    {
      color: ['#fb7e7e', '#fedc69', '#0dca8c'],
      type: 'pie',
      radius: ['45%', '75%'],
      avoidLabelOverlap: false,
      itemStyle: {
        borderRadius: 10,
        borderColor: '#fff',
        borderWidth: 1
      },
      label: {
        show: true,
        position: 'outside',
        formatter: '{b}: {d}%',
        fontWeight: 'bold'
      },
      emphasis: {
        label: {
          show: true,
          fontSize: '12'
        }
      },
      labelLine: {
        show: true,
        length: 15,
        length2: 10
      },
      data: [] as { name: string; value: number }[]
    }
  ]
}));

async function mockData() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  const { error, data } = await fetchDroneBatteryList(0);
  const numberData = [
    { name: '待报废', value: 0 },
    { name: '告警', value: 0 },
    { name: '健康', value: 0 }
  ];
  if (!error) {
    data.rows.forEach(item => {
      if (item.loopTimes >= 400) {
        numberData[0].value += 1;
      } else if (item.loopTimes >= 300) {
        numberData[1].value += 1;
      } else {
        numberData[2].value += 1;
      }
    });
  }

  await updateOptions(opts => {
    opts.series[0].data = numberData;
    return opts;
  });
}

async function init() {
  await mockData();
}

init();
</script>

<template>
  <div ref="domRef" class="h-240px overflow-hidden" />
</template>

<style scoped></style>
