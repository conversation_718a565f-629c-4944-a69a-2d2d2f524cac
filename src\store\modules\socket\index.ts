import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import { useEventListener } from '@vueuse/core';
import { SetupStoreId } from '@/enum';
import { localStg } from '@/utils/storage';

// store/airport.ts
export const useSocketStore = defineStore(SetupStoreId.Airport, () => {
  const chatMessage = reactive({ value: {} });

  function updateChatMessage(newMessage:any) {
    chatMessage.value = newMessage;
  }
  return {
    chatMessage, updateChatMessage
  };
});
