<template>
  <div class="absolute bottom-0 right-[calc(50vw+5vw-45px+440px)] h-30 flex flex-col w-70">
    <n-text class="text-14px text-gray text-left"><n-icon size="16">
        <BrainCircuit24Filled />
      </n-icon>AI识别</n-text>
    <n-scrollbar style="height: 95%;width:100%">
      <template v-if="AIList.length > 0">
        <div v-for="(row, rowIndex) in chunkedList" :key="rowIndex" class="flex">
          <div v-for="(item, index) in row" :key="item.id" class="flex-1 flex gap-2 justify-start items-center rounded">
            <n-text style="width:100%">{{ item.name }}</n-text>
            <n-switch v-model:value="item.status" @update:value="handleSwitchChange(item)" />
          </div>
        </div>
      </template>
      <n-empty v-else description="暂未配置AI功能" class="h-full flex items-center justify-center" />
    </n-scrollbar>
  </div>
</template>

<script setup lang="ts">
import type { Ref } from 'vue';
import { PropType, computed, inject, onBeforeUnmount, onMounted, reactive, ref, watch } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { Locate } from '@vicons/ionicons5';
import { BrainCircuit24Filled } from '@vicons/fluent';
import { useRoute } from 'vue-router';
import AgoraRTC, { IAgoraRTCClient, IAgoraRTCRemoteUser } from 'agora-rtc-sdk-ng';
import { useMessage } from 'naive-ui';
import { $t } from '@/locales';
import { useAirportStore } from '@/store/modules/airport';
import { fetchLivestreamInfo, startLivestream, stopLivestream } from '@/service/api';
import { fetchAIList } from '@/service/api/ai';
import { useDeviceStore } from '@/store/modules/device';
import 'tcplayer.js/dist/tcplayer.min.css';
import { useAIServerStore } from '@/store/modules/ai';

const airportStore = useAirportStore();
const deviceStore = useDeviceStore();
const route = useRoute();
const socket = inject<{ chatMessage: Ref<string> }>('useSocket'); // 通过 inject 获取 provide 的 socket 对象
const aiServerStore = useAIServerStore();

const AIList = ref<Api.AI.AIItem[]>([]);

defineOptions({ name: 'FlyAiList' });
// const emits = defineEmits(['focus-airport']);
const active = ref(false);

const message = useMessage();

const flyInfoDockSN = ref();
const flyInfoDroneSN = ref();
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});

// 定义组件接受的属性
// const props = defineProps<{
//   airportFlyInfo: Api.Airport.AirportDeviceInfo[];
// }>();

onMounted(async () => {
  const { data } = await fetchAIList(100, '1');
  console.log('onMounted: ', data.rows);
  AIList.value = data.rows;
  aiServerStore.updateAIList(AIList.value);
  console.log('AIList.value', AIList.value);
})

const handleSwitchChange = (item: Api.AI.AIItem) => {
  console.log(`${item.name} 状态已更改为：${item.status}`);
  // 添加类型断言确保id是string
  aiServerStore.updateAIItemStatus(Number(item.id), item.status as boolean);
};

// 将列表分成每行两项
const chunkedList = computed(() => {
  const chunkSize = 2;
  return AIList.value.reduce((result, item, index) => {
    const chunkIndex = Math.floor(index / chunkSize);
    if (!result[chunkIndex]) {
      result[chunkIndex] = [];
    }
    result[chunkIndex].push(item);
    return result;
  }, [] as Api.AI.AIItem[][]);
});
</script>

<style scoped></style>
