<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { NButton, NImage, NTag, NText, useMessage } from 'naive-ui';
import dayjs from 'dayjs';
import { fetchAIList, fetchAiRecordList, fetchStatusList, updateRecordConfirm } from '@/service/api';
import {deptTree} from "@/service/api/user";

const tableLoading = ref(true);
const message = useMessage();
const treeData = ref([]);
let generalStatusOptions: { label: string; value: string }[];
let generalAirportOptions: { label: string; value: string }[];

const formValue = reactive<{ deptId: number | null; selectResoult: string | null; selectStatus: []; dateRange: [number, number] | null }>({
  selectResoult: null,
  deptId: null,
  selectStatus: [],
  dateRange: null
});

const columns = [
  {
    title: 'AI识别图片',
    key: 'image',
    render(row: TableItem) {
      return h(NImage, {
        src: row.image,
        style: {
          height: '80px'
        }
      });
    }
  },
  {
    title: '识别结果',
    key: 'eventName'
  },
  {
    title: '设备',
    key: 'deviceName'
  },
  {
    title: '识别时间',
    key: 'eventTime'
  },
  {
    title: '确认状态',
    key: 'aiConfirmStatus',
    render(row: TableItem) {
      return h(
        NTag,
        {
          type: row.aiConfirmStatus === 1 ? 'success' : row.aiConfirmStatus === 0 ? 'warning' : 'error'
        },
        { default: () => (row.aiConfirmStatus === 0 ? '待确认' : row.aiConfirmStatus === 1 ? '已确认' : '误报') }
      );
    }
  },
  {
    title: '识别结果确认',
    key: 'action',
    render(row: TableItem) {
      return h('span', [
        h(
          NText,
          {},
          {
            default: () => {
              const elements = [];
              if (row.aiConfirmStatus === 0) {
                elements.push(
                  h(
                    NText,
                    { type: 'info', class: 'cursor-pointer', onClick: () => recordConfirm(row.eventId, 1) },
                    { default: () => '确认' }
                  )
                );
                elements.push(
                  h(
                    NText,
                    { type: 'error', class: 'ml3 cursor-pointer', onClick: () => recordConfirm(row.eventId, 2) },
                    { default: () => '误报' }
                  )
                );
              }
              return elements;
            }
          }
        )
      ]);
    }
  }
];

const tableData = ref([]);

const handleResetClick = () => {
  tableLoading.value = true;
  formValue.selectResoult = null;
  formValue.selectStatus = [];
  formValue.dateRange = null;
  getAiRecordList();
};

const handleQueryClick = () => {
  tableLoading.value = true;
  getAiRecordList();
}

function renderCell(value: string | number) {
  if (!value) {
    return h(NText, { depth: 3 }, { default: () => '---' });
  }
  return value;
}

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  itemCount: 0
});

// 更改分页页数
const onPaginationChange = (page: number) => {
  pagination.pageNum = page;
  getAiRecordList();
};

async function recordConfirm(id: number, status: number) {
  const data = {
    eventId: id,
    aiConfirmStatus: status
  };
  await updateRecordConfirm(data);
  message.success('操作成功');
  tableLoading.value = true;
  await getAiRecordList();
}

// 获取机场列表
async function getAiRecordList() {
  const { dateRange, selectStatus, selectResoult, deptId } = formValue;
  const json = {
    deptId,
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize,
    aiConfirmStatusList: selectStatus,
    aiSubType: selectResoult,
    startDate: dateRange && dateRange[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : null,
    endDate: dateRange && dateRange[1] ? dayjs(dateRange[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null
  };

  const { error, data: recordList } = await fetchAiRecordList(json);
  if (!error) {
    tableLoading.value = false;
    tableData.value = recordList.rows;
    pagination.itemCount = recordList.total;
  }
}

async function getAIList() {
  const { data } = await fetchAIList();
  // data.rows.forEach((item:Api.AI.AIItem) => { item.status = Boolean(item.status) });
  generalAirportOptions = data.rows.map(v => ({
    label: v.name,
    value: v.aiType
  }));
}

async function getStatus(dataType: string) {
  const { data } = await fetchStatusList(dataType);
  generalStatusOptions = data.map(v => ({
    label: v.dictLabel,
    value: v.dictValue
  }));
}

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
}

onMounted(() => {
  getStatus('ai_confirm_status');
  getDeptTree();
  getAiRecordList();
  getAIList();
});

const disablePreviousDate = (ts: number) => {
  return ts > Date.now();
};
</script>

<script lang="ts">
interface TableItem {
  aiConfirmStatus: number;
  deviceName: string;
  eventId: number;
  eventName: string;
  eventTime: string;
  image: string;
}
</script>

<template>
  <div>
    <NCard title="AI识别记录" :bordered="false">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :cols="5" :x-gap="36">
          <NFormItemGi label="组织选择：" path="deptId">
            <NTreeSelect
              clearable
              v-model:value="formValue.deptId"
              filterable
              key-field="id"
              label-field="label"
              children-field="children"
              :options="treeData"
              :default-value="formValue.deptId"
            />
          </NFormItemGi>
          <NFormItemGi label="识别结果：" path="selectResoult">
            <NSelect
              v-model:value="formValue.selectResoult"
              clearable
              :options="generalAirportOptions"
            />
          </NFormItemGi>
          <NFormItemGi label="确认状态：" path="selectStatus">
            <NSelect v-model:value="formValue.selectStatus" clearable multiple :options="generalStatusOptions" />
          </NFormItemGi>
          <NFormItemGi label="识别时间：" path="dateRange">
            <NDatePicker
              v-model:value="formValue.dateRange"
              type="daterange"
              :is-date-disabled="disablePreviousDate"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi>
            <NButton attr-type="button" class="mr2" @click="handleResetClick">重置</NButton>
            <NButton type="primary" attr-type="button" @click="handleQueryClick">查询</NButton>
          </NFormItemGi>
        </NGrid>
      </NForm>
      <NScrollbar style="max-height: 66vh">
        <NDataTable
          ref="table"
          remote
          :columns="columns"
          :data="tableData"
          :loading="tableLoading"
          :render-cell="renderCell"
        />
      </NScrollbar>
      <div class="mt-15px flex justify-end">
        <NPagination
          v-model:page="pagination.pageNum"
          :item-count="pagination.itemCount"
          @update:page="onPaginationChange"
        />
      </div>
    </NCard>
  </div>
</template>

<style scoped></style>
