<template>
	<div
		class="fixed zindex-2 flex items-center justify-between relative bg-transparent w-full h-50px p-15px shadow-[0_10px_25px_-5px_rgba(0,0,0,0.5)]">
		<!-- 背景图片容器 -->
		<!-- <div class="bg-image absolute top-0 left-0 w-full h-full z-[-1]"></div> -->

		<!-- 回退按钮 -->
		<!-- <div class="flex items-center pl-10 top-0 absolute left-0 mt-3">
			<n-icon @click="handlePageBack" class="cursor-pointer" :component="ChevronBackOutline" size="24"
				color="#ffffff" />
		</div> -->
		<!-- 新增时间显示区域 -->
		<div class="flex items-center pr-10 top-0 absolute right-0 mt-4">
			<span class="text-white text-4 tracking-1">{{ currentTime }}</span>
		</div>
		<div class="absolute top-0 left-1/2 transform -translate-x-1/2 inline-block text-center h-65px w-full"
			style="z-index: 1;">
			<img src="@/assets/imgs/datav_title.png" alt="Title Image" class="h-full w-full" />
			<!-- 标题 -->
			<span
				class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-8 pt-1  tracking-1"
				style="font-family: 'DouyuFont', sans-serif;">{{ authStore.sysInfo.webConfig.webName }}</span>
		</div>
		<!-- 日期选择条 -->
		<div class="date-filter absolute top-20 left-1/2 transform -translate-x-1/2 z-1000 bg-transparent">
			<div class="flex bg-[rgba(26,115,232,0.8)] rounded-2xl p-2">
				<span v-for="(option, index) in timeOptions" :key="index" :class="[
					'px-6 py-1 text-sm font-medium rounded-lg transition-colors',
					selectedTimeRange === option.value ? 'bg-[rgba(33,150,243,0.8)] text-white' : 'text-[#e5f3ff]',
					index !== 0 ? 'ml-4 ' : ''
				]" @click="selectTimeRange(option.value)">
					{{ option.label }}
				</span>
			</div>
		</div>
		<!-- 左侧三个面板 -->
		<div class="absolute left-5 bottom-0 transform  flex flex-col space-y-0 ml-0 z-100" style="height: 90vh;">
			<!-- 左侧上面板 - 组织详情 -->
			<div class="flex flex-col mb-3" style="height: 33.3%;width:90%">
				<div class="flex items-center mb-0 ">
					<img src="@/assets/imgs/title.png" alt="Title" class="h-6 ml-8">
					<span class="absolute left-15 text-white text-sm" style="font-family: 'DouyuFont', sans-serif;">{{
						authStore.userInfo.user.dept.deptName }}</span>
				</div>
				<div class="relative h-full w-full">
					<img src="@/assets/imgs/panel.png" alt="Left Panel Top" class="h-full w-full object-contain">
					<div class="absolute top-0 left-0 w-full h-full grid grid-cols-3 grid-rows-3 gap-0 p-2 ml-4 mt-8">
						<!-- 第一行 -->
						<div class="flex items-center p-2">
							<div class="flex items-center w-full">
								<n-icon size="24" color="#2EC7C9" class="mr-2">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
										<path
											d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
									</svg>
								</n-icon>
								<div class="flex flex-col">
									<span class="text-xs text-gray-300">成员数量</span>
									<span class="text-lg text-white font-bold">{{ indexOrg.userCount }}<span
											class="text-xs ml-1">人</span></span>
								</div>
							</div>
						</div>

						<!-- <div class="flex items-center p-2">
							<div class="flex items-center w-full">
								<n-icon size="24" color="#4ECB73" class="mr-2">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
										<path
											d="M12 2L8 6h3v7.85c-1.3.43-2.25 1.63-2.25 3.05 0 1.82 1.48 3.1 3.25 3.1s3.25-1.28 3.25-3.1c0-1.42-.95-2.62-2.25-3.05V6h3l-4-4z" />
									</svg>
								</n-icon>
								<div class="flex flex-col">
									<span class="text-xs text-gray-300">无人机数</span>
									<span class="text-lg text-white font-bold">{{ indexOrg.droneCount }}<span
											class="text-xs ml-1">架</span></span>
								</div>
							</div>
						</div> -->

						<div class="flex items-center p-2">
							<div class="flex items-center w-full">
								<n-icon size="24" color="#FBD437" class="mr-2">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
										<path d="M12 2L4 7v10l8 5 8-5V7L12 2zm0 14.5l-6-3.5v-7l6 3.5 6-3.5v7l-6 3.5z" />
									</svg>
								</n-icon>
								<div class="flex flex-col">
									<span class="text-xs text-gray-300">设备数量</span>
									<span class="text-lg text-white font-bold">{{ indexOrg.dockCount }}<span
											class="text-xs ml-1">个</span></span>
								</div>
							</div>
						</div>

						<!-- 第二行 -->
						<div class="flex items-center p-2">
							<div class="flex items-center w-full">
								<n-icon size="24" color="#FF6B6B" class="mr-2">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
										<path
											d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z" />
									</svg>
								</n-icon>
								<div class="flex flex-col">
									<span class="text-xs text-gray-300">飞行次数</span>
									<span class="text-lg text-white font-bold">{{ indexOrg.flightCount }}<span
											class="text-xs ml-1">次</span></span>
								</div>
							</div>
						</div>

						<div class="flex items-center p-2">
							<div class="flex items-center w-full">
								<n-icon size="24" color="#9B59B6" class="mr-2">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
										<path
											d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z" />
									</svg>
								</n-icon>
								<div class="flex flex-col">
									<span class="text-xs text-gray-300">航线数量</span>
									<span class="text-lg text-white font-bold">{{ indexOrg.flightLineCount }}<span
											class="text-xs ml-1">条</span></span>
								</div>
							</div>
						</div>

						<div class="flex items-center p-2">
							<div class="flex items-center w-full">
								<n-icon size="24" color="#3498DB" class="mr-2">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
										<path
											d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14h-2v-2h2v2zm0-4h-2V7h2v6z" />
									</svg>
								</n-icon>
								<div class="flex flex-col">
									<span class="text-xs text-gray-300">AI能力</span>
									<span class="text-lg text-white font-bold">{{ indexOrg.aiCount }}<span
											class="text-xs ml-1">次</span></span>
								</div>
							</div>
						</div>

						<!-- 第三行 -->
						<div class="flex items-center p-2">
							<div class="flex items-center w-full">
								<n-icon size="24" color="#1ABC9C" class="mr-2">
									<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
										<path
											d="M17 3H7c-1.1 0-2 .9-2 2v16l7-3 7 3V5c0-1.1-.9-2-2-2zm0 15l-5-2.18L7 18V5h10v13z" />
									</svg>
								</n-icon>
								<div class="flex flex-col">
									<span class="text-xs text-gray-300">在线设备</span>
									<span class="text-lg text-white font-bold">{{ indexOrg.onlineDevice }}<span
											class="text-xs ml-1">台</span></span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 左侧中面板 -->
			<div class="flex flex-col mb-3 relative mb-20px" style="height: 33.3%;width:90%">
				<div class="flex items-center mb-0 mt--5 z-10">
					<img src="@/assets/imgs/title.png" alt="Title" class="h-6 ml-8">
					<span class="absolute left-15 text-white text-sm"
						style="font-family: 'DouyuFont', sans-serif;">机型占比</span>
				</div>
				<img src="@/assets/imgs/panel.png" alt="Left Panel Middle" class="h-full w-full absolute">
				<div id="machineTypeChart" class="w80% h-full mt-15px" style="padding: 10px;"></div>
			</div>

			<!-- 左侧下面板 -->
			<div class="flex flex-col mb-3" style="height: 33.3%;width:90%">
				<div class="flex items-center mb-0">
					<img src="@/assets/imgs/title.png" alt="Title" class="h-6 ml-8">
					<span class="absolute left-15 text-white text-sm"
						style="font-family: 'DouyuFont', sans-serif;">任务信息</span>
				</div>
				<div class="relative h-full w-full">
					<img src="@/assets/imgs/panel.png" alt="Left Panel Bottom" class="h-full w-full object-contain">
					<div class="absolute top-4 left-8% w-90% h-90% overflow-hidden" style="padding: 20px;">
						<div ref="stepsContainer" class="h-full overflow-y-auto no-scrollbar"
							style="font-size: 15px; height: calc(100% - 30px);">
							<n-steps vertical size="small" :current="currentStep" :status="currentStatus">
								<n-step v-for="(task, index) in flyHistoryData" :key="index"
									:title="task.flightName + '-' + task.droneName"
									:description="`状态: ${task.status === 0 ? '执行完成' : task.status === 1 ? '执行失败' : '-----'} | 飞行信息: ${task.flightInfo}`"
									:status="getStepStatus(task.status + '')" />
							</n-steps>
							<!-- 加载更多指示器 -->
							<div v-if="loading" class="flex justify-center py-2">
								<n-spin size="small" />
							</div>
							<div v-if="!hasMore" class="text-center py-2 text-gray-400 text-sm">
								没有更多数据了
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<!-- 地图容器  -->
		<div>
			<div id="cesiumViewer" class="w-100vw h-94vh ml--20px absolute bottom-0 map-filter"></div>
		</div>

		<!-- 右侧三个面板 -->
		<div class="absolute right--12 bottom-0 transform  flex flex-col space-y-0 mr-0" style="height: 90vh;">
			<!-- 右侧上面板 -->
			<div class="flex flex-col mb-3" style="height: 33.3%;width:90%">
				<div class="flex items-center mb-0">
					<img src="@/assets/imgs/title.png" alt="Title" class="h-6 ml-8">
					<span class="absolute left-15 text-white text-sm"
						style="font-family: 'DouyuFont', sans-serif;">飞行数据</span>
				</div>
				<div class="relative h-full w-full">
					<img src="@/assets/imgs/panel.png" alt="Right Panel Middle" class="h-full w-full absolute">
					<div id="dataSummaryChart" class="w-full h-full mt-20px" style="padding: 10px;"></div>
				</div>
			</div>

			<!-- 右侧中面板 -->
			<div class="flex flex-col mb-3" style="height: 33.3%;width:90%">
				<div class="flex items-center mb-0">
					<img src="@/assets/imgs/title.png" alt="Title" class="h-6 ml-8">
					<span class="absolute left-15 text-white text-sm"
						style="font-family: 'DouyuFont', sans-serif;">数据汇总</span>
				</div>
				<div class="relative h-full w-full">
					<img src="@/assets/imgs/panel.png" alt="Right Panel Top" class="h-full w-full object-contain">
					<div id="mediaDataChart" class="absolute top--7 left-3 w-full h-full" style="padding: 10px;">
					</div>
				</div>
			</div>

			<!-- 右侧下面板 -->
			<div class="flex flex-col mb-3 mt-20px" style="height: 33.3%;width:90%">
				<div class="flex items-center mb-0">
					<img src="@/assets/imgs/title.png" alt="Title" class="h-6 ml-8">
					<span class="absolute left-15 text-white text-sm"
						style="font-family: 'DouyuFont', sans-serif;">设备信息</span>
				</div>
				<div class="relative h-full w-full">
					<img src="@/assets/imgs/panel.png" alt="Right Panel Bottom" class="h-full w-full object-contain">
					<div class="absolute top-4 left-5% w-90% h-90% overflow-hidden" style="padding: 20px;">
						<div ref="devicesContainer" class="h-full overflow-y-auto no-scrollbar"
							style="font-size: 15px; height: calc(100% - 30px);">
							<div v-for="(device, index) in deviceList" :key="index" class="device-item mb-3">
								<!-- 第一行：机场名称-状态 -->
								<div class="flex items-center justify-between mb-1">
									<span class="text-white text-sm" style="font-family: 'DouyuFont', sans-serif;">
										{{ device.deviceName }}
									</span>
									<span class="status-indicator" :class="getStatusClass(device.deviceStatus)">
										{{ device.deviceStatus }}
									</span>
								</div>
								<!-- 第二/三行：无人机设备数据 -->
								<div class="grid grid-cols-3 gap-2 text-xs">
									<div class="flex items-center">
										<n-icon size="14" color="#FBD437" class="mr-1">
											<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
												fill="currentColor">
												<path
													d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2l8-2.5V19l-2 1.5V22l3.5-1 3.5 1v-1.5L13 19v-5.5l8 2.5z" />
											</svg>
										</n-icon>
										<span class="text-gray-300">{{ device.droneName || '无' }}</span>
									</div>
									<div class="flex items-center">
										<n-icon size="14" color="#3498DB" class="mr-1">
											<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
												fill="currentColor">
												<!-- 在线图标 -->
												<path v-if="device.droneStatus !== '离线'"
													d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4.59-12.42L10 14.17l-2.59-2.58L6 13l4 4 8-8z" />
												<!-- 离线图标 -->
												<path v-else
													d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z" />
											</svg>
										</n-icon>
										<span class="text-gray-300">{{ device.droneStatus || '离线' }}</span>
									</div>
									<!-- 电量 -->
									<div class="flex items-center">
										<!-- 电量图标 - 根据电量百分比显示不同颜色 -->
										<n-icon size="14" :color="getBatteryColor(device.droneCapacityPercent)"
											class="mr-1">
											<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
												fill="currentColor">
												<path
													d="M17 5.33C17 4.6 16.4 4 15.67 4H14V2h-4v2H8.33C7.6 4 7 4.6 7 5.33V15h10V5.33z" />
												<path
													d="M7 15v5.67C7 21.4 7.6 22 8.33 22h7.33c.74 0 1.34-.6 1.34-1.33V15H7z" />
											</svg>
										</n-icon>
										<!-- 电量百分比文字 - 颜色与图标一致 -->
										<span class="text-xs" :class="getBatteryTextColor(device.droneCapacityPercent)">
											{{ device.droneCapacityPercent }}%
										</span>
									</div>
								</div>
								<div class="grid grid-cols-3 gap-2 text-xs mt-2">
									<!-- 型号 -->
									<div class="flex items-center">
										<n-icon size="14" color="#3498DB" class="mr-1">
											<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
												fill="currentColor">
												<path
													d="M21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16z" />
											</svg>
										</n-icon>
										<span class="text-gray-300">{{ device.droneType || '型号未知' }}</span>
									</div>
									<!-- 任务信息 -->
									<div class="flex items-center">
										<n-icon size="14" color="#4ECB73" class="mr-1">
											<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"
												fill="currentColor">
												<path
													d="M12 2L8 6h3v7.85c-1.3.43-2.25 1.63-2.25 3.05 0 1.82 1.48 3.1 3.25 3.1s3.25-1.28 3.25-3.1c0-1.42-.95-2.62-2.25-3.05V6h3l-4-4z" />
											</svg>
										</n-icon>
										<span class="text-gray-300">{{ device.taskName || "暂无任务" }}</span>
									</div>
								</div>
							</div>
							<!-- 加载更多指示器 -->
							<div v-if="loadingDevices" class="flex justify-center py-2">
								<n-spin size="small" />
							</div>
							<div v-if="!hasMoreDevices" class="text-center py-2 text-gray-400 text-sm">
								没有更多设备了
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- filter遮罩 -->
		<svg id="svgfilters" aria-hidden="true" style="position: absolute; width: 0; height: 0; overflow: hidden;"
			version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
			<defs>
				<filter id="teal-white" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.03 1" />
						<feFuncG type="table" tableValues="0.57 1" />
						<feFuncB type="table" tableValues="0.49 1" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="teal-lightgreen" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.03 0.8" />
						<feFuncG type="table" tableValues="0.57 1" />
						<feFuncB type="table" tableValues="0.49 0.53" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.26 0.95" />
						<feFuncG type="table" tableValues="0.19 0.78" />
						<feFuncB type="table" tableValues="0.11 0.59" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="purple-sepia" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.43 0.97" />
						<feFuncG type="table" tableValues="0.06 0.88" />
						<feFuncB type="table" tableValues="0.37 0.79" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="cherry-icecream" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.84 1" />
						<feFuncG type="table" tableValues="0.05 0.94" />
						<feFuncB type="table" tableValues="0.37 0.61" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="blackCurrant-and-mint" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.75 0.53" />
						<feFuncG type="table" tableValues="0.25 0.97" />
						<feFuncB type="table" tableValues="0.64 0.77" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="sea" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.02 0.13 0.8" />
						<feFuncG type="table" tableValues="0.02 0.47 0.97" />
						<feFuncB type="table" tableValues="0.26 0.52 0.48" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="warm-sea" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.29 0.01 0.97" />
						<feFuncG type="table" tableValues="0.12 0.52 0.94" />
						<feFuncB type="table" tableValues="0.37 0.59 0.47" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="spring-grass" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0 0.38 0.92" />
						<feFuncG type="table" tableValues="0.5 0.8 1" />
						<feFuncB type="table" tableValues="0.5 0.56 0.74" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="red-sunset-with-purple" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.52 0.86 0.97" />
						<feFuncG type="table" tableValues="0 0.08 0.81" />
						<feFuncB type="table" tableValues="0.51 0.24 0.05" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="red-sunset" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.27 0.86 0.97" />
						<feFuncG type="table" tableValues="0.01 0.08 0.81" />
						<feFuncB type="table" tableValues="0.02 0.24 0.05" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="gold-sunset" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.27 0.86 1" />
						<feFuncG type="table" tableValues="0.01 0.31 0.95" />
						<feFuncB type="table" tableValues="0.02 0.02 0.02" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="dark-crimson-sepia" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.01 0.52 0.97" />
						<feFuncG type="table" tableValues="0 0.05 0.81" />
						<feFuncB type="table" tableValues="0.02 0.29 0.61" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="dark-blue-sepia" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.29 0.15 0.97" />
						<feFuncG type="table" tableValues="0.04 0.39 0.93" />
						<feFuncB type="table" tableValues="0.32 0.52 0.73" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="dark-green-sepia" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.25 0.39 0.96" />
						<feFuncG type="table" tableValues="0.16 0.52 0.97" />
						<feFuncB type="table" tableValues="0.06 0.39 0.78" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.98 0.3 0.25" />
						<feFuncG type="table" tableValues="1 0.44 0.24" />
						<feFuncB type="table" tableValues="0.91 0.62 0.39" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="warm-x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.98 0.75 0.51" />
						<feFuncG type="table" tableValues="1 0.45 0.11" />
						<feFuncB type="table" tableValues="0.91 0.39 0.29" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="golden-x-rays" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.98 1 0.94" />
						<feFuncG type="table" tableValues="1 0.98 0.44" />
						<feFuncB type="table" tableValues="0.91 0.43 0.02" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="purple-warm" x="-10%" y="-10%" width="120%" height="120%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.52 0.97 1" />
						<feFuncG type="table" tableValues="0 0.62 1" />
						<feFuncB type="table" tableValues="0.51 0.39 0.89" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="green-pink-acid" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="1 0.98 0.1" />
						<feFuncG type="table" tableValues="0.17 1 0.82" />
						<feFuncB type="table" tableValues="0.7 0.84 0.67" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="yellow-blue-acid" x="-10%" y="-10%" width="120%" height="120%"
					filterUnits="objectBoundingBox" primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feColorMatrix type="matrix" values=".33 .33 .33 0 0
					.33 .33 .33 0 0
					.33 .33 .33 0 0
					0 0 0 1 0" in="SourceGraphic" result="colormatrix" />
					<feComponentTransfer in="colormatrix" result="componentTransfer">
						<feFuncR type="table" tableValues="0.01 0.97 0.89" />
						<feFuncG type="table" tableValues="0.38 1 1" />
						<feFuncB type="table" tableValues="1 0.89 0.01" />
						<feFuncA type="table" tableValues="0 1" />
					</feComponentTransfer>
					<feBlend mode="normal" in="componentTransfer" in2="SourceGraphic" result="blend" />
				</filter>
				<filter id="noise" x="0%" y="0%" width="100%" height="100%">
					<feTurbulence baseFrequency="0.01 0.4" result="NOISE" numOctaves="2" />
					<feDisplacementMap in="SourceGraphic" in2="NOISE" scale="20" xChannelSelector="R"
						yChannelSelector="R">
					</feDisplacementMap>
				</filter>
				<filter id="squiggly-0">
					<feTurbulence id="turbulence1" baseFrequency="0.02" numOctaves="3" result="noise" seed="0" />
					<feDisplacementMap id="displacement" in="SourceGraphic" in2="noise" scale="6" />
				</filter>
				<filter id="squiggly-1">
					<feTurbulence id="turbulence2" baseFrequency="0.02" numOctaves="3" result="noise" seed="1" />
					<feDisplacementMap in="SourceGraphic" in2="noise" scale="8" />
				</filter>
				<filter id="squiggly-2">
					<feTurbulence id="turbulence3" baseFrequency="0.02" numOctaves="3" result="noise" seed="2" />
					<feDisplacementMap in="SourceGraphic" in2="noise" scale="6" />
				</filter>
				<filter id="squiggly-3">
					<feTurbulence id="turbulence4" baseFrequency="0.02" numOctaves="3" result="noise" seed="3" />
					<feDisplacementMap in="SourceGraphic" in2="noise" scale="8" />
				</filter>
				<filter id="squiggly-4">
					<feTurbulence id="turbulence5" baseFrequency="0.02" numOctaves="3" result="noise" seed="4" />
					<feDisplacementMap in="SourceGraphic" in2="noise" scale="6" />
				</filter>
				<filter id="posterize">
					<feComponentTransfer>
						<feFuncR type="discrete" tableValues="0 .5 1" />
					</feComponentTransfer>
				</filter>
				<filter id="dancing" x="-20%" y="-20%" width="140%" height="140%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="linearRGB">
					<feMorphology operator="dilate" radius="4 4" in="SourceAlpha" result="morphology" />
					<feFlood flood-color="#30597E" flood-opacity="1" result="flood" />
					<feComposite in="flood" in2="morphology" operator="in" result="composite" />
					<feComposite in="composite" in2="SourceAlpha" operator="out" result="composite1" />
					<feTurbulence type="fractalNoise" baseFrequency="0.01 0.02" numOctaves="1" seed="0"
						stitchTiles="stitch" result="turbulence" />
					<feDisplacementMap in="composite1" in2="turbulence" scale="17" xChannelSelector="A"
						yChannelSelector="A" result="displacementMap" />
					<feMerge result="merge">
						<feMergeNode in="SourceGraphic" />
						<feMergeNode in="displacementMap" />
					</feMerge>
				</filter>
				<filter id="drops" x="-20%" y="-20%" width="140%" height="140%" filterUnits="objectBoundingBox"
					primitiveUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
					<feTurbulence type="turbulence" baseFrequency="0.05 0.05" numOctaves="1" seed="3"
						stitchTiles="stitch" result="turbulence" />
					<feComposite in="turbulence" in2="SourceGraphic" operator="in" result="composite" />
					<feColorMatrix type="matrix" values="1 0 0 0 0
					0 1 0 0 0
					0 0 1 0 0
					0 0 0 25 -2" in="composite" result="colormatrix" />
					<feComposite in="SourceGraphic" in2="colormatrix" operator="in" result="composite1" />
					<feGaussianBlur stdDeviation="3 3" in="composite1" result="blur" />
					<feSpecularLighting surfaceScale="2" specularConstant="1" specularExponent="20"
						lighting-color="#fffffd" in="blur" result="specularLighting">
						<feDistantLight azimuth="-90" elevation="150" />
					</feSpecularLighting>
					<feSpecularLighting surfaceScale="2" specularConstant="1" specularExponent="20"
						lighting-color="#cae1fe" in="blur" result="specularLighting1">
						<feDistantLight azimuth="90" elevation="150" />
					</feSpecularLighting>
					<feSpecularLighting surfaceScale="7" specularConstant="1" specularExponent="35"
						lighting-color="#fcfeff" in="blur" result="specularLighting2">
						<fePointLight x="150" y="50" z="300" />
					</feSpecularLighting>
					<feComposite in="specularLighting" in2="composite1" operator="in" result="composite2" />
					<feComposite in="specularLighting2" in2="composite1" operator="in" result="composite3" />
					<feComposite in="specularLighting1" in2="composite1" operator="in" result="composite4" />
					<feBlend mode="multiply" in="composite4" in2="SourceGraphic" result="blend" />
					<feBlend in="composite2" in2="blend" result="blend1" />
					<feBlend in="composite3" in2="blend1" result="blend2" />
				</filter>
				<filter id="grain">
					<feTurbulence baseFrequency="0.60,0.90" result="colorNoise" />
					<feColorMatrix in="colorNoise" type="matrix"
						values=".33 .33 .33 0 0 .33 .33 .33 0 0 .33 .33 .33 0 0 0 0 0 1 0" />
					<feComposite operator="in" in2="SourceGraphic" result="monoNoise" />
					<feBlend in="SourceGraphic" in2="monoNoise" mode="multiply" />
				</filter>
				<filter id="fluffy" x="0%" y="0%" width="100%" height="100%">
					<feTurbulence type="fractalNoise" baseFrequency="0.04" result="fluffyNoise" numOctaves="5" />
					<feColorMatrix in="fluffyNoise" type="matrix"
						values=".33 .33 .33 0 0 .33 .33 .33 0 0 .33 .33 .33 0 0 0 0 0 1 0" />
					<feComposite operator="in" in2="SourceGraphic" result="monoFluffyNoise" />
					<feBlend in="SourceGraphic" in2="monoFluffyNoise" mode="screen" />
				</filter>
			</defs>
		</svg>
	</div>
</template>

<script setup lang="ts">
import { Ref, inject, onMounted, onUnmounted, ref, watch, nextTick } from 'vue';
import "@/assets/font/DouyuFont.woff2";
import { useRouterPush } from '@/hooks/common/router';
import { ChevronBackOutline, } from '@vicons/ionicons5'
import * as Cesium from 'cesium';
import HeatMapLayer from '@cesium-extends/heat';
import * as echarts from 'echarts';
import { MouthPositionHandler, cesiumConfig, josiahProvider, labelProvider, position } from '@/config/cesium_config';
import {
	fetchIndexOrg, heatMap, flightSize, fileSize, flightMileageGoup, allDeviceInfo, fetchDataOverview, fetchFlyHistoryList, fetchNoFlyList
} from '@/service/api';
import { useRoute } from 'vue-router';
import { darkTheme } from 'naive-ui';
import back from "@/assets/imgs/back.jpg"; // 背景色
import panel from "@/assets/imgs/panel.png"; // 面板背景框
import title from "@/assets/imgs/title.png"; // 小标题背景框
import { useTaskStore } from '@/store/modules/task';
import { NSteps, NStep } from 'naive-ui';
import { useAuthStore } from '@/store/modules/auth';
import { useDeviceStore } from '@/store/modules/device';
const { routerBack } = useRouterPush();
const authStore = useAuthStore();
const show = ref(false);
const itemCount = ref(0);
const timeOptions = [
	{ label: '全部', value: 0 },
	{ label: '本周', value: 1 },
	{ label: '上周', value: 2 },
	{ label: '本月', value: 3 },
	{ label: '上个月', value: 4 },
	{ label: '近30天', value: 5 },
	{ label: '近六个月', value: 6 }
];
const selectedTimeRange = ref(0); // 0:全部, 1:本月, 3:近三个月, 6:近六个月
// 时间范围选择处理函数
const selectTimeRange = (month: number) => {
	selectedTimeRange.value = month;
	currentPage.value = 1; // 重置页码
	hasMore.value = true; // 重置是否有更多数据
	initDataSummaryChart(month);
	// getSummaryChartM(month)
	let stime = '';
	let etime = '';
	let type = month + '';
	initMachineTypeChart(stime, etime, type)
	initTaskListChart(month)
};
// 新增时间相关逻辑
const currentTime = ref('');
let timer: ReturnType<typeof setInterval>;
function formatTime(date: Date): string {
	return date.toISOString()
		.split('T')[0] + ' ' +
		date.toISOString().split('T')[1].split('.')[0];
}
function updateTime() {
	currentTime.value = formatTime(new Date());
}
//--------------
// cesium viewer 初始化
let viewer: Cesium.Viewer;
const deviceStore = useDeviceStore();
const tianditupUrl = 'https://t{s}.tianditu.gov.cn/DataServer?T=vec_w&x={x}&y={y}&l={z}&tk=2ebde960216378f6c28d8da79c67304a';
const tiandituLabelMapUrl = 'https://t{s}.tianditu.gov.cn/DataServer?T=cva_w&x={x}&y={y}&l={z}&tk=2ebde960216378f6c28d8da79c67304a';
// 服务负载子域
var subdomains = ['0', '1', '2', '3', '4', '5', '6', '7'];
// 卫星影像图层
const josiah = new Cesium.UrlTemplateImageryProvider({
	url: tianditupUrl,
	subdomains: subdomains,
	maximumLevel: 18
});
// 道路图层
const labelProviders = new Cesium.UrlTemplateImageryProvider({
	url: tiandituLabelMapUrl,
	subdomains: subdomains,
});
//----------
// 标题和组织信息
const indexOrg = ref<Api.Home.IndexOrg>({
	aiCount: 0,
	dockCount: 0,
	flightCount: 0,
	flightLineCount: 0,
	onlineDevice: 0,
	resultCount: 0,
	userCount: 0,
	droneCount: 0
});
const getIndexOrg = async () => {
	const res = await fetchIndexOrg();
	indexOrg.value = res.data;
	// console.log("system ~ baseURL:", res)
}
//--------------
// 热力图
const getHeatMap = async () => {
	try {
		// 调用API获取热力图数据
		const res = await heatMap();
		const data = res.data;

		// 将API数据转换为cesium-heatmap需要的格式
		const heatmapData = {
			// 转换数据点格式
			data: data.map((item: { x: any; y: any; value: any; }) => ({
				pos: [Number(item.y), Number(item.x)],
				value: item.value   // 热力值
			}))
		};
		// // 计算边界
		const west = Math.min(...data.map((d: { lng: any; }) => d.lng));
		const east = Math.max(...data.map((d: { lng: any; }) => d.lng));
		const south = Math.min(...data.map((d: { lat: any; }) => d.lat));
		const north = Math.max(...data.map((d: { lat: any; }) => d.lat));

		const mapContainer = document.getElementById('cesiumViewer');
		if (mapContainer) {	// 创建热力图实例
			console.log('----------------', heatmapData, mapContainer.clientWidth, mapContainer.clientHeight)
			const heatmap = new HeatMapLayer({
				viewer,
				heatStyle: {
					radius: 5,
					maxOpacity: 0.8,
					minOpacity: 0.2,
					blur: 0.75
				},
				canvasSize: 5000,
				bbox: [-180, 180, -90, 90], // 全球范围
				data: heatmapData.data,
			});
			// 显示热力图
			heatmap.show = true;
		}

		// 可以保存heatmap实例以便后续操作
		// this.heatmapInstance = heatmap;

		console.log("热力图加载成功");

	} catch (error) {
		console.error("加载热力图失败:", error);
	}
};
// 任务信息-----------
const stepsContainer = ref<HTMLElement | null>(null);
const currentStep = ref(0);
const currentStatus = ref<'process' | 'finish' | 'error' | 'wait'>('process');
const scrollInterval = ref<ReturnType<typeof setInterval>>();
const getStepStatus = (status: string) => {
	const statusMap: Record<string, 'process' | 'finish' | 'error' | 'wait'> = {
		'3': 'wait',
		'2': 'finish',
		'0': 'process',
		'1': 'error'
	};
	return statusMap[status] || 'wait';
};
const flyHistoryData = ref<Api.AirLine.AirLineItems[]>([]);
const currentPage = ref(1); // 当前页码
const pageSize = ref(10); // 每页条数
const loading = ref(false); // 是否正在加载
const hasMore = ref(true); // 是否还有更多数据
//--------------
// 设备状态类型
interface DeviceInfo {
	id: string;
	deviceName: string; //机场
	droneName: string; //无人机
	deviceStatus: string; // 0:离线, 1:在线空闲, 2:在线工作中, 3:在线故障
	droneStatus: string; // 0:离线, 1:在线空闲, 2:在线工作中, 3:在线故障
	droneCapacityPercent: number;
	droneType: string;
	taskName: string;
}
const devicesContainer = ref<HTMLElement | null>(null);
const deviceList = ref<DeviceInfo[]>([]);
const loadingDevices = ref(false);
const hasMoreDevices = ref(true);
const currentDevicePage = ref(1);
const devicePageSize = ref(10);
// 滚动
const deviceScrollInterval = ref<ReturnType<typeof setInterval>>();
const currentDeviceScroll = ref(0);
const userHasScrolledDevices = ref(false);
//----------------
// 初始化地图
const drawLayer = ref<Cesium.CustomDataSource | null>(null);
const initCesium = () => {
	viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
	(viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
	viewer.scene.globe.depthTestAgainstTerrain = true;  //启用深度测试
	viewer.scene.globe.baseColor = Cesium.Color.BLUE; //修改地图背景
	viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
	// 修改图层
	viewer.imageryLayers.removeAll();
	viewer.imageryLayers.addImageryProvider(josiah);
	viewer.imageryLayers.addImageryProvider(labelProviders);
	// 设置最大和最小缩放
	viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
	viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

	// 设置相机位置为当前定位点
	const longitude = Number(authStore.sysInfo.webConfig.centerLongitude);
	const latitude = Number(authStore.sysInfo.webConfig.centerLatitude);
	const lastPointCartesian = Cesium.Cartesian3.fromDegrees(longitude, latitude, 5000);
	// 设置视图以显示多段线
	viewer.camera.flyTo({
		destination: lastPointCartesian,
		orientation: {
			heading: Cesium.Math.toRadians(0),
			pitch: Cesium.Math.toRadians(0), // 调整 pitch 值来改变视角的远近
			roll: 0.0
		},
		duration: 1.0, // 飞行时间，单位为秒
		complete: () => {
			// 可以在这里添加飞行完成后的操作
		}
	});

	drawLayer.value = new Cesium.CustomDataSource('noFlyAreaLayer');
	viewer.dataSources.add(drawLayer.value);
	// 加载禁飞区JSON文件并绘制禁飞区-默认
	loadJSONAndDrawNoFlyZone();
	// 加载自定义禁飞区
	fetchAllNoFlyList()
}
const noFlyList = ref<Api.Device.NoflyInfo[]>([]); // 禁飞区列表
// 获取禁飞区
const fetchAllNoFlyList = async () => {
	const { data } = await fetchNoFlyList();
	if (data) {
		noFlyList.value = data.rows;
		nextTick(renderNoFlyZones);
		console.log("noFlyList.value", noFlyList.value)
	}
}
const renderNoFlyZones = () => {
	// 清空现有图形
	drawLayer.value?.entities.removeAll();

	noFlyList.value.forEach((zone, index) => {
		if (zone.geoJson.type === 'circle') {
			console.log("index", index)
			drawCircleZone(zone);
		} else if (zone.geoJson.type === 'polygon') {
			drawPolygonZone(zone);
		}
	});
};
const drawCircleZone = (zone: Api.Device.NoflyInfo) => {
	drawLayer.value?.entities.add({
		position: Cesium.Cartesian3.fromDegrees(zone.centerGeoJson.coordinates[0], zone.centerGeoJson.coordinates[1], 0),
		ellipse: {
			semiMajorAxis: zone.radius,
			semiMinorAxis: zone.radius,
			material: Cesium.Color.RED.withAlpha(0.3),
			outline: true,
			outlineColor: Cesium.Color.RED,
			outlineWidth: 2,
			heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
		}
	});
};

// 多边形绘制
const drawPolygonZone = (zone: Api.Device.NoflyInfo) => {
	console.log("coordinates", zone.geoJson.geometry.coordinates)
	const hierarchy = new Cesium.PolygonHierarchy(
		zone.geoJson.geometry.coordinates.map((pos) =>
			Cesium.Cartesian3.fromDegrees(pos[0], pos[1], 0)
		)
	);
	drawLayer.value?.entities.add({
		polygon: {
			hierarchy: hierarchy,
			material: Cesium.Color.RED.withAlpha(0.3),
			outline: true,
			outlineColor: Cesium.Color.WHITE,
			outlineWidth: 2,
			heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
		}
	});
};
// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
	try {
		const response = await fetch('/defaultNoFlyZone.json');
		const data = await response.json();
		// 绘制
		addPolygons(data.features);
	} catch (error) {
		console.error('Error loading JSON data:', error);
	}
};

// 绘制禁飞区
function addPolygons(features: any[]) {
	features.forEach(feature => {
		const polygon = feature.geometry.coordinates[0];
		const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
		viewer.entities.add({
			polygon: {
				hierarchy: positions,
				material: Cesium.Color.fromCssColorString('red').withAlpha(0.35),
			}
		});
	});
};
// 媒体数据图表 - 横向条形图
const initMediaDataChart = async () => {
	const chartDom = document.getElementById('mediaDataChart');
	if (!chartDom) return;
	const chartData = await fileSize()
	console.log('数据汇总', chartData.data)
	const chart = echarts.init(chartDom);
	const option = {
		backgroundColor: 'transparent',
		tooltip: {
			trigger: 'axis',
			axisPointer: {
				type: 'shadow'
			}
		},
		grid: {
			left: '15%',
			right: '10%',
			bottom: '15%',
			top: '20%'
		},
		xAxis: {
			type: 'value',
			axisLine: {
				lineStyle: {
					color: 'rgba(255,255,255,0.5)'
				}
			},
			axisLabel: {
				color: '#fff'
			},
			splitLine: {
				lineStyle: {
					color: 'rgba(255,255,255,0.1)'
				}
			}
		},
		yAxis: {
			type: 'category',
			data: ['图片', '视频', '全景', '三维模型'],
			axisLine: {
				lineStyle: {
					color: 'rgba(255,255,255,0.5)'
				}
			},
			axisLabel: {
				color: '#fff'
			}
		},
		series: [
			{
				name: '数量',
				type: 'bar',
				data: [
					{ value: 0, itemStyle: { color: '#4ECB73' } },
					{ value: 0, itemStyle: { color: '#3498DB' } },
					{ value: 0, itemStyle: { color: '#9B59B6' } },
					{ value: 0, itemStyle: { color: '#E74C3C' } }
				],
				label: {
					show: true,
					position: 'right',
					formatter: '{c}',
					color: '#fff'
				},
				barWidth: '40%'
			}
		]
	};
	for (let index = 0; index < chartData.data.length; index++) {
		option.series[0].data[index].value = chartData.data[index].size
	}
	chart.setOption(option);
	window.addEventListener('resize', function () {
		chart.resize();
	});
};

// 飞行记录
const initTaskListChart = async (dateType: number, loadMore = false) => {
	if (loading.value || !hasMore.value) return;

	loading.value = true;
	// show.value = true;

	try {
		const json: Api.Task.TaskScreens = {
			deptId: undefined,
			droneName: '',
			dockName: '',
			flightJobName: '',
			createName: undefined,
			flightId: undefined,
			flightJobType: undefined,
			startDate: '',
			endDate: '',
			pageNum: currentPage.value,
			pageSize: pageSize.value,
			dateType: dateType
		};

		const { data, error } = await fetchFlyHistoryList(json);

		if (!error) {
			if (loadMore) {
				// 加载更多时追加数据
				flyHistoryData.value = [...flyHistoryData.value, ...data.rows];
			} else {
				// 初次加载或时间范围改变时重置数据
				flyHistoryData.value = data.rows;
			}

			itemCount.value = data.total;
			hasMore.value = data.rows.length >= pageSize.value;
			currentPage.value += 1;
		}
	} catch (e) {
		console.error("加载飞行记录失败:", e);
	} finally {
		loading.value = false;
		// show.value = false;
	}
};
// 机型占比图表 - 双层饼图
const initMachineTypeChart = async (stime: string, etime: string, type: string) => {
	const chartDom = document.getElementById('machineTypeChart');
	if (!chartDom) return;
	const res = await fetchDataOverview({ stime, etime, type });
	DataChart.value = res.data;
	console.log('饼图', DataChart.value)
	const chart = echarts.init(chartDom);
	const option = {
		backgroundColor: 'transparent',
		tooltip: {
			trigger: 'item',
			formatter: '{a}<br/>{b}: {c} ({d}%)'
		},
		legend: {
			orient: 'vertical',
			right: 10,
			top: 'top',
			textStyle: {
				color: '#fff',
				fontSize: 12
			},
			data: ['']
		},
		series: [
			{
				name: '飞行总次数',
				type: 'pie',
				radius: [0, '30%'],
				label: {
					position: 'inner',
					color: '#fff'
				},
				labelLine: {
					show: false
				},
				data: [
					{ value: 40, name: '固定翼' },
					{ value: 30, name: '多旋翼' },
					{ value: 20, name: '直升机' },
					{ value: 10, name: '复合翼' }
				]
			},
			{
				name: '飞行总里程数',
				type: 'pie',
				radius: ['40%', '60%'],
				label: {
					color: '#fff',
					formatter: '{b}'
				},
				data: [
					{ value: 35, name: '大疆 M300' },
					{ value: 25, name: '大疆 M600' },
					{ value: 15, name: '其他' }
				]
			}
		]
	};
	let legendList = []
	let seriesList1 = []
	let seriesList2 = []
	if (DataChart.value) {
		for (let index = 0; index < DataChart.value.flightCount.length; index++) {
			legendList.push(DataChart.value.flightCount[index].nick_name);
			seriesList1.push({
				name: DataChart.value.flightCount[index].nick_name,
				value: DataChart.value.flightCount[index].flight_count,
			})
		}
		for (let index = 0; index < DataChart.value.flightMileage.length; index++) {
			seriesList2.push({
				name: DataChart.value.flightMileage[index].nickname,
				value: DataChart.value.flightMileage[index].mileage,
			})
		}
		option.legend.data = legendList
		option.series[0].data = seriesList1
		option.series[1].data = seriesList2
	}
	chart.setOption(option);
	chart.resize();
	window.addEventListener('resize', function () {
		chart.resize();
	});
};
interface chart {
	size: number,
	yymm: string
}
interface chartM {
	mileage: number,
	yymm: string
}
interface DataChartItem {
	drone_sn: string,
	flight_count: number,
	nick_name: string
}
interface DataChartItem1 {
	drone_sn: string,
	mileage: number,
	nickname: string
}
interface DataChart {
	flightCount: Array<DataChartItem>,
	flightTime: Array<DataChartItem>,
	flightMileage: Array<DataChartItem1>
}
let DataChart = ref<DataChart>()
let SummaryChart = ref<chart[]>()
let SummaryChartM = ref<chartM[]>()
// // 数据汇总
// async function getDataChart(stime: string, etime: string, type: string) {
// 	const res = await fetchDataOverview({ stime, etime, type });
// 	DataChart.value = res.data;
// 	console.log('饼图', DataChart.value)
// }
// //飞行数据-次数
// const getSummaryChart = async (selectedTimeRange: number) => {
// 	const res = await flightSize(selectedTimeRange, '', '');
// 	SummaryChart.value = res.data;
// 	console.log('飞行数据', SummaryChart.value)
// }
// //飞行数据-里程
// const getSummaryChartM = async (selectedTimeRange: number) => {
// 	const res = await flightMileageGoup(selectedTimeRange, '', '');
// 	SummaryChartM.value = res.data;
// 	console.log('飞行数据-里程', SummaryChartM.value)
// }
// 飞行数据图表 - 柱状折线混合图
const initDataSummaryChart = async (selectedTimeRange: number) => {
	const chartDom = document.getElementById('dataSummaryChart');
	if (!chartDom) return;
	const chart = echarts.init(chartDom);
	const res = await flightSize(selectedTimeRange, '', '');
	SummaryChart.value = res.data;
	// console.log('飞行数据', SummaryChart.value)
	const res1 = await flightMileageGoup(selectedTimeRange, '', '');
	SummaryChartM.value = res1.data;
	// console.log('飞行数据-里程', SummaryChartM.value)
	const option = {
		backgroundColor: 'transparent',
		tooltip: {
			trigger: 'axis'
		},
		legend: {
			data: ['飞行次数', '飞行里程'],
			textStyle: {
				color: '#fff'
			},
			right: 10,
			top: 0
		},
		grid: {
			left: '15%',
			right: '15%',
			bottom: '15%',
			top: '25%'
		},
		xAxis: {
			type: 'category',
			data: [''],
			axisLine: {
				lineStyle: {
					color: 'rgba(255,255,255,0.5)'
				}
			},
			axisLabel: {
				color: '#fff'
			}
		},
		yAxis: [
			{
				type: 'value',
				name: '次数',
				axisLine: {
					lineStyle: {
						color: 'rgba(255,255,255,0.5)'
					}
				},
				axisLabel: {
					color: '#fff'
				},
				splitLine: {
					lineStyle: {
						color: 'rgba(255,255,255,0.1)'
					}
				}
			},
			{
				type: 'value',
				name: 'M',
				axisLine: {
					lineStyle: {
						color: 'rgba(255,255,255,0.5)'
					}
				},
				axisLabel: {
					color: '#fff'
				},
				splitLine: {
					show: false
				}
			}
		],
		series: [
			{
				name: '飞行次数',
				type: 'bar',
				data: [0],
				itemStyle: {
					color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
						{ offset: 0, color: '#4ECB73' },
						{ offset: 1, color: '#2EC7C9' }
					])
				}
			},
			{
				name: '飞行里程',
				type: 'line',
				yAxisIndex: 1,
				data: [0],
				symbol: 'circle',
				symbolSize: 8,
				itemStyle: {
					color: '#FBD437'
				},
				lineStyle: {
					width: 3
				}
			}
		]
	};
	let yymmList = []
	let sizeList = []
	let mileageList = []
	if (SummaryChart.value) {
		for (let index = 0; index < SummaryChart.value.length; index++) {
			yymmList.push(SummaryChart.value[index].yymm)
			sizeList.push(SummaryChart.value[index].size)
			option.xAxis.data = yymmList
			option.series[0].data = sizeList
		}
	}
	if (SummaryChartM.value) {
		for (let index = 0; index < SummaryChartM.value.length; index++) {
			mileageList.push(SummaryChartM.value[index].mileage)
			option.series[1].data = mileageList
		}
	}
	console.log('数据汇总', SummaryChart.value, SummaryChartM.value)
	chart.setOption(option);
	chart.resize();
	window.addEventListener('resize', function () {
		chart.resize();
	});
};
function fullscreenCheck() {
	if (!document.fullscreenElement) {  // current working methods
		if (document.documentElement.requestFullscreen) {
			document.documentElement.requestFullscreen();
		}
	} else {
		if (document.exitFullscreen) {
			document.exitFullscreen();
		}
	}
}
const startAutoScroll = () => {
	if (!stepsContainer.value) return;
	const container = stepsContainer.value;

	// 只有在没有手动滚动且数据不加载时才自动滚动
	if (!loading.value && !userHasScrolled.value) {
		const stepHeight = 80;
		let currentScroll = container.scrollTop;
		const maxScroll = container.scrollHeight - container.clientHeight;

		if (currentScroll >= maxScroll) {
			currentScroll = 0;
			container.scrollTo({ top: 0, behavior: 'smooth' });
		} else {
			currentScroll += stepHeight;
			container.scrollTo({ top: currentScroll, behavior: 'smooth' });
		}

		currentStep.value = Math.floor(currentScroll / stepHeight);
	}

	// 设置下一次滚动
	scrollInterval.value = setTimeout(startAutoScroll, 3000);
};
// 设备信息
const initDevice = async (loadMore = false) => {
	if (loadingDevices.value || !hasMoreDevices.value) return;
	loadingDevices.value = true;
	try {
		const { data, error } = await allDeviceInfo();

		if (!error) {
			if (loadMore) {
				// 加载更多时追加数据
				deviceList.value = [...deviceList.value, ...data];
			} else {
				// 初次加载时重置数据
				deviceList.value = data;
			}
			// 模拟是否有更多数据
			// hasMoreDevices.value = deviceList.value.length < 20;
			// currentDevicePage.value += 1;
		}
	} catch (e) {
		console.error("加载设备列表失败:", e);
	} finally {
		loadingDevices.value = false;
	}
};
// 设备模块滚动
const startDeviceAutoScroll = () => {
	if (!devicesContainer.value) return;
	const container = devicesContainer.value;

	// 只有在没有手动滚动且数据不加载时才自动滚动
	if (!loadingDevices.value && !userHasScrolledDevices.value) {
		const stepHeight = 60; // 每个设备项的大致高度
		let scroll = currentDeviceScroll.value;
		const maxScroll = container.scrollHeight - container.clientHeight;

		if (scroll >= maxScroll) {
			scroll = 0;
			container.scrollTo({ top: 0, behavior: 'smooth' });
		} else {
			scroll += stepHeight;
			container.scrollTo({ top: scroll, behavior: 'smooth' });
		}

		currentDeviceScroll.value = scroll;
	}

	// 设置下一次滚动
	deviceScrollInterval.value = setTimeout(startDeviceAutoScroll, 3000);
};
// 获取状态样式类
const getStatusClass = (status: string) => {
	const statusClasses = [
		'status-offline',    // 离线
		'status-idle',       // 在线空闲
		'status-working',    // 在线工作中
		'status-error'       // 在线故障
	];
	let statusFlag = 0
	if (status == '离线') {
		statusFlag = 0
	} else if (status == '空闲中') {
		statusFlag = 1
	} else {
		statusFlag = 2
	}
	return statusClasses[statusFlag] || 'status-offline';
};

// 获取状态文本
const getStatusText = (status: number) => {
	const statusTexts = ['离线', '空闲中', '作业中', '故障'];
	return statusTexts[status] || '未知';
};
// 获取电池图标颜色
const getBatteryColor = (percent: number) => {
	if (percent >= 60) return '#4ECB73'; // 绿色 - 电量充足
	if (percent >= 30) return '#FBD437'; // 黄色 - 电量中等
	return '#E74C3C'; // 红色 - 电量不足
};

// 获取电池文字颜色类名
const getBatteryTextColor = (percent: number) => {
	if (percent >= 60) return 'text-green-400'; // 绿色文字
	if (percent >= 30) return 'text-yellow-400'; // 黄色文字
	return 'text-red-400'; // 红色文字
};
// 设备列表滚动处理
const handleDevicesScroll = () => {
	const container = devicesContainer.value;
	if (!container) return;

	// 检查是否滚动到底部
	const { scrollTop, scrollHeight, clientHeight } = container;
	const isBottom = scrollTop + clientHeight >= scrollHeight - 50;

	if (isBottom && !loadingDevices.value && hasMoreDevices.value) {
		initDevice(true);
	}
};
// 添加用户滚动标志
const userHasScrolled = ref(false);
const handleScroll = () => {
	const container = stepsContainer.value;
	if (!container) return;

	// 检查是否滚动到底部
	const { scrollTop, scrollHeight, clientHeight } = container;
	const isBottom = scrollTop + clientHeight >= scrollHeight - 50; // 提前50px触发加载

	if (isBottom && !loading.value && hasMore.value) {
		initTaskListChart(selectedTimeRange.value, true);
	}
};
onMounted(() => {
	// 初始化地图
	initCesium();
	updateTime(); // 初始加载时显示当前时间
	timer = setInterval(updateTime, 1000); // 每秒更新一次
	// 添加滚动监听
	const container = stepsContainer.value;
	if (container) {
		container.addEventListener('scroll', handleScroll);
	}
	// 初始化图表
	nextTick(() => {
		initMachineTypeChart('', '', '0');
		initDataSummaryChart(0);
		initMediaDataChart();
		initTaskListChart(0);
		setTimeout(startAutoScroll, 1000);
		fullscreenCheck()
		initDevice();//未完成
		setTimeout(startDeviceAutoScroll, 1000); // 启动设备自动滚动
		// 添加设备列表滚动监听
		const container = devicesContainer.value;
		if (container) {
			container.addEventListener('scroll', handleDevicesScroll);
		}
		getIndexOrg()
		getHeatMap()
	});
});

onUnmounted(() => {
	clearInterval(timer); // 组件卸载时清除定时器
	// 移除滚动监听
	const container = stepsContainer.value;
	if (container) {
		container.removeEventListener('scroll', handleScroll);
	}
	// 移除设备列表滚动监听
	const containers = devicesContainer.value;
	if (containers) {
		containers.removeEventListener('scroll', handleDevicesScroll);
	}
	if (deviceScrollInterval.value) {
		clearTimeout(deviceScrollInterval.value);
	}
	if (scrollInterval.value) {
		clearTimeout(scrollInterval.value);
	}
});

function handlePageBack() {
	routerBack();
}
</script>

<style scoped>
/* 背景图片样式 */
.bg-image {
	background-image: url(@/assets/imgs/backgroundImg.jpg);
	background-size: cover;
	background-position: center;
	background-repeat: no-repeat;
	opacity: 0.7;
}

/* 原有样式调整（移除渐变背景） */
.bg-gradient-to-b {
	background: transparent;
}

/* 时间显示样式（根据实际情况调整） */
.text-4 {
	font-size: 12px;
	font-family: 'DouyuFont', sans-serif;
}


/* 面板容器样式 */
.panel-container {
	display: flex;
	flex-direction: column;
	margin-bottom: 15px;
	/* 面板之间的间距 */
}

/* 标题栏样式 */
.title-bar {
	display: flex;
	align-items: center;
	margin-bottom: 0px;
	/* 标题和面板之间的间距 */
}

.map-filter {
	filter: url('#x-rays') brightness(1.5) contrast(2.4) grayscale(0.22);
}

/* 保持原有样式，只添加必要的图表容器样式 */
#machineTypeChart,
#dataSummaryChart {
	position: relative;
	z-index: 10;
	width: calc(100% - 20px);
	height: calc(100% - 40px);
}

/* 确保面板内容不被背景图覆盖 */
.relative {
	position: relative;
}

/* 隐藏滚动条但保持滚动功能 */
.no-scrollbar {
	scrollbar-width: none;
	/* Firefox */
	-ms-overflow-style: none;
	/* IE/Edge */
}

.no-scrollbar::-webkit-scrollbar {
	display: none;
	/* Chrome/Safari/Opera */
}

/* 步骤条样式保持不变 */
:deep(.n-step) {
	margin-bottom: 10px;
}

:deep(.n-step__title) {
	color: white !important;
	font-size: 12px !important;
	font-family: 'DouyuFont', sans-serif !important;
}

:deep(.n-step__description) {
	color: rgba(255, 255, 255, 0.7) !important;
	font-size: 10px !important;
}

:deep(.n-step__indicator) {
	background-color: transparent !important;
}

:deep(.n-steps .n-step-indicator) {
	color: white !important;
}

/* 设备信息样式 */
.device-item {
	padding: 8px 0;
	border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

/* 状态指示器基础样式 */
.status-indicator {
	display: inline-block;
	padding: 2px 8px;
	border-radius: 4px;
	font-size: 10px;
	font-family: 'DouyuFont', sans-serif;
}

/* 不同状态的样式 */
.status-offline {
	background-color: rgba(155, 155, 155, 0.3);
	color: rgba(255, 255, 255, 0.7);
}

.status-idle {
	background-color: rgba(46, 199, 201, 0.3);
	color: #2EC7C9;
}

.status-working {
	background-color: rgba(78, 203, 115, 0.3);
	color: #4ECB73;
}

.status-error {
	background-color: rgba(231, 76, 60, 0.3);
	color: #E74C3C;
}

/* 设备信息图标间距 */
.device-item .n-icon {
	margin-right: 4px;
}
</style>