// 设备相关接口
import { request } from '../request';

// 获取无人机设备状态码列表
export function fetchDroneStatusList() {
  return request({ url: '/system/dict/data/type/drone_status', method: 'get' });
}

// 获取dataType状态字典
export function fetchStatusList(dataType: string) {
  return request({
    url: `/system/dict/data/type/${dataType}`,
    method: 'GET'
  });
}

// 获取设备告警信息
export function fetchDeviceHmsList(params: Api.List.Table) {
  return request({
    url: '/deviceHms/healthyMessage',
    method: 'GET',
    params
  });
}

// 获取无人机列表
export function fetchDroneList(params: Api.List.Table) {
  return request({
    url: '/device/drone/list',
    method: 'GET',
    params
  });
}

// 在线和离线电池,online:1在线，0离线
export function fetchDroneBatteryList(online: number) {
  return request({
    url: `/device/drone/batteries/${online}`,
    method: 'GET'
  });
}

// 获取机场配置信息
export function fetchDockList() {
  return request({
    url: `/device/task/list`,
    method: 'GET'
  });
}

// 修改机场配置信息
export function updateDockTaskList(data: Api.Airport.MachineNestInfo[]) {
  return request({
    url: `/device/task`,
    method: 'PUT',
    data
  });
}
// 获取空域
// 获取机场配置信息
export function fetchAllFlyArea() {
  return request({
    // url: `/noflyarea/list`,
    url: '/index/allFlyArea',
    method: 'GET'
  });
}

// 获取禁飞区列表
export function fetchNoFlyList() {
  return request({
    url: `/noflyarea/list`,
    method: 'GET'
  });
}

// 删除禁飞区
export function deleteNoFlyList(id: number) {
  return request({
    url: `/noflyarea/${id}`,
    method: 'DELETE'
  });
}

// 新增或修改禁飞区
export function saveOrUpdate(data: Api.Device.NoflyInfo) {
  return request({
    url: `/noflyarea/saveOrUpdate`,
    method: 'POST',
    data
  });
}
