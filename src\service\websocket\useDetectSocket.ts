import { ref, watchEffect, watch, Ref, reactive } from 'vue'
import useWebSocket, { SocketStatus } from './index'
// import { useAuthStore } from '@/store/modules/auth';
import { createPinia, setActivePinia, storeToRefs, getActivePinia } from 'pinia';
import { localStg } from '@/utils/storage';
import { getServiceBaseURL } from '@/utils/service';
// import { isLogin } from '@/store/modules/auth';
import { getToken } from '@/store/modules/auth/shared';
import { useAirportStore } from '@/store/modules/airport';

type ChatMessageType = string | null;

// 判断是否使用 HTTP 代理
const isHttpProxy = import.meta.env.DEV && import.meta.env.VITE_HTTP_PROXY === 'Y';
// 获取服务的基础 URL
const { baseURL , otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);
const serviceBaseURL = import.meta.env.VITE_SERVICE_BASE_URL;
const wsDetectURL = otherBaseURL.pserver;
// const { setDeviceOSD } = useAirportStore();

// 从本地存储中获取 token
const token = localStg.get('token');
// 检查用户是否已登录
const isLogin = Boolean(localStg.get('token'));

// const Authorization = `${token}`;

// WebSocket 服务的组合式 API
export default function useSocket() {
  // 确保 Pinia 被激活
  if (!getActivePinia()) {
    throw new Error('Pinia is not active. Make sure to use it in setupStore.');
  }

  const chatStore = useAirportStore(); // 创建 store 实例

  // 使用 useWebSocket 组合式函数初始化第一个 WebSocket 连接
  // const { status, message, error, connect, disconnect, send } = useWebSocket({
  //   url: serviceBaseURL + '/api/v1/ws',
  //   heartBeatData: 'heart data',
  // });

  // 新增：使用 useWebSocket 组合式函数初始化第二个 WebSocket 连接
  const { status, message, error, connect, disconnect, send } = useWebSocket({
    url: wsDetectURL, // 使用新的 WebSocket URL
    heartBeatData: 'heart data for detect',
  });

  // 从 Pinia 存储中获取登录状态
  // const { isLogin } = storeToRefs(authStore);
  // console.log("useSocket ~ isLogin:", isLogin)

  // 存储聊天消息
  const detectMessage = ref();
  // 存储 WebSocket 状态文本
  const socketStatusText = ref('');

  // 监听网络离线事件
  window.addEventListener('offline', function () {
    console.log('网络连接已断开');
  })

  // 监听网络在线事件
  window.addEventListener('online', function () {
    console.log('网络连接已恢复');
    // 在网络连接恢复后尝试重新连接 WebSocket
    retryConnect()
  })

  // 监视 WebSocket 状态变化
  watch(() => status.value, (newVal) => {
    console.log('WebSocket 状态:', newVal);

    if (newVal != SocketStatus.Connected) {
      console.log('WebSocket 已连接，准备监听消息...');

      socketStatusText.value = newVal;
    }
  })

  // 监视 WebSocket 消息变化
  watch(() => message.value, (newVal, oldVal) => {
    // console.log("watch ~ newVal:", newVal)
    if (newVal) {
      const parsedMessage = JSON.parse(JSON.stringify(newVal));
      detectMessage.value = parsedMessage;
      if (chatStore) {
        chatStore.updateChatMessage(parsedMessage); // 更新 Pinia store 中的消息
      }
      // console.log("watch ~ newVal:", chatMessage.value)
    }
  })

  // 当登录状态变化时自动连接或断开 WebSocket
  watchEffect(() => {
    console.log("watchEffect ~ isLogin:", isLogin)
    if (isLogin) {
      connect();
    } else {
      disconnect();
    }
  })

  // 尝试重新连接 WebSocket
  const retryConnect = () => {
    // console.log("retryConnect ~ retryConnect:", status.value !== SocketStatus.Connected)
    if (status.value !== SocketStatus.Connected) {
      connect();
    }
  }

  // 修改 send 方法
  const sendMessage = (data: any) => {
    if (status.value !== SocketStatus.Connected) {
      console.warn('WebSocket未连接，无法发送消息');
      return false;
    }

    try {
      // 如果传入的数据不是字符串，转换为JSON字符串
      const messageData = typeof data === 'string' ? data : JSON.stringify(data);
      
      if (send) {
        send(messageData);
        return true;
      }
      return false;
    } catch (err) {
      console.error('发送消息失败:', err);
      return false;
    }
  }

  return {
    socketStatusText,
    message,
    retryConnect,
    sendMessage,
    detectMessage,
  }
}