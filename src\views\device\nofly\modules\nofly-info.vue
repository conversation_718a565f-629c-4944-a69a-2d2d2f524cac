<template>
  <div class="absolute zindex-1 right-0 top-0 w-330px h-100% text-light  nofly-info-box">
    <div class="title">禁飞区</div>
    <n-input class="searchInput" v-model="searchQuery" placeholder="搜索禁飞区" clearable
      @update:value="debouncedHandleInput">
      <template #prefix>
        <n-icon :component="Search" />
      </template>
    </n-input>
    <n-list v-if="filterNoFlyList.length > 0" hoverable clickable class="list">
      <n-list-item v-for="(item, index) in filterNoFlyList" :key="item.id" class="list-item"
        @mouseover="hoveredItem = index" :class="{ 'active-item': currentAreaIndex === item.id }"
        @mouseleave="hoveredItem = null" @click="props.changeCurrentPointIndex(item.id || -1)">
        <template #suffix>
          <n-button v-if="hoveredItem === index" @click="deleteNoFlyAreaItem(item.id)">
            <template #icon>
              <n-icon>
                <TrashOutline />
              </n-icon>
            </template>
          </n-button>
        </template>
        <n-thing>
          {{ item.noFlyName }}
        </n-thing>
      </n-list-item>
    </n-list>
    <n-empty style="display: flex; align-items: center; justify-content: center; height: 70%" v-else size="large"
      description="暂无区域" />
    <div class="bottom-button">
      <n-button type="info" class="add-button" @click="props.changeModalStatus(true)">
        新建禁飞区
      </n-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, onUpdated, ref, watch } from 'vue';
import { useMessage, useDialog } from 'naive-ui'
import { TrashOutline, Search } from '@vicons/ionicons5';
import { deleteNoFlyList } from '@/service/api';
const message = useMessage();
const dialog = useDialog()
// 搜索关键字
const searchQuery = ref('');
defineOptions({
  name: 'NoFlyInfoInfo'
});

// 定义组件接受的属性
const props = defineProps<{
  noFlyList: Api.Device.NoflyInfo[];
  changeCurrentPointIndex: (index: number) => void;
  changeModalStatus: (status: boolean) => void;
  currentAreaIndex: number;
  fetchAllNoFlyList: () => void;
  changeCreate: (status: boolean) => void;
  changeIsEdit: (status: boolean) => void;
}>();
// 添加防抖 不确定传递的函数参数类型所以用any
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number) => {
  let timer: ReturnType<typeof setTimeout> | null = null;
  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn.apply(this, args), delay);
  };
};
const filterNoFlyList = ref<Api.Device.NoflyInfo[]>([]);

// 监听 noFlyList 的变化
watch(
  () => props.noFlyList,
  (newVal) => {
    filterNoFlyList.value = newVal;
  },
  { deep: true }
);
// 根据搜索关键字过滤禁飞区列表
const handleInput = (keyWord: string) => {
  filterNoFlyList.value = props.noFlyList.filter((item) => {
    return item.noFlyName.toLowerCase().includes(keyWord.toLowerCase());
  });
}
const debouncedHandleInput = debounce(handleInput, 500);
const { fetchAllNoFlyList, changeCurrentPointIndex, changeCreate, changeIsEdit } = props;
const deleteNoFlyAreaItem = async (id?: number) => {
  if (!id) return;
  dialog.warning({
    title: '是否删除禁飞区',
    positiveText: '确定',
    negativeText: '取消',
    maskClosable: false,
    onPositiveClick: async () => {
      const { error } = await deleteNoFlyList(id);
      if (!error) {
        message.success('删除成功');
        fetchAllNoFlyList();
        changeCurrentPointIndex(-1);
        changeCreate(false)
        changeIsEdit(false)
        return true;
      }
    }
  })

}
const hoveredItem = ref<number | null>(null);

defineExpose({
});
onMounted(() => {
})

onUpdated(() => {
})

</script>

<style scoped lang="scss">
.nofly-info-box {
  height: 100%;
  background: #111;
  z-index: 1;
}

.title {
  font-size: 20px;
  font-weight: bold;
  color: #fff;
  height: 50px;
  line-height: 50px;
  text-align: center;
  background: #111;
  border-bottom: 2px solid #ccc;
  font-family: 'Courier New', Courier, monospace;
  margin: 0 10px;
}


.list-item {
  border-radius: 5px;
  margin: 0 4px 10px 4px;
  background: #444;
  padding: 5px 10px !important;
  height: 50px;

  &.active-item {
    background-color: #2d8cf0 !important;
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.2);
  }
}

.list {
  // margin: 10px;
  padding: 10px;
  background: #111;
}

.bottom-button {
  width: 100%;
  height: 50px;
  line-height: 40px;
  text-align: center;
  background: #111;
  position: absolute;
  bottom: 20px;
  border-top: 1px solid #ccc;
  border-radius: 5px;
  padding: 10px 0;
}

.add-button {
  width: 80%;
  font-size: 16px;
  height: 40px;
  color: #fff;
  font-weight: bolder;
}

.searchInput {
  width: 85%;
  margin: 10px 24px;
  background: #111;
  border-radius: 5px;
  border: 1px solid #ccc;
}
</style>
