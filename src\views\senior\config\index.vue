<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useMessage } from 'naive-ui';
import { checkAi, fetchAIList, fetchAllAIConfig, updateAiConfig, updateAiItem } from '@/service/api/ai';

const message = useMessage();

const allAIWatch = ref(false);
const isupodate = ref(0);

const AIConfig = ref<Api.AI.AIConfig>({});

const AIList = ref<Api.AI.AIItem[]>([]);

// 计算已开启的AI算法开关数量
const activeSwitchCount = computed(() => {
  return AIList.value.filter(item => item.status).length;
});

// 处理AI算法开关更新事件
const handleSwitchUpdate = async (index: number, value: boolean) => {
  if (value) {
    allAIWatch.value = true;
    const aiCode = AIList.value[index].code;
    if (!aiCode) {
      message.error('AI代码不能为空');
      return;
    }

    // 等待异步结果并赋值
    isupodate.value = await checkedAi(aiCode);
    if (isupodate.value !== 0) {
      // allAIWatch.value = false;
      return;
    }
  }
  if (!value && activeSwitchCount.value <= 1) {
    allAIWatch.value = false;
  }
  if (!value) {
    AIList.value[index].aiRetest = false;
  }

  // 更新开关状态
  AIList.value[index].status = value;
};

// 一键关闭所有AI算法开关
const toggleAllSwitches = async (val: boolean) => {
  if (!val) {
    AIList.value.forEach(item => {
      item.status = false;
      item.aiRetest = false;
    });
    allAIWatch.value = false;
  } else {
    allAIWatch.value = true;
    AIList.value.forEach(item => {
      item.status = true;
      item.aiRetest = true;
    });
  }
  saveData();
  // 刷新当前页面数据
  // await Promise.all([getAIList()]);
};

async function saveData() {
  await saveAiConfig();
  await saveAiItem();
  await getAIList();
}

async function saveAiConfig() {
  AIConfig.value.status = allAIWatch.value ? 1 : 0;
  await updateAiConfig(AIConfig.value);
}

async function checkedAi(code: string) {
  const { error } = await checkAi(code);
  if (!error) {
    return 0;
  }
  return 1;
}

async function saveAiItem() {
  const { error } = await updateAiItem(AIList.value);
  if (!error) {
    message.success('操作成功');
  }
}

// 查询全部AI识别算法配置
async function getAllAIConfig() {
  const { data } = await fetchAllAIConfig();
  // allAIWatch.value = Boolean(data.status);
  AIConfig.value = data;
}

async function getAIList() {
  const { data } = await fetchAIList();
  AIList.value = data.rows;
  AIList.value.forEach(item => {
    if (item.status) {
      allAIWatch.value = true;
    }
  });
}

onMounted(() => {
  getAllAIConfig();
  getAIList();
});
</script>

<template>
  <div>
    <NText class="font-size-4.5 font-bold">开启AI识别&nbsp;</NText>
    <NSwitch :value="allAIWatch" @update:value="val => toggleAllSwitches(val)" />
  </div>
  <NText class="">更改AI识别选项后，需重启飞行器使其生效。</NText>
  <!-- <n-text class="line-height-10">最多可同时开启3种AI识别；更改AI识别选项后，需重启飞行器使其生效。 </n-text> -->
  <NGrid x-gap="12" :cols="5" y-gap="24">
    <NGi v-for="(item, index) in AIList" :key="index">
      <NCard hoverable>
        <template #cover>
          <div class="border-b-1px border-dark-100 pl-20px font-size-4.5 line-height-12">
            <NText>{{ item.name }}</NText>
          </div>
          <div class="p-20px">
            <NForm ref="formRef" :model="item" label-placement="left" label-width="auto" label-align="left">
              <NFormItemGi :span="12" label="开启识别" path="switchValue">
                <NSwitch :value="Boolean(item.status)" @update:value="val => handleSwitchUpdate(index, val)" />
              </NFormItemGi>
              <NFormItemGi :span="12" label="识别后动作" path="checkboxGroupValue">
                <NCheckbox :checked="Boolean(item.aiRetest)" @update:checked="val => (item.aiRetest = val)">
                  持续监控
                </NCheckbox>
              </NFormItemGi>
            </NForm>
          </div>
        </template>
      </NCard>
    </NGi>
    <!-- <NGi :span="5" :offset="4" class="text-left"> -->
    <!-- </NGi> -->
  </NGrid>
  <NButton class="mt-4" type="info" @click="saveData()">保 存</NButton>
</template>

<style></style>
