<template>
	<NCard title="数据概览" :bordered="false">
		<div>
			<NGrid cols="24" :x-gap="gap" :y-gap="20" responsive="screen" item-responsive>
				<!-- 日期段选择 -->
				<NGi span="6">
					<n-date-picker v-model:value="dayRange" @confirm="onDayRangeConfirm" type="daterange" clearable
						:disabled="dataPickerValue !== '6'" />
				</NGi>
				<!-- 单选 -->
				<NGi span="16">
					<n-space vertical>
						<n-radio-group v-model:value="dataPickerValue" name="radiobuttongroup">
							<n-radio-button v-for="item in dataPicker" :key="item.value" :value="item.value"
								:label="item.label" />
						</n-radio-group>
					</n-space>
				</NGi>

				<!-- 饼图 -->
				<NGi span="8">
					<n-card title="各飞行器飞行次数" class="bg-dark-600 h-80" size="small">
						<div ref="domRef" class="h-270px overflow-hidden"></div>
					</n-card>
				</NGi>

				<!-- 飞手排名列表 -->
				<NGi span="8">
					<n-card title="各飞手飞行数据" class="bg-dark-600 h-80" size="small">
						<n-scrollbar style="height: 250px;">
							<n-list v-if="dataOverview.flightTime.length" :show-divider="false" class="bg-dark-600">
								<n-list-item v-for="(item, index) in dataOverview.flightTime" :key="index">
									<template #prefix>
										<SvgIcon :icon="`mdi:numeric-${index + 1}-box`" class="text-icon-large" />
									</template>
									{{ item.nick_name }}
									<template #suffix>
										<span class="w-[250px] text-right inline-block">
											<span class="">{{ (item.mileage / 1000).toFixed(2) }}km</span>
											<span> | </span>
											<span class="">{{ formatTime(item.sumtime) }}</span>
										</span>
									</template>
								</n-list-item>
							</n-list>
							<n-empty v-else description="数据为空" class="mt-20"> </n-empty>
						</n-scrollbar>
					</n-card>
				</NGi>

				<!-- 设备排名列表 -->
				<NGi span="8">
					<n-card title="各飞行器数据量" class="bg-dark-600 h-80" size="small">
						<n-list v-if="dataOverview.flightMileage.length" :show-divider="false" class="bg-dark-600">
							<n-list-item v-for="(item, index) in filledFlightMileage" :key="index">
								<template #prefix>
									{{ index + 1 }}
								</template>
								{{ item.nickname }}
								<template #suffix>
									<span class="w-75 text-right inline-block">
										<n-progress type="line" :percentage="item.mileage / totalMileage * 100">
											<span class="w-120px inline-block">{{ (item.mileage / 1000).toFixed(2)
												}}km</span>
										</n-progress>
									</span>
								</template>
							</n-list-item>
						</n-list>
						<n-empty v-else description="数据为空" class="mt-20"> </n-empty>
					</n-card>
				</NGi>
			</NGrid>
		</div>
	</NCard>
</template>
<script setup lang="ts">
import { computed, ref, watch, onMounted } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import { useEcharts } from '@/hooks/common/echarts';
import { fetchDataOverview } from '@/service/api';

defineOptions({ name: 'DataOverview' });

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

const dataPicker = [
	{
		value: '0',
		label: '全部'
	},
	{
		value: '1',
		label: '本周'
	},
	{
		value: '2',
		label: '上周'
	},
	{
		value: '3',
		label: '本月'
	},
	{
		value: '4',
		label: '上月'
	},
	{
		value: '5',
		label: '近30天'
	},
	{
		value: '6',
		label: '自定义'
	},
].map((s) => {
	s.value = s.value.toLowerCase()
	return s
});

const dataPickerValue = ref('0');

const dayRange = ref<[number, number] | null>(null);

const { domRef, updateOptions } = useEcharts(() => ({
	tooltip: {
		trigger: 'item'
	},
	//   legend: {
	//     bottom: '1%',
	//     left: 'center',
	//     itemStyle: {
	//       borderWidth: 0
	//     }
	//   },
	legend: {
		x: 'right', //可设定图例在左、右、居中
		y: '1%', //可设定图例在上、下、居中
		icon: 'pin',
		textStyle: {
			borderWidth: 0
		},
	},
	series: [
		{
			color: ['#5da8ff', '#8e9dff', '#fedc69', '#26deca'],
			name: $t('page.home.schedule'),
			type: 'pie',
			radius: ['45%', '65%'],
			avoidLabelOverlap: false,
			itemStyle: {
				borderRadius: 10,
				borderColor: '#fff',
				borderWidth: 1
			},
			label: {
				show: false,
				position: 'center'
			},
			emphasis: {
				label: {
					show: true,
					fontSize: '12'
				}
			},
			labelLine: {
				show: false
			},
			data: [] as { name: string; value: number }[]
		}
	]
}));

const dataOverview = ref<Api.Home.DataOverview>({ flightCount: [], flightTime: [], flightMileage: [] })

const formatTime = computed(() => (seconds: number) => {
	const hours = Math.floor(seconds / 3600);
	const minutes = Math.floor((seconds % 3600) / 60);
	return `${hours}h${minutes}min`;
});

// 处理数据量不足5个
const filledFlightMileage = computed(() => {
	const originalData = dataOverview.value.flightMileage || [];
	const filledData = originalData.slice(0, 5); // 取前5个数据项

	// 如果数据不足5个，则填充空数据项
	while (filledData.length < 5) {
		filledData.push({ nickname: '---', mileage: 0, drone_sn: '0' });
	}

	return filledData;
});

// 计算总里程
const totalMileage = computed(() => {
	console.log(dataOverview.value.flightMileage.reduce((sum, item) => sum + item.mileage, 0));

	return dataOverview.value.flightMileage.reduce((sum, item) => sum + item.mileage, 0);
});

function handleData(data: Api.Home.DataOverview) {
	dataOverview.value = data;

	const processedFlightCount = data.flightCount.map(item => ({
		name: item.nick_name,
		value: item.flight_count
	}));

	console.log('processedFlightCount: ', processedFlightCount);

	updateOptions(opts => {
		opts.series[0].data = processedFlightCount;

		return opts;
	});
}

function updateLocale() {
	updateOptions((opts, factory) => {
		const originOpts = factory();

		opts.series[0].name = originOpts.series[0].name;

		opts.series[0].data = [
			{ name: $t('page.home.study'), value: 20 },
			{ name: $t('page.home.entertainment'), value: 10 },
			{ name: $t('page.home.work'), value: 40 },
			{ name: $t('page.home.rest'), value: 30 }
		];

		return opts;
	});
}

async function fetchDate() {
	let stime = '';
	let etime = '';
	let type = '';

	if (dataPickerValue.value === '0') {
		// 全部
		stime = '';
		etime = '';
		type = '';
	} else if (['1', '2', '3', '4', '5'].includes(dataPickerValue.value)) {
		// 本周、上周、本月、上月、近30天
		type = dataPickerValue.value;
		stime = '';
		etime = '';
		// 禁用日期选择器
		dayRange.value = null;
	} else if (dataPickerValue.value === '6') {
		// 自定义
		type = '';
		// 不在这里调用 fetchDataOverview
		return;
	}

	const res = await fetchDataOverview({ stime, etime, type });

	handleData(res.data);
	// console.log(res.data);
}

async function onDayRangeConfirm(dayVal: [number, number] | null) {
	if (dataPickerValue.value === '6' && Array.isArray(dayVal) && dayVal.length === 2) {
		const stime = dayVal[0].toString();
		const etime = dayVal[1].toString();
		const type = '';
		await fetchDataOverview({ stime, etime, type });
	}
}

watch(dataPickerValue, (newValue) => {
	fetchDate();
});

watch(() => appStore.locale, () => {
	updateLocale();
});

onMounted(() => {
	fetchDate();
});

</script>

<style scoped></style>
