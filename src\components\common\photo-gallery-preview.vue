<template>
  <NModal :show="visible" @update:show="visible = $event" :mask-closable="true" preset="card"
    style="width: 90vw; height: 90vh">
    <div class="flex h-full">
      <!-- 左侧区域 -->
      <div class="flex-1 flex flex-col mr-4 w-80%" style="height: calc(90vh - 70px)">
        <!-- 大图区域 -->
        <div class="flex-1 h-100% relative mb-4 flex items-center justify-center bg-gray-100 overflow-hidden">
          <!-- 普通图片展示 -->
          <template v-if="currentPhoto?.subFileType != 1">
            <NImage :src="currentPhoto?.filePath" object-fit="contain" class="max-w-full max-h-full" preview-disabled
              show-toolbar-tooltip />
          </template>

          <!-- 全景图片内嵌展示 -->
          <div v-else id="panorama-main-view" class="w-full h-full relative">
            <div v-if="inMainView" class="back absolute top-2 left-2 z-10" @click="closeMainPanoramaView"></div>
          </div>
        </div>
        <!-- 缩略图列表 -->
        <div class="h-24 flex-shrink-0 relative">
          <!-- 左滚动按钮 -->
          <div
            class="absolute left-0 top-1/2 -translate-y-1/2 z-10 h-full flex items-center px-2 bg-gradient-to-r from-black/10 to-transparent">
            <NButton circle @click="handleScrollLeft" :disabled="!canScrollLeft" type="primary"
              class="shadow-lg hover:scale-110 transition-transform" style="background: rgba(0, 0, 0, 0.6)">
              <template #icon>
                <icon-mdi-chevron-left class="text-xl" />
              </template>
            </NButton>
          </div>

          <!-- 缩略图容器 -->
          <div ref="scrollContainerRef" class="h-full overflow-x-auto hide-scrollbar" @wheel.prevent="handleWheel"
            @scroll="handleScroll">
            <div class="flex gap-2 py-2 h-full">
              <div v-for="(photo, index) in localPhotos" :key="photo.id"
                class="h-full aspect-square flex-shrink-0 cursor-pointer relative rounded overflow-hidden transition-transform hover:scale-105"
                @click="handlePhotoClick(index)">
                <NImage :src="photo.cover" class="w-full h-full" object-fit="cover" preview-disabled />
                <div v-if="index === activeIndex" class="absolute inset-0 border-2 border-primary"></div>
              </div>
            </div>
          </div>

          <!-- 右滚动按钮 -->
          <div
            class="absolute right-0 top-1/2 -translate-y-1/2 z-10 h-full flex items-center px-2 bg-gradient-to-l from-black/10 to-transparent">
            <NButton circle @click="handleScrollRight" :disabled="!canScrollRight" type="primary"
              class="shadow-lg hover:scale-110 transition-transform" style="background: rgba(0, 0, 0, 0.6)">
              <template #icon>
                <icon-mdi-chevron-right class="text-xl" />
              </template>
            </NButton>
          </div>

          <!-- 加载状态指示器 -->
          <div v-if="loading" class="absolute right-2 top-1/2 -translate-y-1/2">
            <NSpin size="small" />
          </div>
        </div>
      </div>

      <!-- 右侧信息区域 -->
      <div class="w-20% bg-white-100 p-4 rounded overflow-auto" style="height: calc(90vh - 65px)">
        <div class="mb-4">
          <h3 class="font-bold mb-2">文件信息</h3>
          <div class="space-y-2 text-sm">
            <div>机型/镜头：{{ currentPhoto?.payload || '---' }}</div>
            <div>飞行任务：{{ currentPhoto?.jobName || '---' }}</div>
            <div>媒体名称：{{ currentPhoto?.fileName || '---' }}</div>
            <div>媒体大小：{{ ((currentPhoto?.size || 0) / 1024 / 1024).toFixed(2) }} MB</div>
            <div>经度：{{ (currentPhoto?.lng || 0).toFixed(6) }}°E</div>
            <div>维度：{{ (currentPhoto?.lat || 0).toFixed(6) }}°N</div>
            <div>海拔：{{ currentPhoto?.absoluteAltitude || 0 }}</div>
            <div>
              上传渠道：{{ (currentPhoto?.flightType || 3) == 0 ? '航线任务' : (currentPhoto?.flightType || 3) == 1 ? '手飞任务' :
                '其他' }}
            </div>
            <div>地址：{{ currentPhoto?.address || '---' }}</div>
            <div>拍摄时间：{{ currentPhoto?.createTime }}</div>
          </div>
        </div>
        <!-- 添加下载按钮 -->
        <NButton type="primary" block @click="handleDownload(currentPhoto?.filePath)"
          :disabled="!currentPhoto?.filePath">
          <template #icon>
            <icon-mdi-download class="text-lg" />
          </template>
          下载原图
        </NButton>
      </div>
    </div>
  </NModal>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  watch,
  nextTick,
  onMounted,
  onUpdated,
  onUnmounted
} from "vue";
import { Viewer } from 'photo-sphere-viewer';
import 'photo-sphere-viewer/dist/photo-sphere-viewer.css';
import { fetchFilesList } from "@/service/api";
import dayjs from "dayjs";
import * as THREE from "three";
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";

let viewer: any = null;
let imgurl1: string | null = null;

let renderer: any = null;
let controls: any = null;
let scene: any = null;
let camera: any = null;
let sphere: any = null;
let panoramaShow = ref(false);
// 控制全景图是否在主视图中显示
const inMainView = ref(false);

const props = defineProps<{
  show: boolean;
  photos: Api.File.PhotoListItem[];
  currentIndex: number;
  itemCount: number;
  queryParams: {
    flightJobId: string | null;
    range: [number, number] | null;
  };
}>();

const emit = defineEmits(["update:show"]);

const visible = ref(props.show);
const currentPage = ref(0);
const pageSize = 10;
const totalCount = ref(0);
const localPhotos = ref<Api.File.PhotoListItem[]>([]);
const loading = ref(false);
const imageLoading = ref(true);
let cuurentIs3Dimg = ref(true);
// 初始化当前图片索引
const activeIndex = ref(0);
const scrollContainerRef = ref<HTMLElement | null>(null);
const isWidImg = ["W", "V"];
function parseImageType(fileName: string) {
  const regex = /^DJI_\d{14}_\d{4}_(\w)\.jpeg$/;
  const match = fileName.match(regex);
  if (match) {
    const imageType = match[1];
    return imageType;
  } else {
    return "Invalid file name format";
  }
}

// 滚动到指定图片
const scrollToActiveImage = () => {
  if (!scrollContainerRef.value) return;

  const container = scrollContainerRef.value;
  const activeThumb = container.children[0].children[
    activeIndex.value
  ] as HTMLElement;

  if (activeThumb) {
    // 计算目标滚动位置（将选中的图片滚动到中间）
    const containerWidth = container.clientWidth;
    const thumbLeft = activeThumb.offsetLeft;
    const thumbWidth = activeThumb.offsetWidth;
    const scrollLeft = thumbLeft - containerWidth / 2 + thumbWidth / 2;

    // 平滑滚动到目标位置
    container.scrollTo({
      left: scrollLeft,
      behavior: "smooth"
    });
  }
};

// 检查容器是否需要加载更多
const checkNeedLoadMore = () => {
  if (!scrollContainerRef.value) return false;
  const container = scrollContainerRef.value;
  return (
    container.scrollWidth <= container.clientWidth &&
    localPhotos.value.length < props.itemCount
  );
};

// 更新滚动按钮状态
const updateScrollButtonsState = () => {
  if (!scrollContainerRef.value) return;
  const container = scrollContainerRef.value;
  canScrollLeft.value = container.scrollLeft > 0;
  canScrollRight.value =
    container.scrollLeft < container.scrollWidth - container.clientWidth - 10; // 添加10px缓冲区
};

// 监听显示状态
watch(
  () => props.show,
  async newVal => {
    visible.value = newVal;
    if (newVal) {
      // 打开预览时初始化数据
      localPhotos.value = props.photos || [];
      activeIndex.value = props.currentIndex;
      imageLoading.value = true;

      // 等待 DOM 更新后检查是否需要加载更多
      await nextTick();
      if (checkNeedLoadMore()) {
        handleNextPage();
      }
      scrollToActiveImage();
      updateScrollButtonsState();

      // 当打开预览并且当前图片是全景图(subFileType=1)时，自动在主视图中显示全景图
      if (currentPhoto.value?.subFileType === 1) {
        inMainView.value = true;
        nextTick(() => {
          initMainViewPanorama(currentPhoto.value?.filePath || '');
        });
      }
    } else {
      // 关闭时重置状态
      inMainView.value = false;
      panoramaShow.value = false;
      cleanupPanorama();
    }
  }
);

function renderToCanvas(url: string) { }

watch(visible, newVal => {
  if (!newVal) {
    panoramaShow.value = false;
    inMainView.value = false;
    cleanupPanorama();
    emit("update:show", newVal);
  }
});

// 监听当前图片变化
watch(activeIndex, (newVal) => {
  // 清理旧的全景图
  if (inMainView.value) {
    cleanupPanorama();
    inMainView.value = false;
  }

  // 如果新的图片是全景图（subFileType==1），则自动加载全景视图
  nextTick(() => {
    if (currentPhoto.value?.subFileType === 1) {
      inMainView.value = true;
      initMainViewPanorama(currentPhoto.value?.filePath || '');
    }
  });

  imageLoading.value = true;
  nextTick(() => {
    scrollToActiveImage();
  });
});

// 计算当前显示的图片信息
const currentPhoto = computed(() => localPhotos.value[activeIndex.value]);

// 主视图全景图初始化
function initMainViewPanorama(url: string) {
  if (!url) return;

  // 确保先清理旧的实例
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }

  // 创建新的全景查看器
  nextTick(() => {
    const container = document.getElementById('panorama-main-view');
    if (container) {
      viewer = new Viewer({
        container: container,
        panorama: url,
        size: {
          width: container.clientWidth,
          height: container.clientHeight
        },
      });
    }
  });
}

// 关闭主视图全景模式
function closeMainPanoramaView() {
  inMainView.value = false;
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
}

// 切换显示全景图
function handleShowPanorama(url?: string) {
  if (!url) return;

  if (inMainView.value) {
    // 如果已经在主视图显示，则关闭
    inMainView.value = false;
    if (viewer) {
      viewer.destroy();
      viewer = null;
    }
  } else {
    // 否则打开主视图全景
    inMainView.value = true;
    nextTick(() => {
      initMainViewPanorama(url);
    });
  }
}

// 清理全景图资源
function cleanupPanorama() {
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }

  if (renderer) {
    renderer.dispose();  // 销毁渲染器
  }
  if (sphere) {
    scene && scene.remove(sphere);  // 从场景中移除
    sphere.geometry.dispose();  // 销毁几何体
    sphere.material.dispose();  // 销毁材质
  }
  scene = null;
}

// 获取图片列表函数
async function getPhotoList(isAppend = false) {
  loading.value = true;

  const json: Api.File.FileQuery = {
    recordIds: props.queryParams.flightJobId || "",
    startDate: props.queryParams.range?.[0]
      ? dayjs(props.queryParams.range[0]).format("YYYY-MM-DD")
      : null,
    endDate: props.queryParams.range?.[1]
      ? dayjs(props.queryParams.range[1] + 24 * 60 * 60 * 1000).format(
        "YYYY-MM-DD"
      )
      : null,
    pageNum: currentPage.value,
    pageSize: pageSize,
    fileType: 0
  };
  const { data, error } = await fetchFilesList(json);
  if (!error) {
    if (isAppend) {
      localPhotos.value = [...localPhotos.value, ...data.rows];
    } else {
      localPhotos.value = data.rows;
    }
    totalCount.value = data.total;
  }
  loading.value = false;
  updateScrollButtonsState();
}

// 翻页处理函数
const handleNextPage = async () => {
  if (currentPage.value * pageSize < totalCount.value && !loading.value) {
    currentPage.value++;
    await getPhotoList(true);
  }
};

// 切换图片
const handlePhotoClick = (index: number) => {
  activeIndex.value = index;
};

// 改为 ref 而不是 computed
const canScrollLeft = ref(false);
const canScrollRight = ref(false);
const SCROLL_STEP = 1000;

// 修改左右滚动按钮处理函数
const handleScrollLeft = () => {
  if (!scrollContainerRef.value || loading.value) return;
  const container = scrollContainerRef.value;
  container.scrollBy({
    left: -SCROLL_STEP,
    behavior: "smooth"
  });
};

const handleScrollRight = async () => {
  if (!scrollContainerRef.value || loading.value) return;
  const container = scrollContainerRef.value;
  container.scrollBy({
    left: SCROLL_STEP,
    behavior: "smooth"
  });
};

// 监听滚动事件
const handleScroll = async () => {
  if (!scrollContainerRef.value || loading.value) return;
  const container = scrollContainerRef.value;

  // 更新按钮状态
  updateScrollButtonsState();

  // 检查是否滚动到末尾
  if (
    container.scrollLeft >=
    container.scrollWidth - container.clientWidth - 10
  ) {
    // 检查是否还有更多数据可加载
    if (currentPage.value * pageSize < totalCount.value) {
      currentPage.value++;
      await getPhotoList(true);
    }
  }
};

// 处理鼠标滚轮事件
const handleWheel = (e: WheelEvent) => {
  if (!scrollContainerRef.value) return;
  e.preventDefault();
  const container = scrollContainerRef.value;
  const scrollLeft = container.scrollLeft;
  const delta = e.deltaY || e.deltaX;
  const newScrollLeft = scrollLeft + (delta > 0 ? SCROLL_STEP : -SCROLL_STEP);
  container.scrollTo({
    left: newScrollLeft,
    behavior: "smooth"
  });
};

// 实现鼠标滚轮缩放
const onWheel = (event: WheelEvent) => {
  if (visible.value && panoramaShow.value) {
    if (event.deltaY > 0) {
      camera.zoom = Math.max(camera.zoom / 1.1, 0.5); // 缩小
    } else {
      camera.zoom = Math.min(camera.zoom * 1.1, 5); // 放大
    }
    camera.updateProjectionMatrix(); // 更新相机的投影矩阵
  }
};

function unRender() {
  cleanupPanorama();
  window.removeEventListener("wheel", onWheel);
}

// 在组件挂载和更新时初始化滚动状态
onMounted(() => {
  initRenderer();
  nextTick(() => {
    updateScrollButtonsState();
    totalCount.value = props.itemCount;
    if (localPhotos.value.length < totalCount.value) {
      handleNextPage();
    }
  });
});

const initRenderer = () => {
  // 只初始化一次 renderer
  if (renderer) return renderer;
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    powerPreference: "default",
    precision: "mediump"
  });
  renderer.setPixelRatio(Math.min(2, window.devicePixelRatio)); // 限制最大像素比
  renderer.outputColorSpace = THREE.SRGBColorSpace;
  renderer.setSize(window.innerWidth, window.innerHeight);
  renderer.domElement.style.touchAction = "none"; // 禁用浏览器默认滚动
  return renderer;
};

const initScene = () => {
  // 只初始化一次 scene 和 camera
  if (!scene) {
    scene = new THREE.Scene();
  }
  if (!camera) {
    camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      2000
    );
  }

  // 清理旧的对象，特别是球体和控制器
  if (controls) {
    controls.dispose(); // 销毁旧的 controls
  }
  if (sphere) {
    scene.remove(sphere); // 移除旧的 sphere
    sphere.geometry.dispose(); // 销毁球体几何体
    sphere.material.dispose(); // 销毁球体材质
  }

  // 重新创建控制器
  controls = new OrbitControls(camera, renderer.domElement);
  controls.enableDamping = true;
  controls.dampingFactor = 0.05;
  controls.zoomSpeed = 0.8;
  controls.minDistance = 50;
  controls.maxDistance = 2000;
  controls.screenSpacePanning = false;
};

// 加载无损纹理
const loadTexture = async (path: string) => {
  const manager = new THREE.LoadingManager();
  const textureLoader = new THREE.TextureLoader(manager);

  return new Promise((resolve, reject) => {
    const texture = textureLoader.load(
      path,
      (texture) => {
        texture.minFilter = THREE.LinearFilter;
        texture.magFilter = THREE.LinearFilter;
        texture.generateMipmaps = false; // 禁用mipmap
        texture.anisotropy = renderer.capabilities.getMaxAnisotropy();
        texture.colorSpace = THREE.SRGBColorSpace;
        resolve(texture);
      },
      undefined,
      (err) => reject(err)
    );
  });
};

// 创建全景球体
const createSphere = (texture: any) => {
  const geometry = new THREE.SphereGeometry(
    500, // 半径
    125, // 宽度分段（超高细分）
    60, // 高度分段
    Math.PI, // 修正接缝位置
    Math.PI * 2
  );
  geometry.computeTangents(); // 增强表面精度
  const material = new THREE.MeshBasicMaterial({
    map: texture,
    side: THREE.BackSide,
    depthTest: false,
    depthWrite: false
  });
  return new THREE.Mesh(geometry, material);
};

// 主程序
const init = async (url: string) => {
  try {
    initScene(); // 每次调用时初始化场景和资源
    // 加载纹理并创建球体
    const texture = await loadTexture(url);
    sphere = createSphere(texture); // 重新创建球体
    scene.add(sphere);
    camera.position.set(0, 0, 0.1);
    // 视窗响应式处理
    window.addEventListener("resize", () => {
      camera.aspect = window.innerWidth / window.innerHeight;
      camera.updateProjectionMatrix();
      renderer.setSize(window.innerWidth, window.innerHeight);
    });
    // 防撕裂渲染循环
    const animate = () => {
      if (scene && camera) {
        requestAnimationFrame(animate);
        controls.update();
        renderer.render(scene, camera);
      }
    };
    // animate();
    if (scene && camera) {
      controls.update();
      renderer.render(scene, camera);
    }
  } catch (error) {
    console.error("初始化失败:", error);
  }
};

// 处理关闭全景图事件
const closeView = () => {
  panoramaShow.value = false;
  if (viewer) {
    viewer.destroy();
    viewer = null;
  }
};

// 处理旧版全景图按钮
const handleInfo = (url?: string) => {
  if (!url) return;

  setTimeout(() => {
    viewer = new Viewer({
      container: document.querySelector('#panorama') as HTMLElement,
      panorama: url,
      size: {
        width: window.innerWidth,
        height: window.innerHeight,
      },
    });
  });
  panoramaShow.value = true;
};

// 监听图片列表变化
watch(localPhotos, async () => {
  await nextTick();
  if (
    checkNeedLoadMore() &&
    !loading.value &&
    currentPage.value * pageSize < totalCount.value
  ) {
    handleNextPage();
  }
  updateScrollButtonsState();
});

// 修改下载方法
const handleDownload = async (url?: string) => {
  if (url) {
    try {
      // 使用 fetch 获取图片数据
      const response = await fetch(url);
      const blob = await response.blob();

      // 创建 Blob URL
      const blobUrl = window.URL.createObjectURL(blob);

      // 从 URL 中提取文件名
      let fileName = "image.jpg"; // 默认文件名
      try {
        // 移除查询参数，获取纯净的 URL 路径
        const urlWithoutParams = url.split("?")[0];
        // 获取路径中的最后一个部分作为文件名
        const pathSegments = urlWithoutParams.split("/");
        const lastSegment = pathSegments[pathSegments.length - 1];
        if (lastSegment) {
          fileName = decodeURIComponent(lastSegment); // 解码 URL 编码的文件名
        }
      } catch (e) {
        console.error("提取文件名失败:", e);
      }

      // 创建临时下载链接
      const link = document.createElement("a");
      link.href = blobUrl;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();

      // 清理
      document.body.removeChild(link);
      window.URL.revokeObjectURL(blobUrl);
    } catch (error) {
      console.error("下载失败:", error);
    }
  }
};

onUnmounted(() => {
  unRender();
});
</script>

<style scoped>
.n-modal {
  @apply bg-white;
}

/* 确保图片容器始终保持居中对齐 */
.n-image {
  @apply flex items-center justify-center;
}

/* 翻页按钮悬停效果 */
.n-button:not(:disabled):hover {
  @apply opacity-90;
}

/* 缩略图悬停效果 */
.thumbnail:hover {
  @apply transform scale-105 transition-transform;
}

/* 禁用状态的按钮样式 */
.n-button:disabled {
  @apply opacity-50 cursor-not-allowed;
}

/* 隐藏滚动条但保持可滚动 */
.hide-scrollbar {
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* IE and Edge */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
  /* Chrome, Safari and Opera */
}

/* 确保缩略图保持正方形 */
.aspect-square {
  aspect-ratio: 1 / 1;
}
</style>
