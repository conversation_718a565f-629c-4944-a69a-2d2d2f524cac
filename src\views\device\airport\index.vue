<script setup lang="ts">
import { h, onBeforeUnmount, onMounted, reactive, ref } from 'vue';
import { NGradientText, NIcon, NInput, NTag, NText, useDialog, useMessage, FormInst } from 'naive-ui';
import { Close } from '@vicons/ionicons5';
import SvgIcon from '@/components/custom/svg-icon.vue';

import {
  deleteAirportByID,
  fetchAirportList,
  fetchAirportRegisterInfo,
  resetAirport,
  updateNickName,
  sendAirportRegisterInfo,
  sendDeviceRegisterInfo
} from '@/service/api';
import { deptTree } from "@/service/api/user";

const message = useMessage();
const dialog = useDialog();

const showAddAirportModal = ref(false);
const showInfoAirportModal = ref(false);
const tableLoading = ref(true);
const formValue = reactive<{ deptId: number | null; name: string }>({
  name: '',
  deptId: null
});
const editingRow = ref("");
const treeData = ref([]);
// 添加全局点击事件处理函数
const handleGlobalClick = (e: MouseEvent) => {
  // 如果点击的不是编辑框或图标，则关闭编辑框
  const target = e.target as HTMLElement;
  if (!target.closest('.editing-input') && !target.closest('.edit-icon')) {
    editingRow.value = "";
  }
};

function renderCell(value: string | number) {
  if (!value) {
    return h(NText, { depth: 3 }, { default: () => '---' });
  }
  return value;
}

const editAirportName = (row: any, deviceName: string) => {
  editingRow.value = row.deviceDto.deviceSn + "_" + deviceName;
};


const columns = [
  {
    title: '组织名称',
    key: 'deviceDto.workspaceName',
    default: '---',
  },
  {
    title: '机场名称',
    key: 'deviceDto.nickname',
    default: '---',
    render(row: any) {
      if (!row.deviceDto) return '---';
      const nickname = row.deviceDto.nickname;
      const isEditing = editingRow.value === `${row.deviceDto.deviceSn}_dock`;
      const displayContent = isEditing
        ? h(NInput, {
          value: nickname,
          class: 'editing-input',
          onBlur: (e: any) => {
            if (e.target.value.length < 1) {
              row.deviceDto.nickname = row.deviceDto.deviceName; // 恢复默认值
            } else {
              updateAirportName(row, e.target.value, "dock");
            }
          },
          onUpdateValue: (value: string) => {
            row.deviceDto.nickname = value;
          }
        })
        : h('span', { style: { display: 'inline-flex', alignItems: 'center' } }, [
          nickname,
          row.deviceDto && row.deviceDto.nickname
            ? h(
              'div',
              {
                class: 'ml-5px cursor-pointer edit-icon',
                onClick: (e: MouseEvent) => {
                  e.stopPropagation();

                  editAirportName(row, "dock");
                },
                style: { display: 'inline-flex', alignItems: 'center' }
              },
              [
                h(SvgIcon, {
                  icon: 'mdi:rename-outline'
                })
              ]
            )
            : null
        ]);

      return displayContent;
    }
  },
  {
    title: '机场型号',
    key: 'deviceDto.deviceName',
    default: '---'
  },
  {
    title: '飞行器名称',
    key: 'deviceDto.children.nickname',
    default: '---',
    render(row: any) {
      if (!row.deviceDto) return '---';
      const nickname = row.deviceDto.children.nickname;
      const isEditing = editingRow.value === row.deviceDto.deviceSn + "_drone";
      const displayContent = isEditing
        ? h(NInput, {
          value: nickname,
          class: 'editing-input',
          onBlur: (e: any) => {
            if (e.target.value.length < 1) {
              row.deviceDto.children.nickname = row.deviceDto.children.deviceName; // 恢复默认值
            } else {
              updateAirportName(row, e.target.value, "drone");
            }
          },
          onUpdateValue: (value: string) => {
            row.deviceDto.children.nickname = value;
          }
        })
        : h('span', { style: { display: 'inline-flex', alignItems: 'center' } }, [
          nickname,
          row.deviceDto && row.deviceDto.children.nickname
            ? h(
              'div',
              {
                class: 'ml-5px cursor-pointer edit-icon',
                onClick: (e: MouseEvent) => {
                  e.stopPropagation();

                  editAirportName(row, "drone");
                },
                style: { display: 'inline-flex', alignItems: 'center' }
              },
              [
                h(SvgIcon, {
                  icon: 'mdi:rename-outline'
                })
              ]
            )
            : null
        ]);

      return displayContent;
    }
  },
  {
    title: '飞行器型号',
    key: 'deviceDto.children.deviceName',
    default: '----'
  },
  {
    title: '绑定状态',
    key: 'bindStatus',
    render(row: any) {
      return h(
        NTag,
        {
          type: row.bindStatus === 1 ? 'success' : 'error'
        },
        {
          default: () => (row.bindStatus === 1 ? '已绑定' : '未绑定')
        }
      );
    }
  },
  {
    title: '所属组织',
    key: 'deviceDto.workspaceName'
  },
  {
    title: '操作',
    key: 'action',
    render(row: any) {
      return h('span', [
        h(
          NText,
          {},
          {
            default: () => {
              const elements = [];
              elements.push(
                h(
                  NText,
                  {
                    type: 'info',
                    class: 'cursor-pointer',
                    onClick: () => play(row)
                  },
                  { default: () => '查看' }
                )
              );
              if (row.bindStatus === 1) {
                elements.push(
                  h(
                    NText,
                    {
                      type: 'error',
                      class: 'cursor-pointer ml-10px',
                      onClick: () => resetGateway(row)
                    },
                    { default: () => '解绑' }
                  )
                );
              } else {
                elements.push(
                  h(
                    NText,
                    {
                      type: 'error',
                      class: 'cursor-pointer ml-10px',
                      onClick: () => deleteRow(row)
                    },
                    { default: () => '删除' }
                  )
                );
              }
              return elements;
            }
          }
        )
      ]);
    }
  }
];

const tableData = ref([]);

const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  itemCount: 0,
  showSizePicker: true,
  pageSizes: [10, 15, 20],
  onChange: (page: number) => {
    pagination.pageNum = page;
    getAirportList();
  },
  onUpdatePageSize: (pageSize: number) => {
    pagination.pageSize = pageSize;
    pagination.pageNum = 1;
    getAirportList();
  }
});

const registerInfo: Api.Airport.MqttRegisterInfo = reactive({
  id: 0,
  mqttGateway: '',
  mqttUsername: '',
  mqttPassword: '',
  gatewayAccount: ''
});

const deviceRegisterInfo = ref<Api.Airport.RegisterInfo>({});

// 获取机场列表
async function getAirportList() {
  const { name, deptId } = formValue;
  const { data: fetchAirportListInfo } = await fetchAirportList({
    name,
    deptId: deptId || undefined,
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize
  });
  tableLoading.value = false;
  tableData.value = fetchAirportListInfo.rows;
  pagination.itemCount = fetchAirportListInfo.total;
  console.log('tableData.value: ', tableData.value);
}

const showCreateDeviceModal = ref(false);
const createDeviceFormRef = ref<FormInst | null>(null);
const addDeviceForm = reactive({
  deviceSn: '',
  deviceName: '',
  childDeviceSn: '',
  deviceDesc: ''
});
const addDeviceFormRules = {
  deviceSn: [{ key: 'deviceSn', required: true, trigger: ['blur', 'input'], message: '请输入设备编码' }],
  deviceName: [{ key: 'deviceName', required: true, trigger: ['blur', 'input'], message: '请输入设备名称' }],
  childDeviceSn: [{ key: 'childDeviceSn', required: true, trigger: ['blur', 'input'], message: '请输入无人机编码' }],
  deviceDesc: [{ key: 'deviceDesc', required: true, trigger: ['blur', 'input'], message: '请输入无人机名称' }]
};

// 点击"新建设备"
async function handelCreateDeviceClick() {
  showCreateDeviceModal.value = true;
}

// 点击"机场注册"
async function handelAirportRegisterClick() {
  const { data: fetchedRegisterInfo } = await fetchAirportRegisterInfo();
  Object.assign(registerInfo, fetchedRegisterInfo);
  showAddAirportModal.value = true;
}

// 点击"注册" (机场注册模态框-注册)
async function handelRegisterClick() {
  await sendAirportRegisterInfo(registerInfo);
  message.success('注册成功');
  showAddAirportModal.value = false;
  tableLoading.value = true;
  await getAirportList();
}

function handleQueryClick() {
  tableLoading.value = true;
  getAirportList();
}

function play(row: any) {
  showInfoAirportModal.value = true;
  deviceRegisterInfo.value = row;
}


const updateAirportName = (row: any, newName: string, type: string) => {

  var sn = '';
  if (type == 'dock') {
    sn = row.deviceDto.deviceSn
    row.deviceDto.nickname = newName;
  } else {
    sn = row.deviceDto.children.deviceSn
    row.deviceDto.children.nickname = newName;
  }
  const data = {
    sn,
    name: newName
  }

  editingRow.value = "";
  const error = updateNickName(data);
  message.success(`已更新为: ${newName}`);
};

// 解绑操作
async function resetGateway(row: any) {
  const { id, deviceSn, deviceDto } = row;
  console.log('解绑：', deviceDto);
  const data = {
    id,
    sn: deviceSn
  };
  // console.log(row);
  dialog.warning({
    title: '警告',
    content: `确定要解绑 ${deviceDto?.nickname} 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const { error } = await resetAirport(data);
      if (!error) {
        message.success('解绑成功');
        tableLoading.value = true;
        getAirportList();
      }
    }
  });
}

// 删除某条机场数据
async function deleteRow(row: any): Promise<void> {
  const { error } = await deleteAirportByID(row.id);
  if (!error) {
    message.success('删除成功');
    tableLoading.value = true;
    await getAirportList();
  }
}

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
};

onMounted(() => {
  document.addEventListener('click', handleGlobalClick);
  getDeptTree();
  getAirportList();
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleGlobalClick);
});

// 添加提交处理函数
const handleCreateDeviceSubmit = async () => {
  const isValid = await createDeviceFormRef.value?.validate(); // 触发验证
  if (!isValid) return

  const { data: fetchedRegisterInfo } = await fetchAirportRegisterInfo();
  // console.log('fetchedRegisterInfo: ', fetchedRegisterInfo);

  const params = {
    mqttGateway: fetchedRegisterInfo.mqttGateway,
    mqttUsername: fetchedRegisterInfo.mqttUsername,
    mqttPassword: fetchedRegisterInfo.mqttPassword,
    gatewayAccount: fetchedRegisterInfo.gatewayAccount,
    deviceDto: { ...addDeviceForm }
  }

  // console.log('params: ', params);

  const { error } = await sendDeviceRegisterInfo(params);

  if (!error) {
    showCreateDeviceModal.value = false;
    tableLoading.value = true;
    await getAirportList();
    message.success('注册成功');
    addDeviceForm.deviceSn = '';
    addDeviceForm.deviceName = '';
    addDeviceForm.childDeviceSn = '';
    addDeviceForm.deviceDesc = '';
  }
};

const handleCreateDeviceCancel = () => {
  showCreateDeviceModal.value = false;
  addDeviceForm.deviceSn = '';
  addDeviceForm.deviceName = '';
  addDeviceForm.childDeviceSn = '';
  addDeviceForm.deviceDesc = '';
}
</script>

<template>
  <div>
    <NCard title="机场注册列表" :bordered="false">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :x-gap="12" :cols="3">
          <NGridItem>
            <NButton type="primary" class="mr-10" attr-type="button" @click="handelCreateDeviceClick">新建设备</NButton>
            <NButton type="primary" attr-type="button" @click="handelAirportRegisterClick">机场注册</NButton>
          </NGridItem>
          <NFormItemGi label="组织选择：" path="deptId">
            <NTreeSelect v-model:value="formValue.deptId" filterable clearable key-field="id" label-field="label"
              children-field="children" :options="treeData" :default-value="formValue.deptId" />
          </NFormItemGi>
          <NGridItem>
            <NFormItem label="机场名称：" path="name">
              <NInput v-model:value="formValue.name" placeholder="输入机场名称" clearable />
              <NButton attr-type="button" class="ml-10" @click="handleQueryClick">查询</NButton>
            </NFormItem>
          </NGridItem>
        </NGrid>
      </NForm>

      <NDataTable ref="table" remote :columns="columns" :data="tableData" :loading="tableLoading"
        :render-cell="renderCell" :pagination="pagination" />
    </NCard>

    <NModal v-model:show="showInfoAirportModal">
      <NCard style="width: 50%" title="机场详情" hoverable :bordered="false" size="huge" role="dialog" aria-modal="true">
        <template #header-extra>
          <NIcon size="20" class="cursor-pointer" @click="showInfoAirportModal = false">
            <Close />
          </NIcon>
        </template>
        <div style="display: flex; justify-content: space-between">
          <!-- 左侧：两张图片 -->
          <!--          <div v-if="deviceRegisterInfo.deviceDto" style="flex: 1">-->
          <!--            <NImage :src="deviceRegisterInfo?.deviceDto?.iconUrl?.normal_icon_url" alt="机场" />-->
          <!--            <NImage :src="deviceRegisterInfo?.deviceDto?.children?.iconUrl?.normal_icon_url" alt="无人机" />-->
          <!--          </div>-->

          <!-- 中间：八个字段 -->
          <div v-if="deviceRegisterInfo.deviceDto" style="flex: 4">
            <NSpace vertical>
              <NDescriptions label-placement="left" :column="1" bordered>
                <NDescriptionsItem label="机场名称">
                  {{ deviceRegisterInfo?.deviceDto?.nickname }}
                </NDescriptionsItem>
                <NDescriptionsItem label="机场型号">
                  {{ deviceRegisterInfo?.deviceDto?.deviceName }}
                </NDescriptionsItem>
                <NDescriptionsItem label="设备序列号">
                  {{ deviceRegisterInfo?.deviceDto?.deviceSn }}
                </NDescriptionsItem>
                <NDescriptionsItem label="固件版本">
                  {{ deviceRegisterInfo?.deviceDto?.firmwareVersion }}
                </NDescriptionsItem>
                <NDescriptionsItem label="飞行器名称">
                  {{ deviceRegisterInfo?.deviceDto?.children?.nickname }}
                </NDescriptionsItem>
                <NDescriptionsItem label="飞行器型号">
                  {{ deviceRegisterInfo?.deviceDto?.children?.deviceName }}
                </NDescriptionsItem>
                <NDescriptionsItem label="设备序列号">
                  {{ deviceRegisterInfo?.deviceDto?.children?.deviceSn }}
                </NDescriptionsItem>
                <NDescriptionsItem label="固件版本">
                  {{ deviceRegisterInfo?.deviceDto?.children?.firmwareVersion }}
                </NDescriptionsItem>
              </NDescriptions>
            </NSpace>
          </div>

          <!-- 右侧：四个字段 -->
          <div style="flex: 2">
            <NSpace vertical :size="10">
              <NDescriptions label-placement="top" :column="1" bordered>
                <NDescriptionsItem label="MQTT网关地址">
                  {{ deviceRegisterInfo.mqttGateway }}
                </NDescriptionsItem>
                <NDescriptionsItem label="MQTT账号">
                  {{ deviceRegisterInfo.mqttUsername }}
                </NDescriptionsItem>
                <NDescriptionsItem label="MQTT密码">
                  {{ deviceRegisterInfo.mqttPassword }}
                </NDescriptionsItem>
                <NDescriptionsItem label="设备绑定码">
                  {{ deviceRegisterInfo.gatewayAccount }}
                </NDescriptionsItem>
              </NDescriptions>
            </NSpace>
          </div>
        </div>
        <template #footer>
          <NFlex justify="space-between">
            <NDescriptions label-placement="left">
              <NDescriptionsItem v-if="deviceRegisterInfo.deviceDto" label="加入时间">
                {{ deviceRegisterInfo.updateTime }}
              </NDescriptionsItem>
            </NDescriptions>
            <NButton @click="showInfoAirportModal = false">关闭</NButton>
          </NFlex>
        </template>
      </NCard>
    </NModal>

    <NModal v-model:show="showCreateDeviceModal">
      <NCard style="width: 400px" title="新建设备" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <template #header-extra>
          <NIcon size="20" class="cursor-pointer" @click="showCreateDeviceModal = false">
            <Close />
          </NIcon>
        </template>
        <NForm ref="createDeviceFormRef" :model="addDeviceForm" :rules="addDeviceFormRules" label-placement="left"
          label-width="auto">
          <NFormItem label="机场SN" path="deviceSn">
            <NInput class="w-250px" v-model:value="addDeviceForm.deviceSn" placeholder="输入机场SN" clearable />
          </NFormItem>
          <NFormItem label="机场名称" path="deviceName">
            <NInput class="w-250px" v-model:value="addDeviceForm.deviceName" placeholder="输入机场名称" clearable />
          </NFormItem>
          <NFormItem label="无人机SN" path="childDeviceSn">
            <NInput class="w-250px" v-model:value="addDeviceForm.childDeviceSn" placeholder="输入无人机SN" clearable />
          </NFormItem>
          <NFormItem label="无人机名称" path="deviceDesc">
            <NInput class="w-250px" v-model:value="addDeviceForm.deviceDesc" placeholder="输入无人机名称" clearable />
          </NFormItem>
          <div class="flex justify-end">
            <NButton class="mr-10px"  @click="handleCreateDeviceCancel">取消</NButton>
            <NButton type="primary" @click="handleCreateDeviceSubmit">注册</NButton>
          </div>
        </NForm>
      </NCard>
    </NModal>

    <NModal v-model:show="showAddAirportModal">
      <NCard style="width: 600px" title="机场注册" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <template #header-extra>
          <NIcon size="20" class="cursor-pointer" @click="showAddAirportModal = false">
            <Close />
          </NIcon>
        </template>
        <NDescriptions label-placement="top" :column="2">
          <NDescriptionsItem label="MQTT网关地址：">
            {{ registerInfo.mqttGateway }}
          </NDescriptionsItem>
          <NDescriptionsItem label="MQTT账号：">
            {{ registerInfo.mqttUsername }}
          </NDescriptionsItem>
          <NDescriptionsItem label="MQTT密码：">
            {{ registerInfo.mqttPassword }}
          </NDescriptionsItem>
          <NDescriptionsItem label="设备绑定码：">
            {{ registerInfo.mqttUsername }}
          </NDescriptionsItem>
        </NDescriptions>
        <div class="mt-10">
          <NGradientText type="info">说明</NGradientText>
          ：用户需要将上述信息通过遥控器输入至机场内完成注册，若直接注册，则遥控器完成操作后需要刷新列表才可显示。
        </div>
        <template #footer>
          <NFlex justify="end">
            <NButton @click="showAddAirportModal = false">取消</NButton>
            <NButton type="primary" @click="handelRegisterClick">注册</NButton>
          </NFlex>
        </template>
      </NCard>
    </NModal>
  </div>
</template>

<style scoped></style>
