<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { $t } from '@/locales';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { fetchIndexAll } from '@/service/api';
import { useRouterPush } from '@/hooks/common/router';

defineOptions({
  name: 'HeaderBanner'
});

const appStore = useAppStore();
const authStore = useAuthStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

interface StatisticData {
  id: number;
  label: string;
  value: string;
}

const statisticData = ref<StatisticData[]>([
  {
    id: 0,
    label: $t('page.home.NumberOfFlights'),
    value: '0次'
  },
  {
    id: 1,
    label: $t('page.home.NumberOfFlightsMiles'),
    value: '0.00km'
  },
  {
    id: 2,
    label: $t('page.home.NumberOfFlightsHours'),
    value: '0h0min'
  }
]);

const getIndexAll = async () => {
  const res = await fetchIndexAll();
  // console.log("system ~ baseURL:", res);

  statisticData.value[0].value = `${res.data.allFlightCount}次`; // 添加“次”字
  statisticData.value[1].value = `${(res.data.allFlightMileage / 1000).toFixed(2)}km`; // 转换为km
  const hours = Math.floor(res.data.allFlightTime / 3600); // 计算小时
  const minutes = Math.floor((res.data.allFlightTime % 3600) / 60); // 计算分钟
  statisticData.value[2].value = `${hours}h${minutes}min`; // 转换为xxhxxmin
}
const { routerPushByKey } = useRouterPush();

const jump = () => {
  // if (import.meta.env.MODE == 'test') {
  //   // //本地
  //   window.location.href = 'https://workspace.easyv.cloud/shareScreen/eyJzY3JlZW5JZCI6MzM0NjM5Mn0=?timeStamp=19588e02239'
  // } else {
  //   window.location.href = 'https://workspace.easyv.cloud/shareScreen/eyJzY3JlZW5JZCI6MzM0ODQ1M30=?timeStamp=1958a1058f2'
  // }
}

onMounted(() => {
  // createServiceConfig
  console.log("system ~ baseURL:", import.meta.env)
  getIndexAll();
})

</script>

<template>
  <NCard :bordered="false" class="card-wrapper">
    <NGrid :x-gap="gap" :y-gap="2" responsive="screen" item-responsive>
      <NGi span="10">
        <div class="flex-y-center">
          <div class="size-72px shrink-0 overflow-hidden rd-1/2">
            <img :src="authStore.sysInfo.webConfig.webLogoUrl" class="size-full" />
          </div>
          <div class="pl-32px" @click="jump()">
            <n-gradient-text type="info" class="text-10 font-semibold tracking-wider" @click="jump()">
              {{ authStore.sysInfo.webConfig.webName }}
            </n-gradient-text>

            <!-- <h3 class="text-12 font-semibold text-#333">
              {{ $t('page.home.greeting') }}
            </h3> -->
            <!-- <p class="text-#999 leading-30px">{{ $t('page.home.weatherDesc') }}</p> -->
          </div>
        </div>
      </NGi>
      <NGi span="14">
        <NSpace :size="24" justify="end">
          <NStatistic v-for="item in statisticData" :key="item.id" class="whitespace-nowrap pt-2 text-center"
            v-bind="item" />
          <!-- <div class="flex">
            <n-card class="mr-5 bg-#D8F7FE" title="电子围栏" size="small" hoverable>点击进入</n-card>
            <n-card class="bg-#D6EBFE" title="数据大屏" size="small" hoverable>点击进入</n-card>
          </div> -->
        </NSpace>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped></style>
