<template>
  <div class="h100vh relative">
    <div id="cesiumViewer" class="h100%"></div>
    <n-popover :show="popMenu.show" :x="popMenu.x" :y="popMenu.y" trigger="manual">
      <n-text @click="onClickFlyHere" class="cursor-pointer">飞到此处</n-text>
      <n-text @click="onClickMultiFly" class="cursor-pointer ml-10px">多点飞行</n-text>
    </n-popover>
    <status-bar id="status-bar" class="w-300px h-max left-0" v-if="loaded" :viewer="viewer"></status-bar>
    <!-- 上侧状态栏 -->
    <flyAnchor @cancelDots="cancelDots" />
    <!-- 底部状态栏 -->
    <flyRecord @cancel="cancel" />
    <!-- 无人机负载操作 -->
    <dronePayload
      v-show="taskStore.ptzControlStoreStatus && CanHandelPTZModeList.some(mode => mode === airportDroneItem.host?.modeCode)" />
    <!-- 指点飞行弹窗 -->
    <n-modal v-model:show="showClickFlyModal" preset="dialog" title="指点飞行" :showIcon="false" positive-text="确认"
      negative-text="取消" @positive-click="submitCallback" @negative-click="cancelCallback">
      <div>
        <n-card title="目标点">
          <n-descriptions label-placement="left" :column="1" separator=" ">
            <n-descriptions-item label="经纬度">经度： {{ mouthPosition.longitude }}<br />纬度： {{ mouthPosition.latitude }}
            </n-descriptions-item>
            <!-- <n-descriptions-item label="海拔高度">{{ mouthPosition.latitude }} </n-descriptions-item> -->
          </n-descriptions>
        </n-card>
        <n-flex align="center" class="mt-10px">
          相对高度： <n-input-number v-model:value="relativeHeight" size="small" clearable :min="10" :max="500"
            class="w-150px" />
        </n-flex>
      </div>
      <template #action>
        <div>
          <n-button @click="handelFlyHereModal" size="small" class="mr-10px">取消</n-button>
          <n-button @click="handelFlyHere" :loading="flyHereLoading" size="small" type="info">确认飞行 </n-button>
        </div>
      </template>
    </n-modal>
    <!-- 多点飞行 -->
    <n-modal v-model:show="showMultiFlyModal" preset="dialog" title="多点飞行" :showIcon="false">
      <div>
        <n-card title="目标点">
          <n-descriptions label-placement="left" :column="1" separator=" ">
            <n-descriptions-item label="经纬度">
              经度： {{ mouthPositionM.longitude }}<br />
              纬度： {{ mouthPositionM.latitude }}
            </n-descriptions-item>
          </n-descriptions>
        </n-card>
        <n-flex align="center" class="mt-10px">
          相对高度： <n-input-number v-model:value="relativeHeight" size="small" clearable :min="10" :max="500"
            class="w-150px" />
        </n-flex>
      </div>
      <template #action>
        <div>
          <n-button @click="handleMultiFlyCancel" size="small" class="mr-10px">取消</n-button>
          <!-- <n-button @click="nextPoint" size="small" class="mr-10px">下一个</n-button> -->
          <n-button :loading="flyHereLoadingM" size="small" type="info" @click="saveMultiFlyPaths(false)">确认</n-button>
        </div>
      </template>
    </n-modal>
    <!-- 目标点弹窗 -->
    <n-modal v-model:show="showTargetInfoModal" preset="dialog" title="目标点信息" :showIcon="false">
      <div>
        <n-card title="目标点">
          <n-descriptions label-placement="left" :column="1" separator=" ">
            <n-descriptions-item label="经纬度">经度： {{ currentTargetInfo.longitude.toFixed(6) }}<br />纬度： {{
              currentTargetInfo.latitude.toFixed(6) }}
            </n-descriptions-item>
            <!-- <n-descriptions-item label="海拔高度">{{ mouthPosition.latitude }} </n-descriptions-item> -->
          </n-descriptions>
        </n-card>
        <n-flex align="center" class="mt-10px">
          相对高度： <n-input-number disabled v-model:value="relativeHeight" size="small" clearable :min="10" :max="500"
            class="w-150px" />
        </n-flex>
      </div>
    </n-modal>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from 'cesium';
import { Ref, h, inject, onBeforeUnmount, onBeforeUpdate, onMounted, reactive, ref, toRefs, watch } from 'vue';
import { MouthPositionHandler, cesiumConfig, josiahProvider, labelProvider, position } from '@/config/cesium_config';
import redMarker from "@/assets/imgs/red.png"; // 假设红
import greenCircle from "@/assets/imgs/greenCircle.png"; // 浅绿背景
import marker from "@/assets/imgs/airport.png";
import alternate from "@/assets/imgs/alternate_0.png";
import drone_marker from "@/assets/imgs/drone-icon.png";
import { color, number } from 'echarts';
import { fetchAirportDeviceList, fetchDroneStatusList, fetchTrackInfo, takeOffToHere, saveMultiFlyPath } from '@/service/api';
import { Widget, DomUtil } from '@cesium-extends/common';
import ZoomController from '@cesium-extends/zoom-control';
import { MouseTooltip } from '@cesium-extends/tooltip';
import statusBar from '@/components/cesium/status-bar.vue'
import flyInfo from './modules/fly-info.vue';
import flyRecord from './modules/fly-record.vue';
import flyAnchor from './modules/fly-anchor.vue';
import droneVideo from './modules/drone-video.vue';
import dronePayload from './modules/drone-payload.vue';
import { useAirportStore } from '@/store/modules/airport';
import { useTaskStore } from '@/store/modules/task';
import { ChevronBackOutline, InformationCircleSharp, Locate } from '@vicons/ionicons5'
import { NButton, NotificationReactive, useModal, useNotification } from 'naive-ui';
import { useRoute } from 'vue-router';
import { useDeviceStore } from '@/store/modules/device';
// 可以手动控制云台的modeCode列表
const CanHandelPTZModeList = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', '16', '17', '18', '19'];
const saveModeList = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '14', '16', '17', '18', '19'];
const airportStore = useAirportStore();
const deviceStore = useDeviceStore();
const taskStore = useTaskStore();
const route = useRoute();
const socket = inject<{ chatMessage: Ref<string> }>('useSocket');  // 通过 inject 获取 provide 的 socket 对象
const notification = useNotification();
// cesium viewer 初始化
let viewer: Cesium.Viewer;
let handler: Cesium.ScreenSpaceEventHandler | null = null;
let polyline: Cesium.Entity | null = null;
const loaded = ref(false);
// 右键的弹出菜单
const popMenu = reactive({ x: 0, y: 0, show: false });
const popDialog = reactive({ x: 0, y: 0, show: false });
// 右键位置的经纬度
let mouthPosition = reactive({ longitude: 0, latitude: 0 })
let mouthPositionM = reactive({ longitude: 0, latitude: 0 })
// 机场列表
const airportDeviceList = reactive<Api.Airport.AirportDeviceInfo[]>([])
// 指点飞行弹出框
const showClickFlyModal = ref(false);
//jwd\
const relativeHeight = ref(120);
const flyHereLoading = ref(false); // 指点飞行任务下发等待
const flyHereLoadingM = ref(false); // 指点飞行任务下发等待
const flyInfoDockSN = ref();
const flyInfoDroneSN = ref();
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});
const taskID = ref();
const errorList = ref<any[]>([]); // 告警提醒列表
const flightMode = ref<'single' | 'multi' | null>(null); // single-指点飞行 multi-多点飞行
// 飞行轨迹信息
const trackPoints = ref<Api.AirLine.WayPointList[]>([]);
let droneEntity: Cesium.Entity; // 用于存储当前的无人机图标实体
// 拖拽相关状态
let isDragging = false; // 是否正在拖拽
let selectedTarget: { entity: Cesium.Entity; index: number } | null = null; // 选中的目标点
let originalCameraInputs: boolean; // 原始地图交互状态
// 初始化地图
const initCesium = () => {
  viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.scene.globe.depthTestAgainstTerrain = true;  //启用深度测试
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; //修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  loaded.value = true;

  // 修改图层
  // viewer.imageryLayers.removeAll();
  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider);

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置相机位置为当前定位点
  const longitude = deviceStore.dockInfo?.longitude || position.coords.longitude;
  const latitude = deviceStore.dockInfo?.latitude || position.coords.latitude;
  const lastPointCartesian = Cesium.Cartesian3.fromDegrees(longitude, latitude, 1200);
  // 设置视图以显示多段线
  viewer.camera.flyTo({
    destination: lastPointCartesian,
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(0), // 调整 pitch 值来改变视角的远近
      roll: 0.0
    },
    duration: 1.0, // 飞行时间，单位为秒
    complete: () => {
      // 可以在这里添加飞行完成后的操作
    }
  });
  // 加载禁飞区JSON文件并绘制禁飞区
  loadJSONAndDrawNoFlyZone();

  // 绑定鼠标右键点击事件
  initRightClickAction();
  // 新增：初始化拖拽逻辑（需在viewer创建后）
  initDragHandler();
}
// 初始化左键拖拽逻辑
const isCurrentTarget = ref(false)
function initDragHandler() {
  // 创建独立的事件处理器（避免覆盖原有右键/创建点事件）
  const dragHandler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

  // 鼠标按下（开始拖拽）
  dragHandler.setInputAction((event: any) => {
    // 仅在多点飞行模式下启用拖拽
    if (flightMode.value !== 'multi') return;

    const pickedObject = viewer.scene.pick(event.position);
    // 检查是否点击了目标点（通过description判断，需与创建时一致）
    if (pickedObject && pickedObject.id?.description._value === 'targetPoint') {
      // 找到对应的目标点索引
      const index = multiFlyTargetPoints.value.findIndex(
        (point) => point.entity.id === pickedObject.id.id
      );
      if (index === -1) return;
      isCurrentTarget.value = isPointInFlight(index) || multiFlyTargetPoints.value.length == 1;
      if (isCurrentTarget.value) {
        notification.warning({
          title: '操作提示',
          content: '不能拖动当前正在飞行的目标点',
          duration: 2000
        });
        return;
      }
      isDragging = true;
      selectedTarget = { entity: pickedObject.id, index };
      originalCameraInputs = viewer.scene.screenSpaceCameraController.enableInputs;
      // 禁用地图交互
      viewer.scene.screenSpaceCameraController.enableInputs = false;
    }
  }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

  // 鼠标移动（拖拽中）
  dragHandler.setInputAction((movement: any) => {
    if (!isDragging || !selectedTarget) return;

    const newPosition = viewer.scene.pickPosition(movement.endPosition);
    if (!newPosition) return;

    const cartographic = Cesium.Cartographic.fromCartesian(newPosition);
    const newLon = Cesium.Math.toDegrees(cartographic.longitude);
    const newLat = Cesium.Math.toDegrees(cartographic.latitude);

    // 更新实体位置和数据模型（同时更新 multiFlyPoints 和 multiFlyTargetPoints）
    selectedTarget.entity.position = new Cesium.ConstantPositionProperty(
      Cesium.Cartesian3.fromDegrees(newLon, newLat)
    );
    multiFlyTargetPoints.value[selectedTarget.index] = {
      ...multiFlyTargetPoints.value[selectedTarget.index],
      longitude: newLon,
      latitude: newLat,
      relativeHeight: multiFlyTargetPoints.value[selectedTarget.index].relativeHeight
    };
    // 同步更新 multiFlyPoints（确保新增点使用最新坐标）
    multiFlyPoints.value[selectedTarget.index] = {
      ...multiFlyPoints.value[selectedTarget.index],
      longitude: newLon,
      latitude: newLat,
      relativeHeight: multiFlyTargetPoints.value[selectedTarget.index].relativeHeight
    };
    // 全量重绘所有连线（清理旧连线并重新绘制）
    redrawAllConnectionLines();
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  // 鼠标释放（结束拖拽）
  dragHandler.setInputAction(() => {
    if (!isDragging || !selectedTarget) return;
    console.log('鼠标左键移动结束！！！', multiFlyTargetPoints.value)
    // 重新下发任务（可选：根据需求决定是否立即下发）
    let flag = isDragging
    isDragging = false;
    // 恢复地图交互
    viewer.scene.screenSpaceCameraController.enableInputs = originalCameraInputs;
    selectedTarget = null;
    if (!isCurrentTarget.value) {
      saveMultiFlyPaths(flag);
    }
  }, Cesium.ScreenSpaceEventType.LEFT_UP);
}
// 查询是否拖动当前目标点
function isPointInFlight(index: number): boolean {
  if (!airportDroneItem.value?.host || trackPoints.value.length === 0) {
    return false;
  }

  // 获取无人机当前位置
  const dronePos = Cesium.Cartesian3.fromDegrees(
    Number(airportDroneItem.value.host.longitude),
    Number(airportDroneItem.value.host.latitude)
  );

  // 获取所有未到达的目标点
  const unreachedPoints = multiFlyTargetPoints.value
    .map((point, i) => ({ ...point, index: i }))
    .filter(point => !point.isReached);

  if (unreachedPoints.length === 0) return false;

  // 找到最近的未到达目标点
  const nearestUnreachedPoint = unreachedPoints.reduce((prev, curr) => {
    const prevPos = prev.entity.position?.getValue();
    const currPos = curr.entity.position?.getValue();
    if (!prevPos) return curr;
    if (!currPos) return prev;
    return Cesium.Cartesian3.distance(dronePos, prevPos) <
      Cesium.Cartesian3.distance(dronePos, currPos) ? prev : curr;
  });
  console.log('未到达点', nearestUnreachedPoint)

  // 检查点击的点是否是最近的未到达点
  return nearestUnreachedPoint.index === index;
}
// 全量重绘所有连线（解决旧连线残留问题）
function redrawAllConnectionLines() {
  // 清理所有旧连线
  multiFlyLineEntities.value.forEach(entity => {
    if (entity) viewer.entities.remove(entity);
  });
  multiFlyLineEntities.value = [];

  // 重新绘制所有连线（基于最新的 multiFlyPoints 数据）
  for (let i = 0; i < multiFlyPoints.value.length; i++) {
    const currentPoint = multiFlyPoints.value[i];
    if (i === 0) {
      // 第一个点与无人机当前位置相连（或起点位置）
      const startPos = getDroneCurrentPosition();
      if (startPos) {
        const lineEntity = viewer.entities.add({
          polyline: {
            positions: [startPos, Cesium.Cartesian3.fromDegrees(currentPoint.longitude, currentPoint.latitude)],
            width: 5,
            material: Cesium.Color.LIGHTGRAY.withAlpha(0.7),
            clampToGround: true
          }
        });
        multiFlyLineEntities.value.push(lineEntity);
      }
    } else {
      // 后续点与前一个点相连
      const prevPoint = multiFlyPoints.value[i - 1];
      const lineEntity = viewer.entities.add({
        polyline: {
          positions: [
            Cesium.Cartesian3.fromDegrees(prevPoint.longitude, prevPoint.latitude),
            Cesium.Cartesian3.fromDegrees(currentPoint.longitude, currentPoint.latitude)
          ],
          width: 5,
          material: Cesium.Color.LIGHTGRAY.withAlpha(0.7),
          clampToGround: true
        }
      });
      multiFlyLineEntities.value.push(lineEntity);
    }
  }
}

// 获取无人机当前位置（辅助函数）
function getDroneCurrentPosition(): Cesium.Cartesian3 | null {
  if (trackPoints.value.length > 0) {
    const lastPoint = trackPoints.value[trackPoints.value.length - 1];
    return Cesium.Cartesian3.fromDegrees(Number(lastPoint.longitude), Number(lastPoint.latitude));
  } else if (airportDockItem.value?.host) {
    return Cesium.Cartesian3.fromDegrees(Number(airportDockItem.value.host.longitude), Number(airportDockItem.value.host.latitude));
  }
  return null;
}
function cancelDots() {
  console.log('急停/一键返航清除所有未完成路径和未完成目标点')
  clearAllPaths()
}
function cancel() {
  console.log('清除飞行轨迹+指点飞行/多点飞行轨迹')
  clearOldTrack();
  clearAllTargetsAndPaths()
  localStorage.removeItem('droneFlightState'); // 清除保存的飞行状态
}


// 清除旧的飞行轨迹
function clearOldTrack() {
  if (polyline) {
    viewer.entities.remove(polyline); // 移除旧的飞行轨迹
    polyline = null; // 重置 polyline 变量
  }
  trackPoints.value = []; // 清空轨迹点数组
}

// 获取航迹
async function getTaskTrack() {
  const { data, error } = await fetchTrackInfo({ sn: flyInfoDroneSN.value });
  if (error) return;
  trackPoints.value = data;
  drawTrack(); // 绘制新的飞行轨迹
}

/// 绘制航迹
function drawTrack() {
  if (polyline && polyline.polyline) {
    // 如果多段线已经存在，更新其位置
    polyline.polyline.positions = new Cesium.CallbackProperty(() => {
      return trackPoints.value.map(point => {
        const longitude = Number(point.longitude);
        const latitude = Number(point.latitude);
        return Cesium.Cartesian3.fromDegrees(longitude, latitude);
      });
    }, false);
  } else {
    // 如果多段线不存在，创建新的多段线
    polyline = viewer.entities.add({
      polyline: {
        positions: new Cesium.CallbackProperty(() => {
          return trackPoints.value.map(point => {
            const longitude = Number(point.longitude);
            const latitude = Number(point.latitude);
            return Cesium.Cartesian3.fromDegrees(longitude, latitude);
          });
        }, false),
        width: 5,
        clampToGround: true,
        material: Cesium.Color.YELLOWGREEN
      }
    });
  }

  // 根据无人机状态决定是否更新图标位置
  if (airportDroneItem.value?.host?.modeCode === '0') {
    // 待机状态，只绘制一次
    if (!Boolean(droneEntity)) {
      drawDroneIcon();
      // popDialog.show = false;
    }
  } else {
    // 在线状态，实时更新位置
    drawDroneIcon();
  }
}

// 绘制无人机图标
function drawDroneIcon() {
  // console.log("drawDroneIcon ~ trackPoints.value:", trackPoints.value.length)
  if (trackPoints.value.length === 0) return;

  const lastPoint = trackPoints.value[trackPoints.value.length - 1];
  const lastPointLongitude = Number(lastPoint.longitude);
  const lastPointLatitude = Number(lastPoint.latitude);

  // 删除旧的无人机图标
  if (Boolean(droneEntity)) {
    viewer.entities.remove(droneEntity);
  }

  // 添加新的无人机图标
  droneEntity = viewer.entities.add({
    id: `drone_icon_${flyInfoDroneSN.value}`, // 使用无人机编号作为id
    position: Cesium.Cartesian3.fromDegrees(lastPointLongitude, lastPointLatitude, 200),
    billboard: {
      image: drone_marker,
      scale: 0.16,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    description: 'droneicon',
  });
}

// 添加新的轨迹点处理函数
function handleTrackPoints(droneData: any) {
  // 根据无人机状态处理轨迹点
  if (droneData?.host?.modeCode === '0') {
    // 待机状态，只保留第一个轨迹点
    if (trackPoints.value.length > 1) return;
    trackPoints.value = [{
      longitude: Number(droneData.host.longitude),
      latitude: Number(droneData.host.latitude),
      altitude: Number(droneData.host.altitude)
    }];
  } else {
    // 非待机状态，添加新的轨迹点
    trackPoints.value.push({
      longitude: Number(droneData.host.longitude),
      latitude: Number(droneData.host.latitude),
      altitude: Number(droneData.host.altitude)
    });
  }
}

// 绑定鼠标右键点击事件
function initRightClickAction() {
  handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  handler.setInputAction((click: any) => {
    // console.log("handler.setInputAction ~ click:", click)
    // const position = mouthPosition(click, viewer);
    const mouthPositionHandler = new MouthPositionHandler();
    mouthPositionHandler.countMouthPosition(click, viewer);
    mouthPosition = mouthPositionHandler.position;
    popMenu.x = click.position.x + 95;
    popMenu.y = click.position.y;
    popMenu.show = true;
    // console.log("handler.setInputAction ~ position:", position)

  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

  handler.setInputAction((click: any) => {
    popMenu.show = false;
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

// 指点飞行弹窗展示
function handelFlyHereModal() {
  showClickFlyModal.value = !showClickFlyModal.value;
  popMenu.show = false
}

// 指点飞行请求
// 在组件data部分添加以下变量
const targetPointEntity = ref<Cesium.Entity | null>(null);
const plannedPathEntity = ref<Cesium.Entity | null>(null);
const actualPathEntity = ref<Cesium.Entity | null>(null);
const waypoints = ref<{ longitude: number, latitude: number }[]>([]); // 存储所有航点
const actualPathPositions = ref<Cesium.Cartesian3[]>([]); // 存储实际飞行路径点
const targetPoints = ref<{ entity: Cesium.Entity, longitude: number, latitude: number, isReached?: boolean, relativeHeight?: number }[]>([]);
const showTargetInfoModal = ref(false);
const currentTargetInfo = ref<{
  longitude: number,
  latitude: number,
  relativeHeight?: number // 新增字段
}>({ longitude: 0, latitude: 0 });
// 修改handelFlyHere函数
async function handelFlyHere() {
  if (flightMode.value === 'multi') {
    isMultiFlyMode.value = false;
    // clearAllTargetsAndPaths();
  }
  flightMode.value = 'single';
  // if (airportDroneItem.value?.host?.modeCode == '0') {
  //   cancel();
  // }
  flyHereLoading.value = true;
  // 发送飞行指令
  const { data } = await takeOffToHere(flyInfoDockSN.value, {
    targetHeight: relativeHeight.value,
    targetLatitude: mouthPosition.latitude,
    targetLongitude: mouthPosition.longitude
  });
  if (data) {
    // 清除所有旧的目标点和路径
    clearAllPaths();
    // 添加新目标点
    const newTarget = {
      longitude: mouthPosition.longitude,
      latitude: mouthPosition.latitude,
      isReached: false, // 初始化未到达状态
      relativeHeight: relativeHeight.value // 新增：保存当前高度
    };

    // 创建目标点实体并存储
    const targetEntity = viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(newTarget.longitude, newTarget.latitude),
      billboard: {
        image: redMarker,
        scale: 0.15, // 调整大小
        verticalOrigin: Cesium.VerticalOrigin.CENTER, // 中心对齐，避免悬浮
      },
      label: {
        // text: '未到达',
        font: '14px sans-serif',
        fillColor: Cesium.Color.WHITE,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 2,
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        pixelOffset: new Cesium.Cartesian2(0, -15)
      },
      description: 'targetPoint',
      properties: {
        type: 'targetPoint',
        flightMode: flightMode.value,
        relativeHeight: newTarget.relativeHeight
      }
    });
    // 只保留最新目标点
    targetPoints.value.push({ entity: targetEntity, ...newTarget });
    saveFlightState()
    // 更新计划路径
    updatePlannedPath();
  }
  flyHereLoading.value = false;
  handelFlyHereModal();
}

// 清除所有目标点和路径
function clearAllTargetsAndPaths() {
  console.log('清除所有目标点和路径')
  // 清除所有目标点
  targetPoints.value.forEach(point => {
    viewer.entities.remove(point.entity);
  });
  targetPoints.value = [];
  multiFlyTargetPoints.value.forEach(point => {
    viewer.entities.remove(point.entity);
  });
  multiFlyPoints.value = []
  multiFlyTargetPoints.value = [];
  // 清除计划路径
  if (plannedPathEntity.value) {
    viewer.entities.remove(plannedPathEntity.value);
    plannedPathEntity.value = null;
  }

  // 清除实际路径
  if (actualPathEntity.value) {
    viewer.entities.remove(actualPathEntity.value);
    actualPathEntity.value = null;
  }
  multiFlyLineEntities.value.forEach(entity => {
    if (entity) viewer.entities.remove(entity);
  });
}
function clearAllPaths() {
  console.log('清除所有未到达的目标点及路径')
  // 清除所有未到达的目标点及路径
  targetPoints.value.forEach(point => {

    if (!point.isReached) {
      viewer.entities.remove(point.entity); // 移除未到达目标点实体
    }
  });
  // 仅保留已到达的目标点
  targetPoints.value = targetPoints.value.filter(point => point.isReached);
  // 清除多点飞行目标点
  multiFlyTargetPoints.value.forEach((point, index) => {
    if (!point.isReached) {
      viewer.entities.remove(point.entity);
      multiFlyTargetPoints.value.splice(index, 1);
    }
  });
  // 清除多点飞行连线
  multiFlyLineEntities.value.forEach(entity => {
    if (entity) viewer.entities.remove(entity);
  });
  multiFlyLineEntities.value = [];
  // 清除计划路径
  if (plannedPathEntity.value) {
    viewer.entities.remove(plannedPathEntity.value);
    plannedPathEntity.value = null;
  }

  // 清除实际路径
  if (actualPathEntity.value) {
    viewer.entities.remove(actualPathEntity.value);
    actualPathEntity.value = null;
  }
}
// 更新计划路径函数
function updatePlannedPath() {
  if (targetPoints.value.length === 0) return;

  const currentTarget = targetPoints.value[targetPoints.value.length - 1];
  // **获取无人机当前位置（实时更新）**
  const currentPos = trackPoints.value.length > 0
    ? Cesium.Cartesian3.fromDegrees(Number(trackPoints.value[trackPoints.value.length - 1].longitude), Number(trackPoints.value[trackPoints.value.length - 1].latitude))
    : Cesium.Cartesian3.fromDegrees(Number(airportDockItem.value?.host?.longitude), Number(airportDockItem.value?.host?.latitude));

  // 清除旧计划路径
  if (plannedPathEntity.value) viewer.entities.remove(plannedPathEntity.value);

  // 绘制新计划路径（当前位置到目标点）
  plannedPathEntity.value = viewer.entities.add({
    polyline: {
      positions: [currentPos, Cesium.Cartesian3.fromDegrees(currentTarget.longitude, currentTarget.latitude)],
      width: 5,
      material: Cesium.Color.LIGHTGRAY.withAlpha(0.7),
      clampToGround: true
    }
  });
}

function onClickFlyHere() {
  handelFlyHereModal();
}
function submitCallback() {
  handelFlyHereModal();
}
function cancelCallback() {
  handelFlyHereModal();
}

// 生成机场范围椭圆的轮廓点
function computeEllipsePositions(
  center: { longitude: number; latitude: number },
  semiMajorAxis: number,
  semiMinorAxis: number,
  granularity: number
): Cesium.Cartesian3[] {
  const positions: Cesium.Cartesian3[] = [];
  for (let i = 0; i < 360; i += granularity) {
    const radians = Cesium.Math.toRadians(i);
    const x = semiMajorAxis * Math.cos(radians);
    const y = semiMinorAxis * Math.sin(radians);
    // 将米转换为经纬度
    const position = Cesium.Cartesian3.fromDegrees(center.longitude + x / 111319.9, center.latitude + y / 111319.9);
    positions.push(position);
  }
  // 闭合椭圆，使首尾相连
  positions.push(positions[0]);
  return positions;
}

// 绘制机场点位及范围
function drawAirportPoint(points: Api.Airport.MachineNestInfo[]) {
  points.forEach((point: Api.Airport.MachineNestInfo) => {
    const { longitude, latitude, distance, deviceName, landPointLongitude, landPointLatitude, deviceSn } = point;
    const center = { longitude, latitude }; // 明确指定中心点类型

    // 生成椭圆边界点
    const positions = computeEllipsePositions(center, distance, distance, 5);

    // 绘制机场范围（用Polygon模拟椭圆）
    viewer.entities.add({
      id: `airport_area_${deviceSn}`, // 使用机场编号作为id的一部分
      polygon: {
        hierarchy: positions,  // 多边形的顶点
        material: Cesium.Color.fromCssColorString('#1177fb').withAlpha(0.15), // 设置填充颜色
        perPositionHeight: false,  // 使多边形所有点贴地
      },
      description: 'airpoint',
    });

    // 绘制椭圆边框
    viewer.entities.add({
      id: `airport_border_${deviceSn}`,
      polyline: {
        positions: positions,  // 线的顶点位置，与多边形顶点一致
        width: 5,  // 边框宽度
        material: Cesium.Color.fromCssColorString('#1177fb'),  // 边框颜色
        clampToGround: true,  // 使边框贴地
      },
      description: 'airpointborder',
    });

    // 绘制机场图标
    viewer.entities.add({
      id: `airport_icon_${deviceSn}`,
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      billboard: {
        image: marker,
        scale: 0.13
      },
      description: 'airpointicon',
    });

    // 备降点图标
    if (landPointLatitude && landPointLongitude) {
      // 绘制机场备降点图标
      viewer.entities.add({
        id: `airport_alternate_${deviceSn}`,
        position: Cesium.Cartesian3.fromDegrees(landPointLongitude, landPointLatitude),
        billboard: {
          image: alternate,
          scale: 0.13
        },
        description: 'airpointbackupicon'
      });

      // 绘制机场备降点标签
      viewer.entities.add({
        position: Cesium.Cartesian3.fromDegrees(landPointLongitude, landPointLatitude),
        label: {
          text: `${deviceName}-备降点`,
          font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
          fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
          outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
          style: Cesium.LabelStyle.FILL_AND_OUTLINE,
          outlineWidth: 8.0,
          horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
          verticalOrigin: Cesium.VerticalOrigin.TOP,
          pixelOffset: new Cesium.Cartesian2(0, -50), // 向上偏移50像素
        },
        description: 'airpointtext',
      });
    }

    // 绘制机场标签
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      label: {
        text: deviceName,
        font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
        fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
        outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 8.0,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        pixelOffset: new Cesium.Cartesian2(0, -50), // 向上偏移50像素
      },
      description: 'airpointtext',
    });
  });
}

// 获取已激活的机场列表
async function getAirportDeviceList() {
  const { data } = await fetchAirportDeviceList();
  // 过滤掉不在线的机场
  const activeAirports = data.rows.filter((item: Api.Airport.MachineNestInfo) => item.status);
  Object.assign(airportDeviceList, activeAirports);
  drawAirportPoint(activeAirports);
}

// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
  try {
    const response = await fetch('/defaultNoFlyZone.json');
    const data = await response.json();
    // 绘制
    addPolygons(data.features);
  } catch (error) {
    console.error('Error loading JSON data:', error);
  }
};

// 绘制禁飞区
function addPolygons(features: any[]) {
  features.forEach(feature => {
    const polygon = feature.geometry.coordinates[0];
    const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    viewer.entities.add({
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString('red').withAlpha(0.35),
      }
    });
  });
};

// 获取无人机设备状态码列表
async function getDroneStatusList() {
  if (deviceStore.droneStatusList.length <= 0) {
    const { data, error } = await fetchDroneStatusList();
    if (error) {
      // popDialog.show = false;
      return
    };
    deviceStore.setDroneStatusList(data);
  }
}
const oneSn = ref('')
const handleClick = (event: MouseEvent) => {
  event.stopPropagation();
  console.log('Clicked', event);
};
let currentNotification: NotificationReactive | null = null;
// 修改 onProcessMessage 函数中的相关部分
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  if (biz_code === 'device_osd' && flyInfoDroneSN.value === sn) { // 本次任务无人机消息
    airportDroneItem.value = data;
    oneSn.value = sn
    handleTrackPoints(data); // 处理轨迹点
    drawTrack(); // 更新轨迹和图标
    updateActualPath(data); // 更新实际飞行路径
  } else if (biz_code === 'dock_osd' && flyInfoDockSN.value === sn) { // 本次任务机场消息
    airportDockItem.value = data;

  } else if (biz_code === 'device_hms' && (flyInfoDockSN.value === sn || flyInfoDroneSN.value === sn)) { // 设备告警
    const { host } = data;
    if (currentNotification) {
      currentNotification.destroy();
    }
    host.forEach((item: any) => {
      // 检查 errorList 中是否已存在相同 key 的告警
      const existingError = errorList.value.find(error => error.key === item.key);
      if (!existingError) {
        // 如果不存在相同的 key ，则添加到 errorList 并显示通知
        errorList.value.push(item);
        // 关闭之前的通知
        const title = `${item.deviceName} - ${item.messageZh}`;
        switch (item.level) {
          case 0:
            notification.destroyAll();
            notification.info({
              title: () => h(
                'div',
                {
                  onClick: handleClick,
                },
                [
                  h('div', title),
                ]
              ),
              duration: 10000,
            });
            break;
          case 1:
            notification.destroyAll();
            notification.warning({
              title: () => h(
                'div',
                {
                  onClick: handleClick,
                },
                [
                  h('div', title),
                ]
              ),
              duration: 10000
            });
            break;
          case 2:
            notification.destroyAll();
            notification.error({
              title: () => h(
                'div',
                {
                  onClick: handleClick,
                },
                [
                  h('div', title),
                ]
              ),
              duration: 10000
            });
            break;
          default:
            notification.destroyAll();
            notification.info({
              title: () => h(
                'div',
                {
                  onClick: handleClick,
                },
                [
                  h('div', title),
                ]
              ),
              duration: 10000
            });
        }
      }
    });
  } else if (biz_code === 'device_status' && sn === flyInfoDroneSN.value && data.host.status) { // 无人机上线
    // 删除无人机图标
    if (droneEntity) {
      viewer.entities.remove(droneEntity);
    }
    clearOldTrack()
  } else if (biz_code === 'device_status' && sn === flyInfoDroneSN.value && !data.host.status) { // 无人机下线
    console.log('xiaji---', data.host.status, droneEntity, polyline)
    // 删除无人机图标
    if (droneEntity) {
      viewer.entities.remove(droneEntity);
    }
    clearOldTrack()
  }
}


// 更新实际飞行路径
function updateActualPath(data: any) {
  // 通用轨迹更新逻辑
  handleTrackPoints(data);
  drawTrack();

  // 根据不同模式处理目标点和路径
  if (flightMode.value === 'single') {
    handleSingleFlyPathUpdate(data);
  } else if (flightMode.value === 'multi') {
    handleMultiFlyPathUpdate(data);
  }
}
// 新增指点飞行路径更新逻辑
function handleSingleFlyPathUpdate(data: any) {
  // 原有指点飞行的目标点到达检测逻辑
  if (targetPoints.value.length > 0) {
    const currentTarget = targetPoints.value[targetPoints.value.length - 1];
    const currentPos = Cesium.Cartesian3.fromDegrees(Number(data.host.longitude), Number(data.host.latitude));
    const targetPos = Cesium.Cartesian3.fromDegrees(currentTarget.longitude, currentTarget.latitude);
    const distance = Cesium.Cartesian3.distance(currentPos, targetPos);
    if (distance < 5 && !currentTarget.isReached) {
      currentTarget.isReached = true;
      if (currentTarget.entity && currentTarget.entity.billboard) {
        currentTarget.entity.billboard.image = greenCircle;
        currentTarget.entity.label.backgroundColor = Cesium.Color.fromCssColorString('#44BB00')
      }
      // 到达后清除已完成的计划路径
      if (plannedPathEntity.value) {
        viewer.entities.remove(plannedPathEntity.value);
        plannedPathEntity.value = null;
      }
    } else {
      // **未到达时，实时更新计划路径**
      updatePlannedPath();
    }
  }
}

// 清理所有多点飞行计划路径线段
function clearMultiFlyPlannedPaths() {
  multiFlyLineEntities.value.forEach(entity => {
    if (entity) viewer.entities.remove(entity);
  });
  multiFlyLineEntities.value = []; // 重置线段数组
}

// 多点飞行更新计划路径
function handleMultiFlyPathUpdate(data: any) {
  const currentPos = Cesium.Cartesian3.fromDegrees(
    Number(data.host.longitude),
    Number(data.host.latitude)
  );

  // 1. 获取所有未到达的目标点（按创建顺序）
  const unreachedPoints = multiFlyTargetPoints.value.filter(
    point => !point.isReached
  );

  if (unreachedPoints.length === 0) return; // 无未完成目标点，无需处理

  // 2. 找到最近的未到达目标点（按距离排序，取第一个）
  const sortedUnreachedPoints = unreachedPoints.sort((a, b) => {
    const aPos = a.entity.position?.getValue();
    const bPos = b.entity.position?.getValue();
    if (!aPos) return 1;
    if (!bPos) return -1;
    const distA = Cesium.Cartesian3.distance(currentPos, aPos);
    const distB = Cesium.Cartesian3.distance(currentPos, bPos);
    return distA - distB;
  });
  const nearestUnreachedPoint = sortedUnreachedPoints[0];
  const nearestIndex = multiFlyTargetPoints.value.indexOf(nearestUnreachedPoint);

  // 3. 提取从最近未到达点开始的所有未完成路径点（包括最近点）
  const pathPoints = multiFlyTargetPoints.value.slice(nearestIndex);

  // 4. 构建计划路径的坐标数组（当前位置 -> 最近点 -> 后续未到达点）
  const plannedPositions: Cesium.Cartesian3[] = [currentPos];
  pathPoints.forEach(point => {
    const pos = point.entity.position?.getValue();
    if (pos) plannedPositions.push(pos);
  });

  // 5. 清除旧计划路径
  clearMultiFlyPlannedPaths();

  // 6. 绘制新计划路径（连续多段线）
  if (plannedPositions.length >= 2) {
    const lineEntity = viewer.entities.add({
      polyline: {
        positions: plannedPositions,
        width: 5,
        material: Cesium.Color.LIGHTGRAY.withAlpha(0.7),
        clampToGround: true
      }
    });
    multiFlyLineEntities.value.push(lineEntity); // 保存路径实体以便后续清除
  }

  // 7. 到达检测（10米阈值）
  const nearestPos = nearestUnreachedPoint.entity.position?.getValue();
  if (nearestPos) {
    const distanceToNearest = Cesium.Cartesian3.distance(
      currentPos,
      nearestPos
    );
    if (distanceToNearest < 5 && !nearestUnreachedPoint.isReached) {
      nearestUnreachedPoint.isReached = true;
      nearestUnreachedPoint.entity.billboard.image = greenCircle;
      nearestUnreachedPoint.entity.label.backgroundColor = Cesium.Color.fromCssColorString('#44BB00')
      saveFlightState(); // 保存状态
    }
  }
}

function restoreFlightState() {
  const savedState = localStorage.getItem('droneFlightState');
  if (!savedState) return;

  const state = JSON.parse(savedState);
  if (!state.isFlying) {
    localStorage.removeItem('droneFlightState');
    return;
  }

  // ------------------- 恢复轨迹（已完成轨迹） -------------------
  trackPoints.value = state.completedTrack || [];
  drawTrack(); // 绘制已完成轨迹和无人机位置
  let index = 0
  // ------------------- 恢复目标点（区分模式） -------------------
  state.targetPoints.forEach((target: any) => {
    const { mode, longitude, latitude, isReached, relativeHeight, entityId } = target;
    const image = isReached ? greenCircle : redMarker; // 已完成用绿色，未完成用红色
    // 创建目标点实体
    const entity = viewer.entities.add({
      id: entityId, // 恢复唯一ID（避免重复）
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      billboard: {
        image,
        scale: 0.15,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
      },
      label: {
        text: index + 1 + '', // 标签内容为点的编号
        font: 'bold 14px sans-serif', // 字体加粗
        fillColor: Cesium.Color.WHITE, // 标签文本颜色为白色
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 标签水平对齐方式
        verticalOrigin: Cesium.VerticalOrigin.CENTER, // 标签垂直对齐方式
        backgroundColor: isReached ? Cesium.Color.fromCssColorString('#44BB00') : Cesium.Color.fromCssColorString('#24a4fe'), // 背景颜色
        showBackground: true, // 不显示默认背景
        backgroundPadding: new Cesium.Cartesian2(2, 2), // 修改边距
      },
      properties: {
        type: 'targetPoint',
        mode, // 保存模式用于点击事件处理
        relativeHeight
      }
    });

    // 根据模式存入对应的数组
    if (mode === 'single') {
      targetPoints.value.push({
        entity,
        longitude,
        latitude,
        isReached,
        relativeHeight
      });
    } else if (mode === 'multi') {
      multiFlyTargetPoints.value.push({
        entity,
        longitude,
        latitude,
        isReached,
        relativeHeight
      });
    }
    index++
  });

  // ------------------- 恢复未完成路径（根据模式） -------------------
  if (state.flightMode === 'single') {
    restoreSingleFlyPath(state.uncompletedTrack);
  } else if (state.flightMode === 'multi') {
    restoreMultiFlyPath(state.uncompletedTrack);
  }

  // 恢复飞行模式
  flightMode.value = state.flightMode;
}

// 辅助函数：恢复指点飞行未完成路径
function restoreSingleFlyPath(uncompletedTrack: any[]) {
  if (uncompletedTrack.length < 2) return; // 至少需要两个点才能成线

  plannedPathEntity.value = viewer.entities.add({
    polyline: {
      positions: uncompletedTrack.map(point =>
        Cesium.Cartesian3.fromDegrees(point.longitude, point.latitude)
      ),
      width: 5,
      material: Cesium.Color.LIGHTGRAY.withAlpha(0.7),
      clampToGround: true
    }
  });
}

// 辅助函数：恢复多点飞行未完成路径
function restoreMultiFlyPath(uncompletedTrack: any[]) {
  // 按顺序分组为线段（每两个点一组）
  for (let i = 0; i < uncompletedTrack.length; i += 2) {
    const start = uncompletedTrack[i];
    const end = uncompletedTrack[i + 1];
    if (start && end) {
      const lineEntity = viewer.entities.add({
        polyline: {
          positions: [
            Cesium.Cartesian3.fromDegrees(start.longitude, start.latitude),
            Cesium.Cartesian3.fromDegrees(end.longitude, end.latitude)
          ],
          width: 5,
          material: Cesium.Color.LIGHTGRAY.withAlpha(0.7),
          clampToGround: true
        }
      });
      multiFlyLineEntities.value.push(lineEntity);
    }
  }
}
// 监听Socket消息
watch(() => socket?.chatMessage?.value, (msg: any) => {
  // console.log('Received chat message:', msg);
  // 处理接收到的消息
  onProcessMessage(msg);
});

onMounted(() => {
  // 初始化地图
  initCesium();
  if (route.query.sn) { // 获取设备编码和下标
    flyInfoDockSN.value = route.query.sn;
    flyInfoDroneSN.value = route.query.dsn;
    taskID.value = route.query.tid;
  }
  // 获取已激活的机场列表
  getAirportDeviceList();
  getDroneStatusList();
  setTimeout(() => {
    if (airportDockItem.value.host?.taskName) { // 调取无人机本次任务飞行历史轨迹
      restoreFlightState(); // 恢复飞行状态
      getTaskTrack();
    }
  }, 2000);
  initLeftClickAction(); // 确保在这里调用
});

function saveFlightState() {
  // 判断是否需要保存状态（非待机状态且存在有效目标点或路径）
  const shouldSave =
    airportDroneItem.value?.host?.modeCode !== '0' ||
    (targetPoints.value.length > 0 || multiFlyTargetPoints.value.length > 0);

  if (!shouldSave) {
    localStorage.removeItem('droneFlightState');
    return;
  }

  // ------------------- 合并所有目标点（已完成+未完成） -------------------
  const allTargetPoints = [
    // 指点飞行目标点
    ...targetPoints.value.map(point => ({
      longitude: point.longitude,
      latitude: point.latitude,
      relativeHeight: point.relativeHeight,
      isReached: point.isReached,
      mode: 'single'
    })),
    // 多点飞行目标点
    ...multiFlyTargetPoints.value.map(point => ({
      longitude: point.longitude,
      latitude: point.latitude,
      relativeHeight: point.relativeHeight,
      isReached: point.isReached,
      mode: 'multi'
    }))
  ];

  // ------------------- 保存轨迹（已完成轨迹 + 未完成路径） -------------------
  const completedTrack = trackPoints.value; // 已完成的飞行轨迹
  const uncompletedTrack = getUncompletedTrack(); // 未完成的计划路径（根据模式生成）

  const dataToSave = {
    isFlying: true, // 标记为飞行中状态（非待机）
    completedTrack,
    uncompletedTrack,
    targetPoints: allTargetPoints, // 包含所有目标点（已完成+未完成）
    flightMode: flightMode.value // 保存当前飞行模式
  };

  console.log('Saving flight state:', dataToSave);
  localStorage.setItem('droneFlightState', JSON.stringify(dataToSave));
}

// 辅助函数：获取未完成的计划路径（根据当前模式）
function getUncompletedTrack(): { longitude: number; latitude: number }[] {
  const uncompleted: { longitude: number; latitude: number }[] = [];

  if (flightMode.value === 'single') {
    // 指点飞行：未完成的计划路径为当前目标点与当前位置的连线
    const plannedPath = plannedPathEntity.value;
    if (plannedPath && plannedPath.polyline) {
      const positions = plannedPath.polyline.positions;
      if (positions) {
        const actualPositions = typeof positions.getValue === 'function'
          ? positions.getValue()
          : positions;
        actualPositions.forEach((pos: Cesium.Cartesian3) => {
          const carto = Cesium.Cartographic.fromCartesian(pos);
          uncompleted.push({
            longitude: Cesium.Math.toDegrees(carto.longitude),
            latitude: Cesium.Math.toDegrees(carto.latitude)
          });
        });
      }
    }
  } else if (flightMode.value === 'multi') {
    // 多点飞行：未完成的所有连线（未到达点之间的路径）
    multiFlyLineEntities.value.forEach((lineEntity, index) => {
      if (lineEntity && lineEntity.polyline) {
        const positions = lineEntity.polyline.positions;
        if (positions) {
          const actualPositions = typeof positions.getValue === 'function'
            ? positions.getValue()
            : positions;
          actualPositions.forEach((pos: Cesium.Cartesian3, i: number) => {
            if (i === 0 && index === 0) return; // 跳过起点（无人机位置，无需保存）
            const carto = Cesium.Cartographic.fromCartesian(pos);
            uncompleted.push({
              longitude: Cesium.Math.toDegrees(carto.longitude),
              latitude: Cesium.Math.toDegrees(carto.latitude)
            });
          });
        }
      }
    });
  }

  return uncompleted;
}
// 监听无人机状态变化（如modeCode改变时保存状态）
watch(
  () => airportDroneItem.value?.host?.modeCode,
  (newMode, oldMode) => {
    if (oldMode) {
      localStorage.setItem('oldMode', oldMode);
    }
    if ((newMode === '0' && (oldMode === '10' || localStorage.getItem('oldMode') == '10')) && flyInfoDroneSN.value === oneSn.value) { // 降落/待机时清除数据
      console.log("降落清楚哈", newMode, oldMode)
      localStorage.removeItem('droneFlightState');
      cancel()
    }
  }
);
watch(
  () => airportDockItem.value.host?.modeCode,
  (newMode, oldMode) => {
    if (((!oldMode && newMode === '0' || oldMode && newMode !== '0') && flyInfoDroneSN.value === oneSn.value)) { // 非待机状态都需要保存
      saveFlightState(); // 开始飞行时保存初始状态
    }
  }
);
// 多点飞行弹出框
const showMultiFlyModal = ref(false);
// 多点飞行路径点
const multiFlyPoints = ref<{ longitude: number, latitude: number, relativeHeight: number }[]>([]);
// 标记是否处于多点飞行创建点状态
const isMultiFlyMode = ref(false);
// // 存储多点飞行中每个点的实体
// const multiFlyPointEntities = ref<(Cesium.Entity | null)[]>([]);
// 存储多点飞行中每条连线的实体
const multiFlyLineEntities = ref<(Cesium.Entity | null)[]>([]);
const billImage = 'data:image/png;base64,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'

// 绘制单点
function drawSinglePoint(point: { longitude: number; latitude: number; }, relativeHeight: number, index: number) {
  const entity = viewer.entities.add({
    id: index + 1 + '',
    position: Cesium.Cartesian3.fromDegrees(point.longitude, point.latitude),
    label: {
      text: index + 1 + '', // 标签内容为点的编号
      font: 'bold 14px sans-serif', // 字体加粗
      fillColor: Cesium.Color.WHITE, // 标签文本颜色为白色
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 标签水平对齐方式
      verticalOrigin: Cesium.VerticalOrigin.CENTER, // 标签垂直对齐方式
      backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'), // 背景颜色
      showBackground: true, // 不显示默认背景
      backgroundPadding: new Cesium.Cartesian2(2, 2), // 修改边距
    },
    billboard: {
      image: billImage, // 圆形背景图像的路径
      width: 35, // 圆形的宽度
      height: 35, // 圆形的高度
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER, // 将背景的垂直对齐方式也改为 CENTER
      scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5), // 根据距离缩放
    },
    description: 'targetPoint',
  });

  // multiFlyPointEntities.value.push(entity);
  // 存储目标点状态（独立于指点飞行的targetPoints）
  multiFlyTargetPoints.value.push({
    entity,
    longitude: point.longitude,
    latitude: point.latitude,
    relativeHeight: relativeHeight,
    isReached: false // 初始状态为未到达
  });
}

// 绘制连线
function drawConnectionLine(point: { longitude: number; latitude: number }) {
  let lineEntity: Cesium.Entity | null = null;
  if (multiFlyPoints.value.length === 1) {
    // 第一个点与无人机相连
    let currentLongitude, currentLatitude;
    if (trackPoints.value.length > 0) {
      const lastPoint = trackPoints.value[trackPoints.value.length - 1];
      currentLongitude = Number(lastPoint.longitude);
      currentLatitude = Number(lastPoint.latitude);
    } else if (airportDockItem.value?.host) {
      currentLongitude = Number(airportDockItem.value.host.longitude);
      currentLatitude = Number(airportDockItem.value.host.latitude);
    } else {
      console.warn("无法获取当前位置，跳过计划路径更新");
      return;
    }

    lineEntity = viewer.entities.add({
      polyline: {
        positions: [
          Cesium.Cartesian3.fromDegrees(currentLongitude, currentLatitude),
          Cesium.Cartesian3.fromDegrees(point.longitude, point.latitude)
        ],
        width: 5,
        material: Cesium.Color.LIGHTGRAY.withAlpha(0.7),
        clampToGround: true
      }
    });
  } else {
    // 后续点与上一个点相连
    const lastPoint = multiFlyPoints.value[multiFlyPoints.value.length - 2];
    lineEntity = viewer.entities.add({
      polyline: {
        positions: [
          Cesium.Cartesian3.fromDegrees(lastPoint.longitude, lastPoint.latitude),
          Cesium.Cartesian3.fromDegrees(point.longitude, point.latitude)
        ],
        width: 5,
        material: Cesium.Color.LIGHTGRAY.withAlpha(0.7),
        clampToGround: true
      }
    });
  }
  if (lineEntity) {
    multiFlyLineEntities.value.push(lineEntity);
  }
}
const mouthPositionHandlerM = new MouthPositionHandler();

// 修改绑定鼠标左键点击事件
function initLeftClickAction() {
  // 统一左键点击事件处理
  handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  let draggingEntity: { entity: Cesium.Entity; index: number } | null = null;
  handler.setInputAction((click: { position: any; }) => {
    const pickedObject = viewer.scene.pick(click.position);
    // 1. 优先处理目标点点击
    console.log('dianjiha--------', pickedObject.id.description._value === 'targetPoint'
      , pickedObject.id, position)
    if (pickedObject.id.description._value === 'targetPoint') {
      const position = pickedObject.id.position?.getValue(viewer.clock.currentTime);
      if (position) {
        const cartographic = Cesium.Cartographic.fromCartesian(position);
        const properties = pickedObject.id.properties?.getValue(viewer.clock.currentTime);

        currentTargetInfo.value = {
          longitude: Cesium.Math.toDegrees(cartographic.longitude),
          latitude: Cesium.Math.toDegrees(cartographic.latitude),
          relativeHeight: properties?.relativeHeight
        };

        if (currentTargetInfo.value.relativeHeight) {
          relativeHeight.value = currentTargetInfo.value.relativeHeight;
        }

        showTargetInfoModal.value = true;
        return; // 阻止后续处理
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
}

// 多点飞行相关的点击事件处理函数
function onClickMultiFly() {
  // if (flightMode.value === 'single') {
  //   clearAllTargetsAndPaths();
  // }
  flightMode.value = 'multi';
  isMultiFlyMode.value = true;
  mouthPositionM = mouthPosition
  popMenu.show = false;
  // notification.info({
  //   title: '多点飞行模式',
  //   content: '当前为多点飞行模式',
  //   duration: 3000
  // });
  handelMultiFlyModal()
}
function handleMultiFlyCancel() {
  handelMultiFlyModal();
}
// 多点飞行弹窗展示
function handelMultiFlyModal() {
  showMultiFlyModal.value = !showMultiFlyModal.value;
  console.log(5346, mouthPosition)
}
// 多点飞行专属目标点（独立于指点飞行）
const multiFlyTargetPoints = ref<{
  entity: Cesium.Entity;
  longitude: number;
  latitude: number;
  relativeHeight: number;
  isReached: boolean;
}[]>([]);
// 保存多点飞行路径并调用接口
async function saveMultiFlyPaths(isDragging: boolean) {
  //保存的时候push当下目标点
  let pathData: object = []
  if (isDragging) {
    console.log('拖-更新', multiFlyPoints)
    // 过滤掉已完成的点
    const uncompletedPoints = multiFlyTargetPoints.value.filter(point => !point.isReached);
    // 整理路径数据（转换为接口需要的格式）
    pathData = uncompletedPoints.map(point => ({
      longitude: point.longitude,
      latitude: point.latitude,
      height: point.relativeHeight // 相对高度作为目标高度
    }));
  } else {
    console.log('正常push', multiFlyPoints)
    multiFlyPoints.value.push({
      longitude: mouthPositionM.longitude,
      latitude: mouthPositionM.latitude,
      relativeHeight: 0
    });
    // 更新最后一个点的高度（用户在弹窗中输入的高度）
    const lastIndex = multiFlyPoints.value.length - 1;
    if (lastIndex >= 0) {
      // 更新最新点的高度为当前输入框的值
      multiFlyPoints.value[lastIndex].relativeHeight = relativeHeight.value;
      const newPoint = multiFlyPoints.value[lastIndex];
      drawSinglePoint(newPoint, multiFlyPoints.value[lastIndex].relativeHeight, lastIndex);
      drawConnectionLine(newPoint);
    }
    // 过滤掉已完成的点
    const uncompletedPoints = multiFlyTargetPoints.value.filter(point => !point.isReached);
    // 整理路径数据（转换为接口需要的格式）
    pathData = uncompletedPoints.map(point => ({
      longitude: point.longitude,
      latitude: point.latitude,
      height: point.relativeHeight // 相对高度作为目标高度
    }));
  }
  console.log(22222, pathData)
  flyHereLoadingM.value = true;
  try {
    // 调用保存路径接口
    const { data, error } = await saveMultiFlyPath(flyInfoDockSN.value, pathData);
    if (error) {
      throw new Error(error.message || '保存路径失败');
    }
    console.log('保存了----', multiFlyPoints.value)
    flyHereLoadingM.value = false;
    // 保存成功提示
    notification.success({
      title: '操作成功',
      content: `已保存 ${multiFlyPoints.value.length} 个目标点的飞行路径`,
      duration: 1000
    });
    // 关闭弹窗并退出多点飞行模式
    if (showMultiFlyModal.value == true) {
      handelMultiFlyModal();
    }
    isMultiFlyMode.value = false;
    // flightMode.value = 'multi'; // 明确当前模式为多点飞行
  } catch (error: any) {
    // 统一错误处理
    console.error('保存多点飞行路径失败:', error.message);
    notification.error({
      title: '操作失败',
      content: error.message || '保存路径时发生未知错误，请重试',
      duration: 1000
    });
  }
}
onBeforeUnmount(() => {
  localStorage.removeItem('droneFlightState');
  // 卸载鼠标右键点击事件
  if (handler) {
    handler.destroy();
    handler = null;
  }
})

</script>

<style scoped>
#container {
  padding: 0;
}

#status-bar {
  bottom: 65px;
  left: 0;
}

.n-modal .n-card,
.n-drawer .n-card {
  background-color: var(--n-border-color);
}

/* 调整 Toastr 样式以适应你的应用 */
.toast {
  font-family: '微软雅黑', Arial, sans-serif;
  font-size: 14px;
  opacity: 0 !important;
}

.toast-info {
  background-color: #1890ff;
}

.toast-warning {
  background-color: #faad14;
}

.toast-error {
  background-color: #ff4d4f;
}

/* 调整位置 */
.toast-top-right {
  top: 30px !important;
  right: 12px !important;
}
</style>
