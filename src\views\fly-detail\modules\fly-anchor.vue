<template>
  <div
    class="fixed zindex-2 flex items-center justify-between bg-gradient-to-b from-black to-gray-800 left-0 top-0 w-100% h-50px p-15px shadow-[0_10px_25px_-5px_rgba(0,0,0,0.5)]  ">
    <div class="flex items-center pl-20">
      <n-icon @click="handlePageBack" class="cursor-pointer" :component="ChevronBackOutline" size="24"
        color="#ffffff" />
      <div class="p-20px">
        <n-tag size="small" v-if="airportDockItem.host?.modeCode === '0'" type="success"> 空闲中 </n-tag>
        <n-tag size="small" v-else-if="airportDockItem.host?.modeCode === '1' || airportDockItem.host?.modeCode === '2'"
          type="warning"> {{ airportDockItem.host?.modeCode === '1' ? '现场调试' : '远程调试' }} </n-tag>
        <n-tag size="small" v-else-if="airportDockItem.host?.modeCode === '3'" type="error"> 固件升级中 </n-tag>
        <n-tag size="small" v-else-if="airportDockItem.host?.modeCode === '4'" type="info"> 作业中 </n-tag>
      </div>
      <n-text class="color-light text-20px">{{ airportDockItem?.host?.taskName || '暂无任务' }}</n-text>
    </div>

    <n-config-provider :theme="darkTheme" class="flex items-center">

      <div>
        <n-button v-if="CanHandelDroneModeList.some(mode => mode === airportDroneItem.host?.modeCode)"
          @click="getDroneControl" :loading="droneControlLoading" :type="droneControlStatus ? 'info' : 'default'"
          size="small" class="mr-15px">
          无人机
        </n-button>
        <n-button v-if="CanHandelPTZModeList.some(mode => mode === airportDroneItem.host?.modeCode)"
          @click="getPTZControl" :loading="ptzControlLoading" :type="ptzControlStatus ? 'info' : 'default'" size="small"
          class="mr-15px">
          云台
        </n-button>
      </div>

      <n-button v-if="CanHandelTakeOffBtnModeList.some(mode => mode === airportDroneItem.host?.modeCode)"
        @click="onClickReturnToBase" :loading="takeOffBtnLoading" type="warning" size="small" class="mr-15px">
        一键返航
      </n-button>
      <n-button
        v-if="airportDroneItem.host?.modeCode == '3' && (airportDockItem.host?.taskName && airportDockItem.host?.taskName != '手动飞行')"
        @click="onClickRecoveryJob" :loading="takeOffBtnLoading" type="warning" size="small" class="mr-15px">
        恢复航线
      </n-button>
      <n-button v-if="CanHandelDroneStopModeList.some(mode => mode === airportDroneItem.host?.modeCode)"
        @click="onClickToStop" :loading="stopLoading" type="error" size="small">
        急停
      </n-button>
    </n-config-provider>

    <n-config-provider :theme="darkTheme" class="absolute left-0 bottom-0 w-100% h-5px">
      <n-progress v-show="percentage > 0 && percentage < 100" type="line" status="success" :percentage="percentage"
        :show-indicator="false" :height="5" processing />
    </n-config-provider>
    <div
      class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 inline-block text-center h-65px w-full"
      style="z-index: -1;">
      <img src="@/assets/imgs/datav_title.png" alt="Title Image" class="h-full w-full" />
      <span
        class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-white text-8 pt-1  tracking-1"
        style="font-family: 'DouyuFont', sans-serif;">{{ authStore.sysInfo.webConfig.webName }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, Ref, computed, inject, onMounted, ref, watch } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import "@/assets/font/DouyuFont.woff2";
import { useRouterPush } from '@/hooks/common/router';
import { useAirportStore } from '@/store/modules/airport';
import { ChevronBackOutline, InformationCircleSharp, Ellipse } from '@vicons/ionicons5'
import { returnHome, takeOffToHere, emergencyStop, fetchFlightControl, fetchPayloadControl, recoveryJob } from '@/service/api';
import { useRoute } from 'vue-router';
import { useMessage, darkTheme } from 'naive-ui';
import { useTaskStore } from '@/store/modules/task';
import { useAuthStore } from '@/store/modules/auth';

const airportStore = useAirportStore();
const { routerBack } = useRouterPush();
const message = useMessage();
const taskStore = useTaskStore();
const authStore = useAuthStore();
const socket = inject<{ chatMessage: Ref<string> }>('useSocket');  // 通过 inject 获取 provide 的 socket 对象

defineOptions({ name: 'FlyAnchor' });

// 定义组件接受的属性
// const props = defineProps<{
// }>();

const emits = defineEmits(['focus-airport', 'cancelDots']);
const route = useRoute();

const flyInfoDockSN = ref(); // 机场sn
const flyInfoDroneSN = ref(); // 无人机sn
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});

const takeOffBtnLoading = ref(false); // 一键返航等待动画
const stopLoading = ref(false); // 急停等待动画
const percentage = ref(0); // 进度条百分比

const droneControlLoading = ref(false); // 无人机控制权等待动画
const ptzControlLoading = ref(false); // 云台控制权等待动画
const droneControlStatus = ref(false); // 无人机控制权获取状态 true-已获取 false-未获取
const ptzControlStatus = ref(false); // 云台控制权获取状态 true-已获取 false-未获取

// 可以手动控制无人机的modeCode列表
const CanHandelDroneModeList = ['3', '4', '5', '6', '7', '8', '9', '10', '16', '17', '18'];
// 可以手动控制云台的modeCode列表
const CanHandelPTZModeList = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '10', , '16', '17', '18', '19'];
// 可以手动控制无人机急停的modeCode列表
const CanHandelDroneStopModeList = ['3', '4', '5', '6', '7', '8', '9', '10', '16', '17', '18'];
// 可以一键返航的modeCode列表
const CanHandelTakeOffBtnModeList = ['3', '4', '5', '6', '7', '8', '17', '16', '18'];

// 获取/关闭无人机控制权
async function getDroneControl() {
  droneControlStatus.value = !droneControlStatus.value;
  const { error } = await fetchFlightControl(flyInfoDockSN.value);
  if (error) {
    droneControlStatus.value = !droneControlStatus.value;
    return
  }
  taskStore.updateDroneControlStoreStatus(droneControlStatus.value);
}

// 获取/关闭云台控制权
async function getPTZControl() {
  ptzControlStatus.value = !ptzControlStatus.value;
  taskStore.updatePTZControlStoreStatus(ptzControlStatus.value);
}

// 一键返航
async function onClickReturnToBase() {
  takeOffBtnLoading.value = true;
  emits('cancelDots')
  message.success('返航任务已下发，请等候设备响应');
  const { error } = await returnHome(flyInfoDockSN.value);
  takeOffBtnLoading.value = false;
}
//恢复航线
async function onClickRecoveryJob() {
  takeOffBtnLoading.value = true;
  message.success('请等候设备响应');
  const { error } = await recoveryJob(flyInfoDockSN.value);
  takeOffBtnLoading.value = false;
}
// 急停
async function onClickToStop() {
  stopLoading.value = true;
  const { error } = await emergencyStop(flyInfoDockSN.value);
  if (!error) {
    emits('cancelDots')
    message.success('急停任务已下发，请等候设备响应');
  }
  stopLoading.value = false;
}


// 处理socket消息
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  if (biz_code === 'device_osd' && flyInfoDroneSN.value === sn) { // 本次任务无人机消息
    airportDroneItem.value = data;
    // 无人机控制权状态更新
    if (Boolean(data.host.droneControl) != taskStore.droneControlStoreStatus) {
      taskStore.updateDroneControlStoreStatus(Boolean(data.host.droneControl));
    }
  } else if (biz_code === 'dock_osd' && flyInfoDockSN.value === sn) { // 本次任务机场消息
    airportDockItem.value = data;

  } else if (biz_code === 'drone_open' && flyInfoDockSN.value === sn) { // 无人机开机消息，使用的机场sn
    percentage.value = data.host.output.progress.percent;
  } else if (biz_code === 'wayline_progress' && flyInfoDroneSN.value === sn) { // 无人机任务进度消息
    percentage.value = Number(data.host.progress);
    // percentage.value = data.host.output.progress.percent;
  }
}

// 监听Socket消息
watch(() => socket?.chatMessage?.value, (msg) => {
  //
  // 处理接收到的消息
  onProcessMessage(msg);
});


function handlePageBack() {
  routerBack();
}

onMounted(() => {
  if (route.query.sn) { // 获取设备编码
    flyInfoDockSN.value = route.query.sn;
    flyInfoDroneSN.value = route.query.dsn;
    taskStore.updatePTZControlStoreStatus(ptzControlStatus.value);
  }
})
</script>

<style scoped></style>
