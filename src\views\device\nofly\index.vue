<template>
  <div class="h100vh relative">
    <div id="cesiumViewer" class="h100%"></div>
    <noflyInfo :noFlyList="noFlyList" :changeCurrentPointIndex="changeCurrentPointIndex"
      :changeModalStatus="changeAddNoFlyAreaModal" :currentAreaIndex="currentAreaIndex"
      :fetchAllNoFlyList="fetchAllNoFlyList" :changeCreate="changeCreate" :changeIsEdit="changeIsEdit">
    </noflyInfo>
    <noflyCreate :noFlyList="noFlyList" :currentAreaIndex="currentAreaIndex" v-if="showCreate"
      :changeCreate="changeCreate" ref="noflyCreateRef" :drawCirCle="drawCircle"
      :computeEllipsePositions="computeEllipsePositions" :fetchAllNoFlyList="fetchAllNoFlyList"
      :clearNowAndReDraw="clearNowAndReDraw" :drawPolygon="drawPolygon"
      :changeCurrentPointIndex="changeCurrentPointIndex" :changeIsEdit="changeIsEdit">
    </noflyCreate>
    <addNoFlyAreaModal :showModal="showaddNoFlyAreaModal" :changeModalStatus="changeAddNoFlyAreaModal"
      :confirmSelection="confirmSelection" />
  </div>
</template>

<script setup lang="ts">
import * as Cesium from 'cesium';
import { onBeforeUnmount, onMounted, ref, onBeforeMount, watch, nextTick } from 'vue';
import { cesiumConfig, josiahProvider, labelProvider } from '@/config/cesium_config';
import { fetchNoFlyList } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import noflyInfo from './modules/nofly-info.vue';
import noflyCreate from './modules/nofly-creat.vue';
import addNoFlyAreaModal from './modules/addNoFlyAreaModal.vue';
import { MouseTooltip } from '@cesium-extends/tooltip';
import { useDialog } from 'naive-ui'
const authStore = useAuthStore();
const dialog = useDialog();
const { sysInfo } = authStore;
// cesium viewer 初始化
let viewer: Cesium.Viewer;
const noFlyList = ref<Api.Device.NoflyInfo[]>([]); // 禁飞区列表
const points = ref<Cesium.Cartesian3[]>([]); // 存储标记的点
const allPositions = ref<{ lng: number; lat: number }[] | null>(null);
const centerPoint = ref<{ lng: number; lat: number } | null>(null);
const polygonEntity = ref<Cesium.Entity | null>(null);
const lineEntity = ref<Cesium.Entity | null>(null);
const drawLayer = ref<Cesium.CustomDataSource | null>(null);
const eventHandler = ref<Cesium.ScreenSpaceEventHandler | null>(null);
const moveHandler = ref<Cesium.ScreenSpaceEventHandler | null>(null);
const billImage = 'data:image/png;base64,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'
const noflyCreateRef = ref();
const isEdit = ref(false);
onBeforeMount(() => {
  // 获取禁飞区数据
  fetchAllNoFlyList();
});
onMounted(() => {
  // 初始化地图
  initCesium();
});
const initCesium = () => {
  viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; //修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none';
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK); // 取消双击事件
  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider); // 添加注记图层

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置中心点为当前定位点
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(parseFloat(sysInfo.webConfig.centerLongitude), parseFloat(sysInfo.webConfig.centerLatitude), 60000),
    orientation: {
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });

  drawLayer.value = new Cesium.CustomDataSource('noFlyAreaLayer');
  viewer.dataSources.add(drawLayer.value);

  // 添加点击事件监听
  viewer.screenSpaceEventHandler.setInputAction((click: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
    const picked = viewer.scene.pick(click.position);
    if (Cesium.defined(picked) && picked.id && picked.id.description) {
      const desc = picked.id.description;
      if (desc?.getValue() === 'noflyarea') {
        // 查找对应的禁飞区索引
        if (picked.id.id) {
          if (isEdit.value) {
            clickNoFlyArea(picked.id.id, zoomToNoFlyArea)
          } else {
            zoomToNoFlyArea(picked.id.id);
          }
        }
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  loadJSONAndDrawNoFlyZone();
}
// 获取禁飞区
const fetchAllNoFlyList = async () => {
  const { data } = await fetchNoFlyList();
  if (data) {
    noFlyList.value = data.rows;
    nextTick(renderNoFlyZones);
  }
}
// 点击禁飞区
const clickNoFlyArea = (id: string | number, callback: ((arg0: string | number) => void) | undefined) => {
  dialog.warning({
    title: '当前禁飞区存在未保存的修改，是否继续跳转',
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: () => {
      callback && callback(id);
      changeIsEdit(false);
    },
    onNegativeClick: () => {

    }
  })
}
// 选中禁飞区
const zoomToNoFlyArea = (id: string | number) => {
  clearNowAndReDraw();
  changeCreate(true)
  // 清除原有图形
  const targetEntity = drawLayer.value?.entities.getById(id.toString());
  if (targetEntity) {
    drawLayer.value?.entities.remove(targetEntity);
  }
  const borderEntity = drawLayer.value?.entities.getById(`${id}-border`);
  if (borderEntity) drawLayer.value?.entities.remove(borderEntity);
  nextTick(() => {
    currentAreaIndex.value = parseFloat(id.toString());
  });
  const currentArea = noFlyList.value.filter((item) => item.id == id)[0];
  const Positions = currentArea.geoJson.geometry.coordinates;
  const cartesian3Position = Positions.map(([lng, lat]) => {
    return Cesium.Cartesian3.fromDegrees(lng, lat, 0); // 高度设为0
  });
  if (currentArea.noFlyType === 2) {
    cartesian3Position.forEach(cartesian => {
      points.value.push(cartesian);
      addPoint(cartesian); // 调用原有添加点方法
    });
    startDrawing();
    updateLine();
    updatePolygon();
  } else {
    const allpositions = computeEllipsePositions(
      { longitude: currentArea.centerGeoJson.coordinates[0], latitude: currentArea.centerGeoJson.coordinates[1] },
      currentArea.radius || 0,
      currentArea.radius || 0,
      6
    );
    tempCircleEntity = viewer.entities.add({
      polygon: {
        hierarchy: allpositions,
        material: Cesium.Color.RED.withAlpha(0.3),
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
      },
      show: true
    });
    tempPolylineEntity = viewer.entities.add({
      polyline: {
        positions: allpositions,
        width: 5,
        material: Cesium.Color.fromCssColorString('#1177fb'),
        clampToGround: true
      },
      description: 'onflyareaborder',
      show: true
    });
    // 计算中心点
    const center = Cesium.Cartesian3.fromDegrees(currentArea.centerGeoJson.coordinates[0], currentArea.centerGeoJson.coordinates[1], 0);
    circleCenter.value = center;
  }

  zoomToPoints(cartesian3Position);
}
const currentAreaIndex = ref<number>(-1);
const changeCurrentPointIndex = (index: number) => {
  if (isEdit.value) {
    dialog.warning({
      title: '当前禁飞区存在未保存的修改，是否继续跳转',
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        currentAreaIndex.value = index;
        nextTick(() => {
          currentAreaIndex.value = index;
        });
        if (index >= 0) {
          zoomToNoFlyArea(index);
        }
        changeIsEdit(false);
      },
      onNegativeClick: () => {
      }
    })
  } else {
    currentAreaIndex.value = index;
    nextTick(() => {
      currentAreaIndex.value = index;
    });
    if (index >= 0) {
      zoomToNoFlyArea(index);
    }
  }
}
// 控制新增弹窗
const showaddNoFlyAreaModal = ref(false);
const showCreate = ref(false);
const changeAddNoFlyAreaModal = (show: boolean) => {
  showaddNoFlyAreaModal.value = show;
}
const changeCreate = (show: boolean) => {
  showCreate.value = show;
  if (!show) {
    isEdit.value = false;
  }
}
const changeIsEdit = (edit: boolean) => {
  isEdit.value = edit;
}
const confirmSelection = (selectedType: string) => {
  if (selectedType) {
    changeCreate(true);
    changeIsEdit(true);
    clearNowAndReDraw();
    if (selectedType === 'polygon') {
      startDrawing();
      nextTick(() => {
        noflyCreateRef.value?.changeNoFlyType('polygon');
      });

    } else if (selectedType === 'circle') {
      startDrawCircle()
      nextTick(() => {
        noflyCreateRef.value?.changeNoFlyType('circle');
      });

    }
  }
};
// 绑定地图事件，绘制多边形
const startDrawing = () => {
  eventHandler.value = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  // 左键添加点
  eventHandler.value.setInputAction((movement: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
    const cartesian = viewer.scene.pickPosition(movement.position);
    if (cartesian) {
      // 检查是否已经存在相同位置的点
      const pick = viewer.scene.pick(movement.position);
      if (pick && pick.id && pick.id.description && pick.id.description._value === 'waypoint') {
        return;
      }
      if (pick && pick.id && pick.id.description && pick.id.description._value === 'noflyarea') {
        return;
      }
      points.value.push(cartesian);
      addPoint(cartesian);
      updateLine();
      updatePolygon();
      returnPositionsAndCenter();
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  // 右键删除点
  eventHandler.value.setInputAction((movement: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
    const picked = viewer.scene.pick(movement.position);
    if (Cesium.defined(picked) && picked.id) {
      const entity = drawLayer.value?.entities.values.find(e => e.id === picked.id.id);
      if (entity) {
        const index = points.value.findIndex((pos) => pos.equals(entity.position!.getValue(Cesium.JulianDate.now())));
        if (index !== -1) {
          points.value.splice(index, 1);
          if (drawLayer.value) {
            drawLayer.value.entities.remove(entity);
          }
          updateLine();
          updatePolygon();
          returnPositionsAndCenter(index);
          drawLayer.value?.entities.values.forEach(entity => {
            if (entity.point) {
              const pos = entity.position!.getValue(Cesium.JulianDate.now());
              const currentIndex = points.value.findIndex(p => p.equals(pos));
              entity.label!.text = new Cesium.ConstantProperty((currentIndex + 1).toString());
            }
          });
        }
      }
    }
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

  // 鼠标拖拽点
  addDragEvent();

  // 鼠标悬停提示
  addHoverTooltip();
};
// 鼠标拖拽点的位置
const addDragEvent = () => {
  if (!eventHandler.value) {
    eventHandler.value = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  }
  let isDragging = false;
  let draggedEntity: Cesium.Entity | null = null;

  // 鼠标左键按下开始拖拽
  eventHandler.value?.setInputAction((movement: { position: Cesium.Cartesian2 }) => {
    const picked = viewer.scene.pick(movement.position);
    if (picked && picked.id && picked.id.description?.getValue() === "waypoint") {
      isDragging = true;
      draggedEntity = picked.id as Cesium.Entity;
      viewer.scene.screenSpaceCameraController.enableRotate = false; // 禁用场景旋转
      viewer.scene.screenSpaceCameraController.enableTranslate = false; // 禁用地图拖拽
    }
  }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

  // 鼠标移动更新点位置
  eventHandler.value.setInputAction((movement: { endPosition: Cesium.Cartesian2 }) => {
    if (isDragging && draggedEntity) {
      const cartesian = viewer.scene.pickPosition(movement.endPosition);
      if (cartesian && Cesium.defined(cartesian)) {
        const index = points.value.findIndex((pos) => pos.equals(draggedEntity!.position!.getValue(Cesium.JulianDate.now())));
        if (index !== -1) {
          points.value[index] = cartesian;
          draggedEntity.position = new Cesium.ConstantPositionProperty(cartesian);
          updateLine();
          updatePolygon();
          returnPositionsAndCenter();
        }
      }
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  // 鼠标左键释放结束拖拽
  eventHandler.value?.setInputAction(() => {
    if (isDragging) {
      isDragging = false;
      draggedEntity = null;
      viewer.scene.screenSpaceCameraController.enableRotate = true; // 恢复场景旋转
      viewer.scene.screenSpaceCameraController.enableTranslate = true; // 恢复地图拖拽
    }
  }, Cesium.ScreenSpaceEventType.LEFT_UP);
};
let mouseTooltip: MouseTooltip;
// 鼠标悬停提示
const addHoverTooltip = () => {
  moveHandler.value = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  mouseTooltip = new MouseTooltip(viewer, {
    content: "右键删除该点，拖拽移动该点",
    offset: [0, -5]
  });
  moveHandler.value.setInputAction((movement: { endPosition: Cesium.Cartesian2 }) => {
    const pick = viewer.scene.pick(movement.endPosition);
    if (pick && pick.id && pick.id.description && pick.id.description._value === 'waypoint') {
      mouseTooltip.showAt(movement.endPosition, "右键删除该点，拖拽移动该点");
    } else {
      mouseTooltip.hide();
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
};
// 添加点
const addPoint = (position: Cesium.Cartesian3) => {
  const pointIndex = points.value.length;
  const pointEntity = new Cesium.Entity({
    position,
    properties: {
      type: 'waypoint',
      index: pointIndex
    },
    point: {
      heightReference: Cesium.HeightReference.NONE,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    label: {
      text: pointIndex.toString(),
      font: 'bold 14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'),
      showBackground: true,
      backgroundPadding: new Cesium.Cartesian2(2, 2),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    billboard: {
      image: billImage,
      width: 30,
      height: 30,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    description: 'waypoint',
  });
  drawLayer.value?.entities.add(pointEntity);
}
// 更新线条
const updateLine = () => {
  if (lineEntity.value) {
    drawLayer.value?.entities.remove(lineEntity.value);
  }
  if (points.value.length > 1) {
    const positions = [...points.value];
    if (points.value.length > 2) {
      positions.push(points.value[0]); // 闭合多边形
    }
    lineEntity.value = new Cesium.Entity({
      polyline: {
        positions: positions,
        width: 4,
        material: Cesium.Color.RED,
        clampToGround: true,
      },
    });
    drawLayer.value?.entities.add(lineEntity.value);
  }
}
// 更新面积
const updatePolygon = () => {
  if (polygonEntity.value) {
    drawLayer.value?.entities.remove(polygonEntity.value);
  }
  if (points.value.length > 2) {
    polygonEntity.value = new Cesium.Entity({
      polygon: {
        hierarchy: new Cesium.PolygonHierarchy(points.value),
        material: Cesium.Color.RED.withAlpha(0.3),
      },
    });
    drawLayer.value?.entities.add(polygonEntity.value);
  }
}
// 返回所有点和中心点
const returnPositionsAndCenter = (runIndex?: number) => {
  const positions = points.value.map((cartesian) => {
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    return {
      lng: Cesium.Math.toDegrees(cartographic.longitude),
      lat: Cesium.Math.toDegrees(cartographic.latitude),
    };
  });
  const center = computeCenter(positions);
  allPositions.value = positions;
  centerPoint.value = center;
  noflyCreateRef.value.getPointData({ allPositions: positions, centerPoint: center }, runIndex);
}
// 计算中心点
const computeCenter = (positions: { lng: number; lat: number }[]): { lng: number; lat: number } | null => {
  if (positions.length === 0) return null;
  const sum = positions.reduce(
    (acc, pos) => {
      acc.lng += pos.lng;
      acc.lat += pos.lat;
      return acc;
    },
    { lng: 0, lat: 0 }
  );
  return {
    lng: parseFloat((sum.lng / positions.length).toFixed(8)),
    lat: parseFloat((sum.lat / positions.length).toFixed(8)),
  };
}
// ---------------------------------- 绘制圆 ---------------------------
const circleCenter = ref<Cesium.Cartesian3 | null>(null); // 圆心坐标
const circleEntity = ref<Cesium.Entity | null>(null); // 存储圆形实体引用
let tempCircleEntity: Cesium.Entity;
let tempPolylineEntity: Cesium.Entity;
let currentRadius = 0;
// 圆形绘制方法
const startDrawCircle = () => {
  currentRadius = 0;
  circleCenter.value = null;
  circleEntity.value = null;
  eventHandler.value = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);

  // 左键点击事件处理
  eventHandler.value.setInputAction((movement: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
    const cartesian = viewer.scene.pickPosition(movement.position);
    if (!cartesian) return;
    if (!circleCenter.value) {
      // 第一次点击设置圆心
      circleCenter.value = cartesian;
      // cartesian转换为经纬度
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(8);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(8);
      noflyCreateRef.value.setFormValue('circleCenter', [longitude, latitude]);
      addPoint1(cartesian);
    } else {
      eventHandler.value?.removeInputAction(Cesium.ScreenSpaceEventType.MOUSE_MOVE);
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  tempCircleEntity = viewer.entities.add({
    polygon: {
      hierarchy: new Cesium.CallbackProperty(() => [], false), // 初始空数据
      material: Cesium.Color.RED.withAlpha(0.3),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    show: false // 初始隐藏
  });
  tempPolylineEntity = viewer.entities.add({
    polyline: {
      positions: new Cesium.CallbackProperty(() => [], false),
      width: 5,
      material: Cesium.Color.fromCssColorString('#1177fb'),
      clampToGround: true
    },
    description: 'onflyareaborder',
    show: false
  });
  // 鼠标移动事件处理
  eventHandler.value.setInputAction((movement: Cesium.ScreenSpaceEventHandler.MotionEvent) => {
    if (!circleCenter.value) return;
    const cartesian = viewer.scene.pickPosition(movement.endPosition);
    if (!cartesian) return;
    currentRadius = Cesium.Cartesian3.distance(circleCenter.value, cartesian);
    noflyCreateRef.value.setFormValue('radius', currentRadius.toFixed(0));
    const cartographic = Cesium.Cartographic.fromCartesian(circleCenter.value);
    const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(8);
    const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(8);
    const allpositions = computeEllipsePositions({
      longitude: parseFloat(longitude),
      latitude: parseFloat(latitude),
    }, currentRadius, currentRadius, 6)
    if (tempCircleEntity && tempCircleEntity.polygon) {
      tempCircleEntity.polygon.hierarchy = new Cesium.CallbackProperty(() => {
        return new Cesium.PolygonHierarchy(allpositions);
      }, false);
      tempCircleEntity.show = true; // 显示实体
    }
    if (tempPolylineEntity && tempPolylineEntity.polyline) {
      tempPolylineEntity.polyline.positions = new Cesium.CallbackProperty(() => allpositions, false);
      tempPolylineEntity.show = true; // 显示实体
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
};
const drawCircle = (radius: number) => {
  if (!circleCenter.value) return;
  const cartographic = Cesium.Cartographic.fromCartesian(circleCenter.value);
  const longitude = Cesium.Math.toDegrees(cartographic.longitude).toFixed(8);
  const latitude = Cesium.Math.toDegrees(cartographic.latitude).toFixed(8);
  const allpositions = computeEllipsePositions(
    { longitude: parseFloat(longitude), latitude: parseFloat(latitude) },
    radius || 0,
    radius || 0,
    6
  );
  if (tempCircleEntity && tempCircleEntity.polygon) {
    tempCircleEntity.polygon.hierarchy = new Cesium.CallbackProperty(() => {
      return new Cesium.PolygonHierarchy(allpositions);
    }, false);
    tempCircleEntity.show = true; // 显示实体
  }
  if (tempPolylineEntity && tempPolylineEntity.polyline) {
    tempPolylineEntity.polyline.positions = new Cesium.CallbackProperty(() => allpositions, false);
    tempPolylineEntity.show = true; // 显示实体
  }
};
const drawPolygon = (positions: { longitude: number; latitude: number }, pointIndex: number) => {
  // 将经纬度转换为笛卡尔坐标
  const newCartesian = Cesium.Cartesian3.fromDegrees(
    positions.longitude,
    positions.latitude,
    0
  );

  // 更新坐标数组
  if (pointIndex >= 0 && pointIndex < points.value.length) {
    points.value.splice(pointIndex, 1, newCartesian);
  }
  const targetEntity = drawLayer.value?.entities.values.find(entity => {
    const props = entity.properties?.getValue(Cesium.JulianDate.now());
    return props?.type === 'waypoint' && props?.index === pointIndex + 1;
  });
  if (targetEntity) {
    targetEntity.position = new Cesium.ConstantPositionProperty(newCartesian);
  }

  updateLine();
  updatePolygon();
  // returnPositionsAndCenter(pointIndex);
}
// 计算禁飞区轮廓点
function computeEllipsePositions(
  center: { longitude: number; latitude: number },
  semiMajorAxis: number,
  semiMinorAxis: number,
  granularity: number
): Cesium.Cartesian3[] {
  const positions: Cesium.Cartesian3[] = [];
  for (let i = 0; i < 360; i += granularity) {
    const radians = Cesium.Math.toRadians(i);
    const x = semiMajorAxis * Math.cos(radians);
    const y = semiMinorAxis * Math.sin(radians);
    // 将米转换为经纬度
    const position = Cesium.Cartesian3.fromDegrees(center.longitude + x / 111319.9, center.latitude + y / 111319.9);
    positions.push(position);
  }
  // 闭合椭圆，使首尾相连
  positions.push(positions[0]);
  return positions;
}
const addPoint1 = (position: Cesium.Cartesian3) => {
  const pointEntity = new Cesium.Entity({
    position,
    point: {
      heightReference: Cesium.HeightReference.NONE,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    billboard: {
      image: billImage,
      width: 10,
      height: 10,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5),
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    },
    description: 'waypoint',
  });
  drawLayer.value?.entities.add(pointEntity);
}
// ---------------------------------- 绘制圆 ---------------------------
const renderNoFlyZones = () => {
  // 清空现有图形
  drawLayer.value?.entities.removeAll();
  noFlyList.value.forEach((zone, index) => {
    if (zone.geoJson.type === 'circle') {
      drawCircleZone(zone);
    } else if (zone.geoJson.type === 'polygon') {
      drawPolygonZone(zone);
    }
  });
};
const drawCircleZone = (zone: Api.Device.NoflyInfo) => {
  // 生成椭圆顶点坐标
  const allpositions = computeEllipsePositions(
    { longitude: zone.centerGeoJson.coordinates[0], latitude: zone.centerGeoJson.coordinates[1] },
    zone.radius || 0,
    zone.radius || 0,
    6
  );
  drawLayer.value?.entities.add({
    id: `${zone.id}`,
    polygon: {
      hierarchy: allpositions,
      material: Cesium.Color.RED.withAlpha(0.2),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    description: 'noflyarea'
  })
  drawLayer.value?.entities.add({
    id: `${zone.id}-border`,
    polyline: {
      positions: allpositions,
      width: 4,
      // material: Cesium.Color.RED.withAlpha(0.9),
      material: Cesium.Color.fromCssColorString('#FF4D4F'),
      clampToGround: true,
    },
    description: 'noflyareaborder',
  });
}
const drawPolygonZone = (zone: Api.Device.NoflyInfo) => {
  const hierarchy = new Cesium.PolygonHierarchy(
    zone.geoJson.geometry.coordinates.map((pos) =>
      Cesium.Cartesian3.fromDegrees(pos[0], pos[1], 0)
    )
  );
  const allpositions = [...zone.geoJson.geometry.coordinates, zone.geoJson.geometry.coordinates[0]].map((pos) =>
    Cesium.Cartesian3.fromDegrees(pos[0], pos[1], 0)
  );
  drawLayer.value?.entities.add({
    id: `${zone.id}`,
    polygon: {
      hierarchy: hierarchy,
      material: Cesium.Color.RED.withAlpha(0.2),
      outline: true,
      outlineColor: Cesium.Color.WHITE,
      outlineWidth: 2,
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    description: 'noflyarea'
  });
  console.log(zone.geoJson.geometry.coordinates)
  drawLayer.value?.entities.add({
    id: `${zone.id}-border`,
    polyline: {
      positions: allpositions,
      width: 4,
      // material: Cesium.Color.RED.withAlpha(0.9),
      material: Cesium.Color.fromCssColorString('#FF4D4F'),
      clampToGround: true,
    },
    description: 'noflyareaborder',
  });
};
// 自动调整视图范围
const zoomToPoints = (points: Cesium.Cartesian3[]): void => {
  if (points.length === 0) return;
  const positions = points.map(p => Cesium.Cartographic.fromCartesian(p));
  const longitudes = positions.map(p => Cesium.Math.toDegrees(p.longitude));
  const latitudes = positions.map(p => Cesium.Math.toDegrees(p.latitude));
  const averagelatitudes = latitudes.reduce((a: number, b: number) => a + b, 0) / latitudes.length;
  const xSpan = Math.max(...longitudes) - Math.min(...longitudes); // 经度跨度
  const ySpan = Math.max(...latitudes) - Math.min(...latitudes);   // 纬度跨度
  const maxSpan = Math.max(xSpan, ySpan);     // 取较大值

  const distance = maxSpan * 111319.9; // 根据经度计算距离
  const adjustedDistance = Math.max(distance * 2.5, 1000);
  // 设置相机视角
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(Math.max(...longitudes), averagelatitudes, distance * 2.2),
    orientation: {
      heading: Cesium.Math.toRadians(0),
      pitch: Cesium.Math.toRadians(-30),
      roll: 0
    },
    duration: 1.5,
    endTransform: Cesium.Matrix4.IDENTITY,
    maximumHeight: adjustedDistance
  });
};
// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
  try {
    const response = await fetch('/defaultNoFlyZone.json');
    const data = await response.json();
    // 绘制
    addPolygons(data.features);
  } catch (error) {
    console.error('Error loading JSON data:', error);
  }
}

// 绘制禁飞区
function addPolygons(features: any[]) {
  features.forEach(feature => {
    const polygon = feature.geometry.coordinates[0];
    const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    viewer.entities.add({
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString('red').withAlpha(0.35)
      }
    });
  });
}
// 清除所有事件以及图形
const clearAll = () => {
  if (eventHandler.value) {
    eventHandler.value.destroy();
    eventHandler.value = null;
  }
  if (moveHandler.value) {
    moveHandler.value.destroy();
    eventHandler.value = null;
    mouseTooltip.hide();
  }
  if (drawLayer.value) {
    drawLayer.value.entities.removeAll(); // 移除所有点线面
  }
  if (tempCircleEntity && viewer.entities.contains(tempCircleEntity)) {
    viewer.entities.remove(tempCircleEntity);
  }
  if (tempPolylineEntity && viewer.entities.contains(tempPolylineEntity)) {
    viewer.entities.remove(tempPolylineEntity);
  }
  points.value = []; // 重置点数组
  polygonEntity.value = null;
  lineEntity.value = null;
  if (viewer) {
    viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType?.MOUSE_MOVE);
  }
}
const clearNowAndReDraw = () => {
  if (eventHandler.value) {
    eventHandler.value.destroy();
    eventHandler.value = null;
  }
  if (mouseTooltip) {
    mouseTooltip.hide();
  }
  if (drawLayer.value) {
    drawLayer.value.entities.removeAll(); // 移除所有点线面
  }
  if (tempCircleEntity && viewer.entities.contains(tempCircleEntity)) {
    viewer.entities.remove(tempCircleEntity);
  }
  if (tempPolylineEntity && viewer.entities.contains(tempPolylineEntity)) {
    viewer.entities.remove(tempPolylineEntity);
  }
  points.value = []; // 重置点数组
  polygonEntity.value = null;
  lineEntity.value = null;
  if (viewer) {
    viewer.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType?.MOUSE_MOVE);
  }
  renderNoFlyZones();
}
onBeforeUnmount(() => {
  clearAll();
});

</script>

<style scoped>
#container {
  padding: 0;
}

.n-modal .n-card,
.n-drawer .n-card {
  background-color: var(--n-border-color);
}

.dashboard {
  width: 200px;
  height: 200px;
  position: fixed;
  bottom: 10px;
  left: calc(50% - 240px);
}
</style>
