import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import { useEventListener } from '@vueuse/core';
import { SetupStoreId } from '@/enum';
import { localStg } from '@/utils/storage';

// store/airport.ts
export const useAirportStore = defineStore(SetupStoreId.Airport, () => {
  const videoBoxes = ref<Api.Airport.AirportDeviceInfo[]>([]); // videoBoxes.value 是一个数组
  const airlineItem = ref<Api.AirLine.AirLineItem>({}); // 查看/修改某一个航线
  const deviceOSD = ref<Api.Airport.AirportDeviceInfo>(); // 设备OSD
  const deviceEmergency = ref<Record<string, Api.AirLine.AirLineItem>>({}); // 设备告警信息
  const chatMessage = reactive({ value: {} });

  // 添加视频框
  function addVideoBox(item: Api.Airport.AirportDeviceInfo) {
    if (videoBoxes.value.length < 4 && !videoBoxes.value.some(box => box.id === item.id)) {
      // 确保 item 有 id 属性
      videoBoxes.value.push(item);
    }
  }

  // 根据ID删除视频框
  function removeVideoBoxByID(id: number) {
    const index = videoBoxes.value.findIndex(item => item.id === id);
    if (index !== -1) {
      videoBoxes.value.splice(index, 1);
    }
  }
  
  // 删除全部视频框
  function removeAllVideoBox() {
    videoBoxes.value.length = 0;
  }
  
  // 编辑航线-存储航线数据
  function setAirLineItem(item: Api.AirLine.AirLineItem) {
    airlineItem.value = item;
  }

  // 存储设备告警
  function setDeviceEmergency(json:any) {
    const {sn, host} = json;
    deviceEmergency.value[sn] = host;
  }
  
  // 存储设备告警
  function setDeviceOSD(json:any) {
    // const {sn, host} = json;
    deviceOSD.value = json;
    // deviceEmergency.value[sn] = host;
  }

  function updateChatMessage(newMessage:any) {
    chatMessage.value = newMessage;
  }

  // // 编辑航线-获取航线数据
  // function (params: type) {

  // }

  

  return {
    videoBoxes,
    addVideoBox,
    removeVideoBoxByID,
    removeAllVideoBox,
    airlineItem,
    setAirLineItem,
    deviceEmergency,
    setDeviceEmergency,
    deviceOSD,
    setDeviceOSD,
    chatMessage, updateChatMessage
  };
});
