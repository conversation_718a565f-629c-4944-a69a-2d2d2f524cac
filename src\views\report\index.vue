<template>
  <div class="flex min-h-screen">
    <!-- <div  class="fixed inset-0 bg-dark z-9999 flex justify-center items-center">
      <div class="bg-white p-30px rounded-8px text-center max-w-80%">
        <NSpin size="large" />
        <div class="text-18px my-15px font-bold flex items-center justify-center gap-8px">
          正在生成报告: {{ progress }}%
        </div>
        <div class="text-gray-600">当前处理第 {{ currentPage }}/{{ totalPages }} 页</div>
      </div>
    </div> -->

    <div class="w-300px p-20px bg-dark-700 text-gray-200 border-r border-dark-100 flex flex-col gap-20px
                overflow-y-auto custom-scrollbar">
      <div class="bg-white/8 p-15px rounded-6px shadow-lg shadow-black/30">
        <h3 class="mt-0 mb-15px text-white text-16px border-b border-white/15 pb-8px">标题页设置</h3>
        <div class="mb-12px">
          <label class="block mb-5px text-14px text-gray-400">报告标题：</label>
          <input
            v-model="titleSettings.reportTitle"
            type="text"
            class="w-full px-12px py-8px border border-gray-700 rounded-4px bg-white/5 text-white text-14px
                   focus:outline-none focus:border-gray-500 focus:shadow-outline-gray-500/30 focus:bg-white/10"
            placeholder="请输入报告标题"
          />
        </div>
        <div class="mb-12px">
          <label class="block text-14px text-gray-400">
            <input v-model="titleSettings.showLogo" type="checkbox" class="mr-8px accent-gray-500" />
            显示Logo
          </label>
        </div>
      </div>

      <div class="bg-white/8 p-15px rounded-6px shadow-lg shadow-black/30">
        <h3>报告设置</h3>
        <div class="mb-12px">
          <label class="block text-14px text-gray-400">
            <input v-model="watermarkEnabled" type="checkbox" class="mr-8px accent-gray-500" />
            启用水印
          </label>
        </div>
        <div v-if="watermarkEnabled" class="mb-12px">
          <label class="block mb-5px text-14px text-gray-400">水印文字：</label>
          <input
            v-model="customWatermark"
            type="text"
            class="w-full px-12px py-8px border border-gray-700 rounded-4px bg-white/5 text-white text-14px
                   focus:outline-none focus:border-gray-500 focus:shadow-outline-gray-500/30 focus:bg-white/10"
            placeholder="请输入水印文字"
          />
        </div>
        <div class="mb-12px">
          <label class="block text-14px text-gray-400">
            <input v-model="enableTempPoints" type="checkbox" class="mr-8px accent-gray-500" />
            显示测温点位
          </label>
        </div>
      </div>

      <div class="bg-white/8 p-15px rounded-6px shadow-lg shadow-black/30">
        <h3>操作</h3>
        <!-- <button
          :disabled="generating"
          class="w-full p-10px mb-10px bg-gray-700 text-white border-none rounded-4px cursor-pointer
                 transition-all duration-200 ease hover:bg-gray-600 hover:translate-y--1px
                 disabled:bg-gray-500! disabled:cursor-not-allowed disabled:opacity-70"
          @click="downloadReport"
        >
          <span v-if="!generating">下载报告</span>
          <span v-else class="flex items-center justify-center gap-8px">
            <NSpin size="small" />
            正在生成 ({{ progress }}%)
          </span>
        </button> -->
        <n-button :loading="Boolean(generating)" type="info" block class="mb-10px rounded-4px" @click="downloadReport" >
          {{ !generating ? '下载报告' : `正在生成 (${progress}%)` }}
        </n-button>
         <!-- <n-button type="info">      Info    </n-button> -->
        <div v-if="generating" class="mt-10px text-12px text-gray-400 text-center">
          报告较大，生成可能需要一些时间，请勿退出，请耐心等待...
        </div>
      </div>
    </div>

    <!-- 飞行报告 -->
    <div id="report-container" class="flex-1 p-20px overflow-y-auto font-sans text-gray-800 bg-black print:hidden">
      <div class="pdfpage w-210mm! h-297mm! mx-auto bg-white relative p-15mm box-border
                  page-break-after-always overflow-hidden shadow-lg shadow-black/5 print:visible">
        <div v-if="watermarkEnabled" class="watermark absolute inset-0 pointer-events-none z-1000">
          <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold top-20% left-10%">
            {{ customWatermark }}
          </div>
          <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
            {{ customWatermark }}
          </div>
          <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold bottom-20% right-10%">
            {{ customWatermark }}
          </div>
        </div>
        <div class="absolute right-15mm bottom-10mm text-12px text-gray-600">第 1 页</div>
        <div v-if="titleSettings.showLogo" class="absolute top-15mm left-15mm h-30mm w-auto z-100">
          <img :src="titleSettings.logoUrl" alt="LOGO" class="h-full" />
        </div>
        <div class="text-center mt-100mm text-36px font-bold text-gray-900 drop-shadow-md drop-shadow-black/10">
          {{ titleSettings.reportTitle }}
        </div>
        <div class="text-center mt-50mm text-24px text-gray-700">
          {{ hasData ? recordData.jobName : '无任务数据' }}
        </div>
        <div class="absolute bottom-15mm left-0 right-0 text-center text-18px font-bold text-gray-900 px-15mm
                    flex flex-col gap-5px">
          <div class="flex justify-center items-center gap-5px">
            <span class="font-bold">操作员：</span>
            <span>{{ hasData ? recordData.createName : '无数据' }}</span>
          </div>
          <div class="flex justify-center items-center gap-5px">
            <span class="font-bold">报告时间：</span>
            <span>{{ hasData ? recordData.operatorTime : new Date().toLocaleString() }}</span>
          </div>
        </div>
      </div>

      <div class="pdfpage w-210mm! h-297mm! mx-auto bg-white relative p-15mm box-border
                  page-break-after-always overflow-hidden shadow-lg shadow-black/5 print:visible">
        <div v-if="watermarkEnabled" class="absolute inset-0 pointer-events-none z-1000">
          <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold top-20% left-10%">
            {{ customWatermark }}
          </div>
          <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
            {{ customWatermark }}
          </div>
          <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold bottom-20% right-10%">
            {{ customWatermark }}
          </div>
        </div>
        <div class="absolute right-15mm bottom-10mm text-12px text-gray-600">第 2 页</div>
        <div class="text-18px font-bold mt-0 mb-5mm border-b border-gray-300 pb-2mm text-gray-900">基本信息</div>
        <div class="grid grid-cols-2 gap-3mm mb-10mm">
          <div class="font-bold text-right text-gray-600">所属组织：</div>
          <div>{{ recordData.workspaceName }}</div>

          <div class="font-bold text-right text-gray-600">作业模式：</div>
          <div>{{ recordData.jobType }}</div>

          <div class="font-bold text-right text-gray-600">航线名称：</div>
          <div>{{ recordData.flightName }}</div>

          <div class="font-bold text-right text-gray-600">里程|时间：</div>
          <div>{{ recordData.flyMessage }}</div>

          <div class="font-bold text-right text-gray-600">开始时间：</div>
          <div>{{ recordData.execTime }}</div>

          <div class="font-bold text-right text-gray-600">结束时间：</div>
          <div>{{ recordData.endTime }}</div>
        </div>

        <div class="text-18px font-bold mt-0 mb-5mm border-b border-gray-300 pb-2mm text-gray-900">设备信息</div>
        <div class="grid grid-cols-2 gap-3mm mb-10mm">
          <div class="font-bold text-right text-gray-600">机库厂家：</div>
          <div>DJI大疆</div>
          <div class="font-bold text-right text-gray-600">机库型号：</div>
          <div>{{ recordData.dockType }}</div>
          <div class="font-bold text-right text-gray-600">机库名称：</div>
          <div>{{ recordData.dockName }}</div>
          <div class="font-bold text-right text-gray-600">无人机厂家：</div>
          <div>DJI大疆</div>
          <div class="font-bold text-right text-gray-600">无人机型号：</div>
          <div>{{ recordData.droneType }}</div>
          <div class="font-bold text-right text-gray-600">无人机名称：</div>
          <div>{{ recordData.droneName }}</div>
          <div class="font-bold text-right text-gray-600">载荷类型：</div>
          <div>摄像头</div>
          <div class="font-bold text-right text-gray-600">载荷厂家：</div>
          <div>DJI大疆</div>
          <div class="font-bold text-right text-gray-600">载荷型号：</div>
          <div>{{ recordData.payloadName }}</div>
        </div>
      </div>

      <template v-if="hasTempData && hasData">
        <div v-for="page in totalTempPages" :key="'temp-page-' + page" class="pdfpage w-210mm! h-297mm! mx-auto bg-white relative p-15mm box-border
            page-break-after-always overflow-hidden shadow-lg shadow-black/5 print:visible">
          <div v-if="watermarkEnabled" class="watermark absolute inset-0 pointer-events-none z-1000">
            <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold top-20% left-10%">
              {{ customWatermark }}
            </div>
            <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
              {{ customWatermark }}
            </div>
            <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold bottom-20% right-10%">
              {{ customWatermark }}
            </div>
          </div>
          <div class="absolute right-15mm bottom-10mm text-12px text-gray-600">第 {{ page + 2 }} 页</div>

          <div v-if="page === 1" class="text-18px font-bold mt-0 mb-5mm border-b border-gray-300 pb-2mm text-gray-900">温度检测数据</div>
          <div v-else class="text-18px font-bold mt-0 mb-5mm border-b border-gray-300 pb-2mm text-gray-900">温度检测数据 (续 {{ page }})</div>

          <table class="w-full border-collapse text-12px mt-5mm table-fixed page-break-inside-avoid">
            <colgroup>
              <col class="w-110px" />
              <col class="w-80px" />
              <col class="w-80px" />
              <col class="w-80px" />
              <col class="w-80px" />
              <col class="w-80px" />
              <col class="w-170px" />
            </colgroup>
            <thead>
              <tr>
                <th class="bg-gray-100 p-8px text-center sticky top-0 border border-gray-300">测温点</th>
                <th class="bg-gray-100 p-8px text-center sticky top-0 border border-gray-300">时间</th>
                <th class="bg-gray-100 p-8px text-center sticky top-0 border border-gray-300">经度</th>
                <th class="bg-gray-100 p-8px text-center sticky top-0 border border-gray-300">纬度</th>
                <th class="bg-gray-100 p-8px text-center sticky top-0 border border-gray-300">最高温度</th>
                <th class="bg-gray-100 p-8px text-center sticky top-0 border border-gray-300">最低温度</th>
                <th class="bg-gray-100 p-8px text-center sticky top-0 border border-gray-300">温度阈值</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in getTempPageData(page)" :key="index" class="">
                <td class="p-6px border border-gray-300 text-left break-all whitespace-normal hyphens-auto min-w-0">
                  {{ item.tips }}
                </td>
                <td class="p-6px border border-gray-300 text-center">{{ item.createTime }}</td>
                <td class="p-6px border border-gray-300 text-center">{{ item.longitude.toFixed(6) }}</td>
                <td class="p-6px border border-gray-300 text-center">{{ item.latitude.toFixed(6) }}</td>
                <td class="p-6px border border-gray-300 text-center">{{ item.maxValue }}℃</td>
                <td class="p-6px border border-gray-300 text-center">{{ item.minValue }}℃</td>
                <td class="p-6px border border-gray-300 text-center">{{ item.threshold }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>

      <template v-if="hasImageData && hasData">
        <div v-for="(page, pageIndex) in totalImagePages" :key="'image-page-' + pageIndex" class="pdfpage w-210mm! h-297mm! mx-auto bg-white relative p-15mm box-border
            page-break-after-always overflow-hidden shadow-lg shadow-black/5 print:visible">
          <div v-if="watermarkEnabled" class="watermark absolute inset-0 pointer-events-none z-1000">
            <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold top-20% left-10%">
              {{ customWatermark }}
            </div>
            <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
              {{ customWatermark }}
            </div>
            <div class="watermark-item absolute text-40px text-black/8 rotate--30 font-bold bottom-20% right-10%">
              {{ customWatermark }}
            </div>
          </div>
          <div class="absolute right-15mm bottom-10mm text-12px text-gray-600">第 {{ imageStartPage + pageIndex }} 页</div>

          <div v-if="pageIndex === 0" class="text-18px font-bold mt-0 mb-5mm border-b border-gray-300 pb-2mm text-gray-900">回传影像</div>
          <div v-else class="text-18px font-bold mt-0 mb-5mm border-b border-gray-300 pb-2mm text-gray-900">回传影像 (续{{ pageIndex + 1 }})</div>

          <template
            v-for="(item, index) in recordData.irRecordPhotoList?.slice(
              pageIndex * imagesPerPage,
              (pageIndex + 1) * imagesPerPage
            )"
            :key="item.point + '-' + index"
          >
            <div class="grid grid-cols-2 gap-5mm mb-5mm text-14px">
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">采集点</span>
                <span>{{ item.tips }}</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">采集时间</span>
                <span>{{ item.createTime }}</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">经度</span>
                <span>{{ item.longitude }}</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">纬度</span>
                <span>{{ item.latitude }}</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">最高温度</span>
                <span>{{ item.maxValue }}℃</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">最低温度</span>
                <span>{{ item.minValue }}℃</span>
              </div>
            </div>

            <div class="image-row flex justify-between mb-neg-5mm" :class="{ 'grid grid-cols-3 gap-7mm': item.imageList.length === 3, 'gap-5mm': item.imageList.length !== 3 }">
              <div v-for="(img, imgIndex) in item.imageList" :key="img"
                class="flex flex-col w-1/2-gap-5mm mb-5mm h-80mm" :class="{ 'w-1/3-gap-7mm': item.imageList.length === 3 }">
                <div class="w-full h-max relative inline-block ">
                  <!-- <img
                    :src="img.image"
                    :alt="'影像-' + (imgIndex + 1)"
                    class="w-full h-full max-h-120mm object-contain rounded-4px shadow-md shadow-black/10 border border-gray-200 bg-gray-50 image-loading-anim"
                    @error="handleImageError"
                  /> -->
                  <n-image width="100%" :src="img.image" preview-disabled	class="shadow-md shadow-black/10">
                    <template #error>
                      <n-icon :size="100" color="lightGrey">
                        <ImageOutlineIcon />
                      </n-icon>
                    </template>
                  </n-image>
                  <div
                    class="absolute inset-0 pointer-events-none"
                    v-if="enableTempPoints && img.fileName.endsWith('_T.jpeg') && (item.maxPoint || item.minPoint)"
                  >
                    <div
                      v-if="enableTempPoints && item.maxPoint"
                      class="absolute w-16px h-16px rounded-full box-border shadow-white/70 shadow-[0_0_0_2px] border-red-500 border-3px bg-transparent marker-pulse"
                      :style="{
                        left: `${item.maxPoint.x * 100}%`,
                        top: `${item.maxPoint.y * 100}%`,
                        transform: 'translate(-50%, -50%)',
                      }"
                    ></div>
                    <div
                      v-if="enableTempPoints && item.minPoint"
                      class="absolute w-16px h-16px rounded-full box-border shadow-white/70 shadow-[0_0_0_2px] border-blue-500 border-3px bg-transparent marker-pulse"
                      :style="{
                        left: `${item.minPoint.x * 100}%`,
                        top: `${item.minPoint.y * 100}%`,
                        transform: 'translate(-50%, -50%)',
                      }"
                    ></div>
                  </div>
                </div>
                <div class="text-center text-10px text-gray-600 mt-2mm">
                  {{ img.fileName }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </template>

      <template v-if="hasSimpleImageData && hasData">
        <div v-for="(page, pageIndex) in totalSimpleImagePages" :key="'image-page-' + pageIndex" class="pdfpage w-210mm! h-297mm! mx-auto bg-white relative p-15mm box-border
            page-break-after-always overflow-hidden shadow-lg shadow-black/5 print:visible">
          <div v-if="watermarkEnabled" class="watermark absolute inset-0 pointer-events-none z-1000">
            <div class="absolute text-40px text-black/8 rotate--30 font-bold top-20% left-10%">
              {{ customWatermark }}
            </div>
            <div class="absolute text-40px text-black/8 rotate--30 font-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2">
              {{ customWatermark }}
            </div>
            <div class="absolute text-40px text-black/8 rotate--30 font-bold bottom-20% right-10%">
              {{ customWatermark }}
            </div>
          </div>
          <div class="absolute right-15mm bottom-10mm text-12px text-gray-600">第 {{ imageSimpleStartPage + pageIndex }} 页</div>

          <div v-if="pageIndex === 0" class="text-18px font-bold mt-0 mb-5mm border-b border-gray-300 pb-2mm text-gray-900">回传影像</div>
          <div v-else class="text-18px font-bold mt-0 mb-5mm border-b border-gray-300 pb-2mm text-gray-900">回传影像 (续{{ pageIndex + 1 }})</div>

          <template
            v-for="(item, index) in recordData.recordPhotoList?.slice(
              pageIndex * imagesSimplePerPage,
              (pageIndex + 1) * imagesSimplePerPage
            )"
            :key="item.createTime + '-' + index"
          >
            <div class="grid grid-cols-2 gap-5mm mb-5mm text-14px">
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">采集点</span>
                <span>{{ item.address }}</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">采集时间</span>
                <span>{{ item.createTime }}</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">经度</span>
                <span>{{ item.lng }}</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">纬度</span>
                <span>{{ item.lat }}</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">海拔高度</span>
                <span>{{ item.absoluteAltitude }}m</span>
              </div>
              <div class="flex flex-col">
                <span class="font-bold text-gray-600">拍照设备</span>
                <span>{{ item.payload }}</span>
              </div>
            </div>

            <div class="flex justify-between mb-neg-5mm" :class="{ 'grid grid-cols-3 gap-7mm': item.imageList.length === 3, 'gap-5mm': item.imageList.length !== 3 }">
              <div v-for="(img, imgIndex) in item.imageList" :key="img"
                class="flex flex-col w-1/2-gap-5mm mb-5mm h-80mm" :class="{ 'w-1/3-gap-7mm': item.imageList.length === 3 }">
                <img
                  :src="img.image"
                  :alt="'影像-' + (imgIndex + 1)"
                  class="w-full h-full max-h-120mm object-contain rounded-4px shadow-md shadow-black/10 border border-gray-200 bg-gray-50 image-loading-anim"
                  @error="handleImageError"
                />
                <div class="text-center text-10px text-gray-600 mt-2mm">
                  {{ img.fileName }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onBeforeMount, ref, nextTick } from 'vue';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useLoadingBar, useMessage } from 'naive-ui';
import type { MessageReactive } from 'naive-ui'
import { useRoute } from 'vue-router';
import { useAuthStore } from '@/store/modules/auth';
import { getFlyReport } from '@/service/api';
import { ImageOutline as ImageOutlineIcon } from '@vicons/ionicons5';

const route = useRoute();
const message = useMessage();
const authStore = useAuthStore();
const watermarkEnabled = ref(true);
const customWatermark = ref('我是水印');
const recordId = ref();
const hasData = ref(false);
// 在现有ref变量附近添加
const enableTempPoints = ref(true); // 默认开启测温点位渲染

interface TempRecord {
  tips: string;
  createTime: string;
  longitude: number;
  latitude: number;
  maxValue: number;
  minValue: number;
  threshold: number;
}

const recordData = ref<{
  irRecordList: TempRecord[];
  irRecordPhotoList: any[];
  recordPhotoList: any[];
  jobName: string;
  createName: string;
  operatorTime: string;
  workspaceName: string;
  jobType: string;
  flightName: string;
  flyMessage: string;
  execTime: string;
  endTime: string;
  dockType: string;
  dockName: string;
  droneType: string;
  droneName: string;
  payloadName: string;
}>({
  irRecordList: [],
  irRecordPhotoList: [],
  recordPhotoList: [],
  jobName: '',
  createName: '',
  operatorTime: '',
  workspaceName: '',
  jobType: '',
  flightName: '',
  flyMessage: '',
  execTime: '',
  endTime: '',
  dockType: '',
  dockName: '',
  droneType: '',
  droneName: '',
  payloadName: ''
});

onBeforeMount(() => {
  if (route.query.recordId) {
    recordId.value = route.query.recordId;
    getFlyReport(recordId.value)
      .then(res => {
        if (res.data) {
          recordData.value = res.data;
          hasData.value = res.data.irRecordList?.length > 0 || res.data.irRecordPhotoList?.length > 0 || res.data.recordPhotoList?.length > 0;
        }
      })
      .catch(() => {
        message.error('加载报告失败！');
      });
  } else {
    message.error('没有找到报告数据！');
  }
});

// 标题页设置
const titleSettings = ref({
  reportTitle: '飞行作业报告',
  logoUrl: authStore.sysInfo.webConfig.webLogoUrl,
  showLogo: true
});

// 每页显示的行数（根据您的页面高度调整）
const rowsPerPage = 15;

// 计算温度数据分页
const hasTempData = computed(() => hasData.value && recordData.value.irRecordList?.length > 0);
const totalTempPages = computed(() =>
  hasTempData.value ? Math.ceil(recordData.value.irRecordList.length / rowsPerPage) : 0
);
const getTempPageData = (page: number) => {
  if (!hasTempData.value) return [];
  const start = (page - 1) * rowsPerPage;
  const end = page * rowsPerPage;
  return recordData.value.irRecordList.slice(start, end);
};

// 计算影像数据分页
const imagesPerPage = 2;
// 计算影像数据分页
const hasImageData = computed(() => hasData.value && recordData.value.irRecordPhotoList?.length > 0);
const totalImagePages = computed(() =>
  hasImageData.value ? Math.ceil(recordData.value.irRecordPhotoList.length / imagesPerPage) : 0
);
// 计算影像数据起始页码
const imageStartPage = computed(() => (hasTempData.value ? 3 + totalTempPages.value : 3));


// 普通图片信息
const imagesSimplePerPage = 2;
const hasSimpleImageData = computed(() => hasData.value && recordData.value.recordPhotoList?.length > 0);
const totalSimpleImagePages = computed(() =>
  hasSimpleImageData.value ? Math.ceil(recordData.value.recordPhotoList.length / imagesSimplePerPage) : 0
);
// 计算影像数据起始页码
const imageSimpleStartPage = computed(() => (hasTempData.value ? 3 + totalTempPages.value : 3));

// 下载功能
const loadingBar = useLoadingBar();
const generating = ref(false);
const progress = ref(0);
const currentPage = ref(0);
const totalPages = ref(0);

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src =
    'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="150" viewBox="0 0 200 150"><rect width="200" height="150" fill="#eee"/><text x="100" y="75" font-family="Arial" font-size="16" text-anchor="middle" fill="#666">图片加载失败</text></svg>';
  img.style.border = '1px dashed #ccc';
};



// 延迟函数
const delay = (ms: number) =>
  new Promise(resolve => {
    setTimeout(resolve, ms);
  });

// 预加载所有图片
const preloadAllImages = async () => {
  const images = Array.from(document.querySelectorAll('img'));
  const promises = images.map(img => {
    if (img.complete) return Promise.resolve();

    return new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
    });
  });

  return Promise.all(promises);
};

const downloadReport = async () => {
  await nextTick();
  generating.value = true;
  progress.value = 0;
  currentPage.value = 0;

  loadingBar.start();

  try {
    const reportContainer = document.querySelector('#report-container');
    if (!reportContainer) return;

    // 1. 预加载所有图片
    await preloadAllImages();

    // 2. 获取所有页面元素
    const pages = Array.from(reportContainer.querySelectorAll('.pdfpage'));
    const pdf = new jsPDF('p', 'mm', 'a4');
    totalPages.value = pages.length;

    // 3. 分步处理每个页面
    for (let i = 0; i < totalPages.value; i++) {
      currentPage.value = i + 1;
      progress.value = Math.round((i / totalPages.value) * 100);

      const page = pages[i] as HTMLElement;
      const canvas = await html2canvas(page, {
        scale: 2,
        useCORS: true,
        allowTaint: false,
        scrollX: 0,
        scrollY: 0,
        async: true,
        backgroundColor: '#FFFFFF'
      });

      const imgData = canvas.toDataURL('image/png', 0.6);
      const imgWidth = 210;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      if (i < totalPages.value - 1) pdf.addPage();

      // 短暂延迟避免UI阻塞
      if (i % 3 === 0) await delay(50);
    }

    // 4. 保存PDF文件
    pdf.save(`${titleSettings.value.reportTitle}.pdf`);
    message.success('报告生成成功!');
  } catch (error) {
    console.error('导出PDF失败:', error);
    message.error(`导出PDF失败: ${error.message}`);
  } finally {
    generating.value = false;
    loadingBar.finish();
  }
};
</script>


<style scoped>
/*
  UnoCSS custom rules for animations and utility classes not directly mapped.
  These can be placed in unocss.config.ts for global availability,
  or kept here if they are truly scoped to this component and you enable
  transformerDirectives for @apply syntax (though we aim for pure atomic here).
*/

/* Custom Scrollbar */
.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1);
}

/* Custom width calculations for image rows */
.w-1\/2-gap-5mm {
  width: calc(50% - 5mm);
}
.w-1\/3-gap-7mm {
  width: calc(33.33% - 7mm);
}
.mb-neg-5mm {
  margin-bottom: -5mm; /* Specific negative margin */
}
.mt-2mm {
  margin-top: 2mm; /* Specific margin */
}

/* Animations (needs to be defined in unocss.config.ts via `rules` or `safelist` to be generated) */
.marker-pulse {
  animation: pulse 1.5s infinite;
}
@keyframes pulse {
  0% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.1); }
  100% { transform: translate(-50%, -50%) scale(1); }
}

.image-loading-anim {
  animation: loading 1.5s infinite;
}
@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Print specific styles (UnoCSS `print:` variant applies this) */
/* .report-page uses print:visible to ensure it's visible during print */
/* control-panel uses print:hidden to hide during print */

/* Reset button default styles for proper display */
button {
  /* Some button styles might be overridden by default browser/uni-app styles. */
  /* Ensure padding and border are handled by UnoCSS if you want full control. */
  /* Add initial `appearance-none` if button has unwanted default styles from user-agent stylesheet. */
}
</style>




