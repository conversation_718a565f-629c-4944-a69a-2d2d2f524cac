<script setup lang="ts">
import { computed, onBeforeMount, ref } from 'vue';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { useLoadingBar, useMessage } from 'naive-ui';
import { useRoute } from 'vue-router';
import { useAuthStore } from '@/store/modules/auth';
import { getFlyReport } from '@/service/api';

const route = useRoute();
const message = useMessage();
const authStore = useAuthStore();
const watermarkEnabled = ref(true);
const customWatermark = ref('我是水印');
const recordId = ref();
const hasData = ref(false);

const recordData = ref({
  irRecordList: [],
  irRecordPhotoList: [],
  recordPhotoList: [],
  jobName: '',
  createName: '',
  operatorTime: '',
  workspaceName: '',
  jobType: '',
  flightName: '',
  flyMessage: '',
  execTime: '',
  endTime: '',
  dockType: '',
  dockName: '',
  droneType: '',
  droneName: '',
  payloadName: ''
});

onBeforeMount(() => {
  if (route.query.recordId) {
    recordId.value = route.query.recordId;
    getFlyReport(recordId.value)
      .then(res => {
        if (res.data) {
          recordData.value = res.data;
          hasData.value = res.data.irRecordList?.length > 0 || res.data.irRecordPhotoList?.length > 0 || res.data.recordPhotoList?.length > 0;
        }
      })
      .catch(() => {
        message.error('加载报告失败！');
      });
  } else {
    message.error('没有找到报告数据！');
  }
});

// 标题页设置
const titleSettings = ref({
  reportTitle: '飞行作业报告',
  logoUrl: authStore.sysInfo.webConfig.webLogoUrl,
  showLogo: true
});

// 每页显示的行数（根据您的页面高度调整）
const rowsPerPage = 15;

// 计算温度数据分页
const hasTempData = computed(() => hasData.value && recordData.value.irRecordList?.length > 0);
const totalTempPages = computed(() =>
  hasTempData.value ? Math.ceil(recordData.value.irRecordList.length / rowsPerPage) : 0
);
const getTempPageData = (page: number) => {
  if (!hasTempData.value) return [];
  const start = (page - 1) * rowsPerPage;
  const end = page * rowsPerPage;
  return recordData.value.irRecordList.slice(start, end);
};

// 计算影像数据分页
const imagesPerPage = 2;
// 计算影像数据分页
const hasImageData = computed(() => hasData.value && recordData.value.irRecordPhotoList?.length > 0);
const totalImagePages = computed(() =>
  hasImageData.value ? Math.ceil(recordData.value.irRecordPhotoList.length / imagesPerPage) : 0
);
// 计算影像数据起始页码
const imageStartPage = computed(() => (hasTempData.value ? 3 + totalTempPages.value : 3));


// 普通图片信息
const imagesSimplePerPage = 2;
const hasSimpleImageData = computed(() => hasData.value && recordData.value.recordPhotoList?.length > 0);
const totalSimpleImagePages = computed(() =>
  hasSimpleImageData.value ? Math.ceil(recordData.value.recordPhotoList.length / imagesSimplePerPage) : 0
);
// 计算影像数据起始页码
const imageSimpleStartPage = computed(() => (hasTempData.value ? 3 + totalTempPages.value : 3));

// 下载功能
const loadingBar = useLoadingBar();
const generating = ref(false);
const progress = ref(0);
const currentPage = ref(0);
const totalPages = ref(0);

const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement;
  img.src =
    'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="200" height="150" viewBox="0 0 200 150"><rect width="200" height="150" fill="#eee"/><text x="100" y="75" font-family="Arial" font-size="16" text-anchor="middle" fill="#666">图片加载失败</text></svg>';
  img.style.border = '1px dashed #ccc';
};

// 延迟函数
const delay = (ms: number) =>
  new Promise(resolve => {
    setTimeout(resolve, ms);
  });

// 预加载所有图片
const preloadAllImages = async () => {
  const images = Array.from(document.querySelectorAll('img'));
  const promises = images.map(img => {
    if (img.complete) return Promise.resolve();

    return new Promise((resolve, reject) => {
      img.onload = resolve;
      img.onerror = reject;
    });
  });

  return Promise.all(promises);
};

const downloadReport = async () => {
  generating.value = true;
  progress.value = 0;
  currentPage.value = 0;
  loadingBar.start();

  try {
    const reportContainer = document.querySelector('.report-container');
    if (!reportContainer) return;

    // 1. 预加载所有图片
    await preloadAllImages();

    // 2. 获取所有页面元素
    const pages = Array.from(reportContainer.querySelectorAll('.page'));
    const pdf = new jsPDF('p', 'mm', 'a4');
    totalPages.value = pages.length;

    // 3. 分步处理每个页面
    for (let i = 0; i < totalPages.value; i++) {
      currentPage.value = i + 1;
      progress.value = Math.round((i / totalPages.value) * 100);

      const page = pages[i] as HTMLElement;
      const canvas = await html2canvas(page, {
        scale: 2,
        useCORS: true,
        allowTaint: false,
        scrollX: 0,
        scrollY: 0,
        async: true,
        backgroundColor: '#FFFFFF'
      });

      const imgData = canvas.toDataURL('image/png');
      const imgWidth = 210;
      const imgHeight = (canvas.height * imgWidth) / canvas.width;

      pdf.addImage(imgData, 'PNG', 0, 0, imgWidth, imgHeight);
      if (i < totalPages.value - 1) pdf.addPage();

      // 短暂延迟避免UI阻塞
      if (i % 3 === 0) await delay(50);
    }

    // 4. 保存PDF文件
    pdf.save(`${titleSettings.value.reportTitle}.pdf`);
    message.success('报告生成成功!');
  } catch (error) {
    console.error('导出PDF失败:', error);
    message.error(`导出PDF失败: ${error.message}`);
  } finally {
    generating.value = false;
    loadingBar.finish();
  }
};
</script>

<template>
  <div class="report-app">
    <!-- 全局加载指示器 -->
    <div v-if="generating" class="global-loading">
      <div class="loading-content">
        <NSpin size="large" />
        <div class="loading-text">正在生成报告: {{ progress }}%</div>
        <div class="loading-detail">当前处理第 {{ currentPage }}/{{ totalPages }} 页</div>
      </div>
    </div>
    <!-- 左侧操作面板 -->
    <div class="control-panel">
      <!-- 标题页设置 -->
      <div class="control-group">
        <h3>标题页设置</h3>
        <div class="control-item">
          <label>报告标题：</label>
          <input v-model="titleSettings.reportTitle" type="text" />
        </div>
        <div class="control-item">
          <label>
            <input v-model="titleSettings.showLogo" type="checkbox" />
            显示Logo
          </label>
        </div>
      </div>
      <div class="control-group">
        <h3>报告设置</h3>
        <div class="control-item">
          <label>
            <input v-model="watermarkEnabled" type="checkbox" />
            启用水印
          </label>
        </div>
        <div v-if="watermarkEnabled" class="control-item">
          <label>水印文字：</label>
          <input v-model="customWatermark" type="text" />
        </div>
      </div>

      <div class="control-group">
        <h3>操作</h3>
        <button :disabled="generating" class="download-button" @click="downloadReport">
          <span v-if="!generating">下载报告</span>
          <span v-else class="loading-text">
            <NSpin size="small" />
            正在生成 ({{ progress }}%)
          </span>
        </button>
        <!-- 进度条提示 -->
        <div v-if="generating" class="progress-hint">报告较大，生成可能需要一些时间，请勿退出，请耐心等待...</div>
      </div>
    </div>

    <!-- 飞行报告 -->
    <div class="report-container">
      <!-- 第一页 (标题页) -->
      <div class="page">
        <div v-if="watermarkEnabled" class="watermark">
          <div class="watermark-item watermark-1">{{ customWatermark }}</div>
          <div class="watermark-item watermark-2">{{ customWatermark }}</div>
          <div class="watermark-item watermark-3">{{ customWatermark }}</div>
        </div>
        <div class="page-number">第 1 页</div>
        <div v-if="titleSettings.showLogo" class="logo">
          <img :src="titleSettings.logoUrl" alt="LOGO" style="height: 30mm" />
        </div>
        <div class="report-title">{{ titleSettings.reportTitle }}</div>
        <div class="task-name">{{ hasData ? recordData.jobName : '无任务数据' }}</div>
        <div class="operator-info">
          <div class="operator-item">
            <span class="operator-label">操作员：</span>
            <span class="operator-value">{{ hasData ? recordData.createName : '无数据' }}</span>
          </div>
          <div class="operator-item">
            <span class="operator-label">报告时间：</span>
            <span class="operator-value">{{ hasData ? recordData.operatorTime : new Date().toLocaleString() }}</span>
          </div>
        </div>
      </div>

      <!-- 第二页 (固定) -->
      <div class="page">
        <div v-if="watermarkEnabled" class="watermark">
          <div class="watermark-item watermark-1">{{ customWatermark }}</div>
          <div class="watermark-item watermark-2">{{ customWatermark }}</div>
          <div class="watermark-item watermark-3">{{ customWatermark }}</div>
        </div>
        <div class="page-number">第 2 页</div>
        <div class="section-title">基本信息</div>
        <div class="info-grid">
          <div class="info-label">所属组织：</div>
          <div>{{ recordData.workspaceName }}</div>

          <div class="info-label">作业模式：</div>
          <div>{{ recordData.jobType }}</div>

          <div class="info-label">航线名称：</div>
          <div>{{ recordData.flightName }}</div>

          <div class="info-label">里程|时间：</div>
          <div>{{ recordData.flyMessage }}</div>

          <div class="info-label">开始时间：</div>
          <div>{{ recordData.execTime }}</div>

          <div class="info-label">结束时间：</div>
          <div>{{ recordData.endTime }}</div>
        </div>

        <!-- Replace the existing "设备信息" section with this: -->
        <div class="section-title">设备信息</div>
        <div class="info-grid">
          <!-- 机库信息 -->
          <div class="info-label">机库厂家：</div>
          <div>DJI大疆</div>
          <div class="info-label">机库型号：</div>
          <div>{{ recordData.dockType }}</div>
          <div class="info-label">机库名称：</div>
          <div>{{ recordData.dockName }}</div>
          <!-- 无人机信息 -->
          <div class="info-label">无人机厂家：</div>
          <div>DJI大疆</div>
          <div class="info-label">无人机型号：</div>
          <div>{{ recordData.droneType }}</div>
          <div class="info-label">无人机名称：</div>
          <div>{{ recordData.droneName }}</div>
          <!-- 载荷信息 -->
          <div class="info-label">载荷类型：</div>
          <div>摄像头</div>
          <div class="info-label">载荷厂家：</div>
          <div>DJI大疆</div>
          <div class="info-label">载荷型号：</div>
          <div>{{ recordData.payloadName }}</div>
        </div>
      </div>

      <!-- 温度检测数据页 (动态分页) - 仅在hasTempData为true时渲染 -->
      <template v-if="hasTempData && hasData">
        <div v-for="page in totalTempPages" :key="'temp-page-' + page" class="page temp-page">
          <div v-if="watermarkEnabled" class="watermark">
            <div class="watermark-item watermark-1">{{ customWatermark }}</div>
            <div class="watermark-item watermark-2">{{ customWatermark }}</div>
            <div class="watermark-item watermark-3">{{ customWatermark }}</div>
          </div>
          <div class="page-number">第 {{ page + 2 }} 页</div>

          <div v-if="page === 1" class="section-title">温度检测数据</div>
          <div v-else class="section-title">温度检测数据 (续 {{ page }})</div>

          <table class="temp-table">
            <colgroup>
              <col style="width: 110px" />
              <!-- 测温点列宽 -->
              <col style="width: 80px" />
              <!-- 时间列宽 -->
              <col style="width: 80px" />
              <!-- 经度列宽 -->
              <col style="width: 80px" />
              <!-- 纬度列宽 -->
              <col style="width: 80px" />
              <!-- 最高温度列宽 -->
              <col style="width: 80px" />
              <!-- 最低温度列宽 -->
              <col style="width: 170px" />
              <!-- 阈值列宽 -->
            </colgroup>
            <thead>
              <tr>
                <th>测温点</th>
                <th>时间</th>
                <th>经度</th>
                <th>纬度</th>
                <th>最高温度</th>
                <th>最低温度</th>
                <th>温度阈值</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(item, index) in getTempPageData(page)" :key="index">
                <td class="point-cell">{{ item.tips }}</td>
                <td>{{ item.createTime }}</td>
                <td>{{ item.longitude.toFixed(6) }}</td>
                <td>{{ item.latitude.toFixed(6) }}</td>
                <td>{{ item.maxValue }}℃</td>
                <td>{{ item.minValue }}℃</td>
                <td>{{ item.threshold }}</td>
              </tr>
            </tbody>
          </table>
        </div>
      </template>

      <template v-if="hasImageData && hasData">
        <!-- 回传影像页 (动态分页) -->
        <div v-for="(page, pageIndex) in totalImagePages" :key="'image-page-' + pageIndex" class="page">
          <div v-if="watermarkEnabled" class="watermark">
            <div class="watermark-item watermark-1">{{ customWatermark }}</div>
            <div class="watermark-item watermark-2">{{ customWatermark }}</div>
            <div class="watermark-item watermark-3">{{ customWatermark }}</div>
          </div>
          <div class="page-number">第 {{ imageStartPage + pageIndex }} 页</div>

          <div v-if="pageIndex === 0" class="section-title">回传影像</div>
          <div v-else class="section-title">回传影像 (续{{ pageIndex + 1 }})</div>

          <!-- 每页显示2组数据 -->
          <template
            v-for="(item, index) in recordData.irRecordPhotoList?.slice(
              pageIndex * imagesPerPage,
              (pageIndex + 1) * imagesPerPage
            )"
            :key="item.point + '-' + index"
          >
            <div class="image-info-grid">
              <div class="image-info-item">
                <span class="image-info-label">采集点</span>
                <span>{{ item.tips }}</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">采集时间</span>
                <span>{{ item.createTime }}</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">经度</span>
                <span>{{ item.longitude }}</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">纬度</span>
                <span>{{ item.latitude }}</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">最高温度</span>
                <span>{{ item.maxValue }}℃</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">最低温度</span>
                <span>{{ item.minValue }}℃</span>
              </div>
            </div>

            <!-- 动态渲染图片，根据图片数量自动调整布局 -->
            <div class="image-row" :class="{ 'three-images': item.imageList.length === 3 }">
              <div v-for="(img, imgIndex) in item.imageList" :key="img" class="image-container">
                <img
                  :src="img.image"
                  :alt="'影像-' + (imgIndex + 1)"
                  style="width: 100%; height: auto; max-height: 120mm"
                  @error="handleImageError"
                />
                <div class="image-caption">
                  {{ img.fileName }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </template>

      <template v-if="hasSimpleImageData && hasData">
        <!-- 回传影像页 (动态分页) -->
        <div v-for="(page, pageIndex) in totalSimpleImagePages" :key="'image-page-' + pageIndex" class="page">
          <div v-if="watermarkEnabled" class="watermark">
            <div class="watermark-item watermark-1">{{ customWatermark }}</div>
            <div class="watermark-item watermark-2">{{ customWatermark }}</div>
            <div class="watermark-item watermark-3">{{ customWatermark }}</div>
          </div>
          <div class="page-number">第 {{ imageSimpleStartPage + pageIndex }} 页</div>

          <div v-if="pageIndex === 0" class="section-title">回传影像</div>
          <div v-else class="section-title">回传影像 (续{{ pageIndex + 1 }})</div>

          <!-- 每页显示2组数据 -->
          <template
            v-for="(item, index) in recordData.recordPhotoList?.slice(
              pageIndex * imagesSimplePerPage,
              (pageIndex + 1) * imagesSimplePerPage
            )"
            :key="item.createTime + '-' + index"
          >
            <div class="image-info-grid">
              <div class="image-info-item">
                <span class="image-info-label">采集点</span>
                <span>{{ item.address }}</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">采集时间</span>
                <span>{{ item.createTime }}</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">经度</span>
                <span>{{ item.lng }}</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">纬度</span>
                <span>{{ item.lat }}</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">海拔高度</span>
                <span>{{ item.absoluteAltitude }}m</span>
              </div>
              <div class="image-info-item">
                <span class="image-info-label">拍照设备</span>
                <span>{{ item.payload }}</span>
              </div>
            </div>

            <!-- 动态渲染图片，根据图片数量自动调整布局 -->
            <div class="image-row" :class="{ 'three-images': item.imageList.length === 3 }">
              <div v-for="(img, imgIndex) in item.imageList" :key="img" class="image-container">
                <img
                  :src="img.image"
                  :alt="'影像-' + (imgIndex + 1)"
                  style="width: 100%; height: auto; max-height: 120mm"
                  @error="handleImageError"
                />
                <div class="image-caption">
                  {{ img.fileName }}
                </div>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>
  </div>
</template>

<style scoped>
.global-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
}

.loading-content {
  background: white;
  padding: 30px;
  border-radius: 8px;
  text-align: center;
  max-width: 80%;
}

.loading-text {
  font-size: 18px;
  margin: 15px 0;
  font-weight: bold;
}

.loading-detail {
  color: #666;
}
/* 新增样式 */
.download-button {
  position: relative;
  overflow: hidden;
}

.loading-text {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.progress-hint {
  margin-top: 10px;
  font-size: 12px;
  color: #aaa;
  text-align: center;
}

/* 禁用状态按钮样式 */
button:disabled {
  background-color: #555 !important;
  cursor: not-allowed;
  opacity: 0.7;
}

/* 加载动画 */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 新增的布局样式 */
.report-app {
  display: flex;
  min-height: 100vh;
}

/* 左侧控制面板样式 - 黑色主题 */
.control-panel {
  width: 300px;
  padding: 20px;
  background-color: #000000; /* 纯黑色背景 */
  color: #e0e0e0; /* 浅灰色文字 */
  border-right: 1px solid #333;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.control-group {
  background: rgba(255, 255, 255, 0.08); /* 非常微弱的白色背景 */
  padding: 15px;
  border-radius: 6px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.control-group h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #ffffff; /* 纯白色标题 */
  font-size: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.15);
  padding-bottom: 8px;
}

.control-item {
  margin-bottom: 12px;
}

.control-item label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
  color: #d0d0d0; /* 稍亮的灰色 */
}

.control-item input[type='text'],
.control-item input[type='checkbox'] + span,
textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #333; /* 深灰色边框 */
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.05); /* 非常微弱的背景 */
  color: #ffffff; /* 白色文字 */
  font-size: 14px;
}

.control-item input[type='checkbox'] {
  margin-right: 8px;
}

button {
  width: 100%;
  padding: 10px;
  margin-bottom: 10px;
  background-color: #333; /* 深灰色按钮 */
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

button:hover {
  background-color: #444; /* 悬停时稍亮 */
  transform: translateY(-1px);
}

/* 输入框聚焦样式 */
.control-item input:focus,
textarea:focus {
  outline: none;
  border-color: #555;
  box-shadow: 0 0 0 2px rgba(85, 85, 85, 0.3);
  background-color: rgba(255, 255, 255, 0.1);
}

/* 复选框样式调整 */
.control-item label > input[type='checkbox'] {
  accent-color: #555; /* 自定义复选框颜色 */
}

/* 滚动条样式 */
.control-panel::-webkit-scrollbar {
  width: 6px;
}

.control-panel::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 3px;
}

.control-panel::-webkit-scrollbar-track {
  background-color: rgba(0, 0, 0, 0.1);
}

.report-container {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  font-family: 'Microsoft YaHei', Arial, sans-serif;
  color: #333;
  background-color: #000000;
  position: relative;
}

/* 确保所有内容可见 */
.report-container * {
  visibility: visible;
}

.page {
  width: 210mm !important;
  height: 297mm !important;
  margin: 0 auto;
  background: white !important;
  position: relative;
  padding: 15mm;
  box-sizing: border-box;
  page-break-after: always;
  overflow: hidden;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.05); /* 轻微阴影增加层次感 */
}

/* 水印容器 - 每页独立 */
.watermark {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

/* 单个水印样式 */
.watermark-item {
  position: absolute;
  font-size: 40px;
  color: rgba(0, 0, 0, 0.08);
  transform: rotate(-30deg);
  font-weight: bold;
}

/* 三个水印位置 */
.watermark-1 {
  top: 20%;
  left: 10%;
}

.watermark-2 {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) rotate(-30deg);
}

.watermark-3 {
  bottom: 20%;
  right: 10%;
}

/* 页码样式 */
.page-number {
  position: absolute;
  right: 15mm;
  bottom: 10mm;
  font-size: 12px;
  color: #666;
}

/* 第一页背景效果 */
.page:first-child {
  background: white; /* 纯白背景 */
}

.logo {
  position: absolute;
  top: 15mm;
  left: 15mm;
  width: auto;
  height: 30mm;
  z-index: 100;
}

.report-title {
  text-align: center;
  margin-top: 100mm;
  font-size: 36px;
  font-weight: bold;
  color: #2c3e50;
  text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
}

.task-name {
  text-align: center;
  margin-top: 50mm;
  font-size: 24px;
  color: #34495e;
}

.operator-info {
  position: absolute;
  bottom: 15mm;
  left: 0;
  right: 0;
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  color: #2c3e50;
  padding: 0 15mm;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.operator-item {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 5px;
}

.operator-label {
  font-weight: bold;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-top: 0mm;
  margin-bottom: 5mm;
  border-bottom: 1px solid #ddd;
  padding-bottom: 2mm;
  color: #2c3e50;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 3fr;
  gap: 3mm;
  margin-bottom: 10mm;
}

.info-label {
  font-weight: bold;
  text-align: right;
  color: #7f8c8d;
}

/* 表格基础样式 */
.temp-table {
  table-layout: fixed; /* 关键：固定布局 */
  width: 100%;
  border-collapse: collapse;
  font-size: 12px;
  margin-top: 5mm;
  page-break-inside: avoid;
}

/* 表头样式 */
.temp-table th {
  background-color: #f2f2f2;
  padding: 8px 5px;
  text-align: center;
  position: sticky;
  top: 0;
  border: 1px solid #ddd;
}

/* 单元格通用样式 */
.temp-table td {
  padding: 6px 5px;
  border: 1px solid #ddd;
  text-align: center;
}

/* 测温点单元格特殊处理 */
.point-cell {
  word-break: break-word; /* 允许在单词内换行 */
  white-space: normal; /* 允许换行 */
  hyphens: auto; /* 自动连字符（英文有效） */
  text-align: left; /* 左对齐更美观 */
  padding: 6px 4px; /* 适当减小内边距 */
  min-width: 0; /* 重要：允许缩小 */
}

/* 确保表格在分页时正确断开 */
.temp-page {
  page-break-after: always;
}

.temp-table tr {
  page-break-inside: avoid;
}

/* 回传影像两列布局 */
.image-info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 5mm;
  margin-bottom: 5mm;
  font-size: 14px;
}

.image-info-item {
  display: flex;
  flex-direction: column;
}

.image-info-label {
  font-weight: bold;
  color: #7f8c8d;
  font-size: 14px;
}

/* 图片行布局 */
.image-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: -5mm;
}

.image-container {
  width: calc(50% - 5mm);
  margin-bottom: 5mm;
  height: 80mm; /* 固定高度 */
  display: flex;
  flex-direction: column;
}

.image-container img {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 保持比例 */
  border-radius: 4px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
  border: 1px solid #eee;
  background-color: #f9f9f9;
}

.image-caption {
  text-align: center;
  font-size: 10px;
  color: #666;
  margin-top: 2mm;
}

/* 图片加载时的占位样式 */
img[data-loading] {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.three-images .image-container {
  width: calc(33.33% - 7mm);
}

/* 隐藏不需要导出的元素 */
@media print {
  .control-panel {
    display: none !important;
  }
}
</style>
