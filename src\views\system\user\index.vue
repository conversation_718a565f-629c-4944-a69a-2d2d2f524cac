<script setup lang="ts">
import {type FormInst, NButton, NIcon, NInput, NSwitch, NText, useDialog, useMessage} from 'naive-ui';
import { h, onMounted, reactive, ref } from 'vue';
import { Close } from '@vicons/ionicons5';
import { addUser, changeUserStatus, delUser, deptTree, editUser, fetchUserList, resetPwd } from '@/service/api/user';

const message = useMessage();
const dialog = useDialog();
const tableData = ref([]);
const treeData = ref([]);
const pattern = ref('');
const showIrrelevantNodes = ref(false);
const formRef = ref<FormInst | null>(null)

const columns = [
  {
    title: '登录账号',
    key: 'userName'
  },
  {
    title: '用户名称',
    key: 'nickName'
  },
  {
    title: '部门',
    key: 'dept.deptName'
  },
  {
    title: '手机号码',
    key: 'phonenumber'
  },
  {
    title: '状态',
    key: 'status',
    render(row: any) {
      return h(NSwitch, {
        value: row.status === '0',
        onUpdateValue: value => handleSwitchChange(row, value)
      });
    }
  },
  {
    title: '操作',
    key: 'action',
    render(row: Api.User.UserItem) {
      return h('span', [
        h(
          NText,
          {},
          {
            default: () => {
              const elements = [];
              elements.push(
                h(
                  NText,
                  {
                    type: 'info',
                    class: 'cursor-pointer',
                    onClick: () => updateByUser(row)
                  },
                  { default: () => '修改' }
                ),
                h(
                  NText,
                  {
                    type: 'error',
                    class: 'cursor-pointer ml-10px',
                    onClick: () => deleteByUser(row)
                  },
                  { default: () => '删除' }
                ),
                h(
                  NText,
                  {
                    type: 'warning',
                    class: 'cursor-pointer ml-10px',
                    onClick: () => resetPwdByUser(row)
                  },
                  { default: () => '重置密码' }
                )
              );
              return elements;
            }
          }
        )
      ]);
    }
  }
];

const infoRules: any = {
  userName: [{ key: 'userName', required: true, trigger: ['blur', 'input'], message: '请填写登录账号' }],
  nickName: [{ key: 'nickName', required: true, trigger: ['blur', 'input'], message: '请填写用户昵称' }],
  deptId: [{ key: 'deptId', required: true, type: 'number', trigger: ['blur', 'select'], message: '请选择所属部门' }],
  phonenumber: [{ key: 'phonenumber', required: true, trigger: ['blur', 'input'], message: '请填写手机号码' }]
};

const addInfoRules: any = {
  userName: [{ key: 'userName', required: true, trigger: ['blur', 'input'], message: '请填写登录账号' }],
  password: [{ key: 'password', required: true, trigger: ['blur', 'input'], message: '请填写登录密码' }],
  nickName: [{ key: 'nickName', required: true, trigger: ['blur', 'input'], message: '请填写用户昵称' }],
  deptId: [{ key: 'deptId', required: true, type: 'number', trigger: ['blur', 'select'], message: '请选择所属部门' }],
  phonenumber: [{ key: 'phonenumber', required: true, trigger: ['blur', 'input'], message: '请填写手机号码' }],
};

const infoModel = ref<Api.User.UserItem>({
  userId: null,
  deptId: null,
  userName: '',
  nickName: '',
  phonenumber: '',
  email: '',
  password: '',
  sex: 1,
  status: 0,
  roleIds: [2]
});

const statusList = [
  {
    id: '0',
    label: '正常',
    sex: '男'
  },
  {
    id: '1',
    label: '停用',
    sex: '女'
  }
];

const tableLoading = ref(true);
const showResetPwdModal = ref(false);
const resetPwdTitle = ref('');
const showUpdateByUserModal = ref(false);
const showAddByUserModal = ref(false);

// 表单
const formValue = reactive<{ deptId: number | null; userName: string }>({
  userName: '',
  deptId: null
});

const resetPwdObj = reactive({
  userId: null as number | null,
  password: ''
});

function handleResetModal() {
  resetPwdObj.password = '';
  resetPwdObj.userId = null;
  showResetPwdModal.value = false;
}

function handleUpdateModal() {
  showUpdateByUserModal.value = false;
}

function handleAddModal() {
  showAddByUserModal.value = false;
}

// 分页
const pagination = reactive({
  pageNum: 1,
  pageSize: 10,
  itemCount: 0
});

function updateByUserConfirm() {
  formRef.value?.validate(errors => {
    if (errors) {
      return;
    }
    editByUser();
  });
}

function addByUserConfirm(){
  formRef.value?.validate(errors => {
    if (errors) {
      return;
    }
    addByUser();
  });
}

async function addByUser(){
  const data = {
    ...infoModel.value,
    roleIds: [2]
  }
  const { error } = await addUser(data);
  if(!error){
    message.success('新增成功');
    tableLoading.value = true;
    showAddByUserModal.value = false;
    await getUserList();
  }
}

async function editByUser(){
  const data = {
    ...infoModel.value,
    roleIds: [2]
  }
  const { error } = await editUser(data);
  if(!error){
    message.success('修改成功');
    tableLoading.value = true;
    showUpdateByUserModal.value = false;
    await getUserList();
  }
}

async function updateByUser(row: Api.User.UserItem) {
  showUpdateByUserModal.value = true;
  const data = {
    ...row
  };
  infoModel.value = data;
  console.log('修改的对象', infoModel)
}

// 处理数据
async function resetPwdByUser(row: Api.User.UserItem) {
  resetPwdTitle.value = `重置[${row.userName}] 用户的密码 `;
  resetPwdObj.userId = row.userId;
  showResetPwdModal.value = true;
}

// 重置密码提交接口
async function resetPwdByConfirm() {
  const data = {
    userId: resetPwdObj.userId,
    password: resetPwdObj.password
  };
  const { error } = await resetPwd(data);
  if (!error) {
    message.success('重置成功！');
    tableLoading.value = true;
    showResetPwdModal.value = false;
    resetPwdObj.password = '';
    resetPwdObj.userId = null;
    await getUserList();
  }
}

function addUserOpen() {
  showAddByUserModal.value = true;
  infoModel.value = {
    userId: null,
    deptId: null,
    userName: '',
    nickName: '',
    phonenumber: '',
    email: '',
    password: '',
    sex: '0',
    status: '0',
    roleIds: [2]
  }
}

// 删除用户
async function deleteByUser(row: Api.User.UserItem) {
  const { userId, userName } = row;
  dialog.error({
    title: '警告',
    content: `确定要删除 ${userName} 的账号吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const { error } = await delUser(userId);
      if (!error) {
        message.success('删除成功！');
        tableLoading.value = true;
        await getUserList();
      }
    }
  });
}

// 处理开关状态变化
async function handleSwitchChange(option, value) {
  const mess = value ? '启用' : '停用';
  dialog.warning({
    title: '警告',
    content: `确定要${mess}${option.userName} 的账号吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      // 更新数据状态
      option.status = value ? '0' : '1'; // 根据开关状态更新 status 字段
      const { error } = await changeUserStatus({
        userId: option.userId,
        status: option.status
      });
      if (!error) {
        message.success(value ? '启用成功' : '停用成功');
        tableLoading.value = true;
        await getUserList();
      }
    }
  });
}

// 更改分页页数
const onPaginationChange = (page: number) => {
  pagination.pageNum = page;
  tableLoading.value = true;
  getUserList();
};

// 查询
const handleQueryClick = () => {
  tableLoading.value = true;
  getUserList();
};

// 获取用户列表
async function getUserList() {
  const { userName, deptId } = formValue;
  const param = {
    userName,
    deptId,
    nickName: userName,
    pageNum: pagination.pageNum,
    pageSize: pagination.pageSize
  };
  const { error, data } = await fetchUserList(param);
  if (!error) {
    tableLoading.value = false;
    tableData.value = data.rows;
    pagination.itemCount = data.total;
  }
}

function nodeProps({ option }) {
  return {
    onClick() {
      formValue.deptId = option.id;
      getUserList();
    }
  };
}

async function getDeptTree() {
  const { data } = await deptTree({
    deptName: formValue.deptName
  });
  treeData.value = data;
}

onMounted(() => {
  getDeptTree();
  getUserList();
});
</script>

<template>
  <div>
    <NGrid class="mx-10px mt-10px" :x-gap="12" :y-gap="12" :cols="4" layout-shift-disabled>
      <NGi :span="1">
        <NCard title="部门管理" :bordered="false">
          <NGrid :x-gap="24" :cols="1">
            <NGridItem>
              <NFormItem>
                <NInput v-model:value="pattern" placeholder="输入部门名称" clearable />
              </NFormItem>
            </NGridItem>
          </NGrid>
          <NScrollbar style="max-height: 66vh">
            <NTree
              block-line
              :pattern="pattern"
              :show-irrelevant-nodes="showIrrelevantNodes"
              :data="treeData"
              :default-expand-all="true"
              key-field="id"
              label-field="label"
              children-field="children"
              :node-props="nodeProps"
              selectable
            />
          </NScrollbar>
        </NCard>
      </NGi>
      <NGi :span="3">
        <NCard title="用户管理" :bordered="false">
          <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
            <NGrid :x-gap="12" :cols="3">
              <NGridItem>
                <NButton type="primary" attr-type="button" @click="addUserOpen">新建用户</NButton>
              </NGridItem>
              <NGridItem :offset="1">
                <NFormItem path="userName">
                  <NInput v-model:value="formValue.userName" placeholder="输入用户名称" clearable />
                  <NButton attr-type="button" class="ml-10" @click="handleQueryClick">查询</NButton>
                </NFormItem>
              </NGridItem>
            </NGrid>
          </NForm>
          <NScrollbar style="max-height: 66vh">
            <NDataTable ref="table" remote :columns="columns" :data="tableData" :loading="tableLoading" />
          </NScrollbar>
          <div class="mt-15px flex justify-end">
            <NPagination
              v-model:page="pagination.pageNum"
              :item-count="pagination.itemCount"
              @update:page="onPaginationChange"
            />
          </div>
        </NCard>
      </NGi>
    </NGrid>
    <NModal v-model:show="showResetPwdModal">
      <NCard style="width: 600px" :title="resetPwdTitle" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <template #header-extra>
          <NIcon size="20" class="cursor-pointer" @click="handleResetModal">
            <Close />
          </NIcon>
        </template>
        <NFormItem path="password">
          <NInput v-model:value="resetPwdObj.password" placeholder="请输入重置后的密码" clearable />
        </NFormItem>
        <template #footer>
          <NFlex justify="end">
            <NButton @click="handleResetModal">取消</NButton>
            <NButton type="primary" @click="resetPwdByConfirm">确定</NButton>
          </NFlex>
        </template>
      </NCard>
    </NModal>

    <NModal v-model:show="showUpdateByUserModal">
      <NCard style="width: 600px" title="修改用户" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <template #header-extra>
          <NIcon size="20" class="cursor-pointer" @click="handleUpdateModal">
            <Close />
          </NIcon>
        </template>
        <NForm
          ref="formRef"
          :model="infoModel"
          :rules="infoRules"
          require-mark-placement="right-hanging"
          label-placement="left"
          label-width="auto"
        >
          <NFormItem label="归属部门" path="deptId">
            <NTreeSelect
              filterable
              key-field="id"
              label-field="label"
              children-field="children"
              v-model:value="infoModel.deptId"
              :options="treeData"
              :default-value="infoModel.deptId"
            />
          </NFormItem>
          <NFormItem label="用户账号" path="userName">
            <NInput v-model:value="infoModel.userName" disabled clearable />
          </NFormItem>
          <NFormItem label="用户昵称" path="nickName">
            <NInput v-model:value="infoModel.nickName" clearable />
          </NFormItem>
          <NFormItem label="手机号码" path="phonenumber">
            <NInput v-model:value="infoModel.phonenumber" clearable />
          </NFormItem>
          <NFormItem label="邮箱" path="email">
            <NInput v-model:value="infoModel.email" clearable />
          </NFormItem>
          <NFormItem label="性别" path="sex">
            <NRadioGroup v-model:value="infoModel.sex" name="sex">
              <NSpace>
                <NRadio v-for="item in statusList" :key="item.id" :value="item.id">
                  {{ item.sex }}
                </NRadio>
              </NSpace>
            </NRadioGroup>
          </NFormItem>
          <NFormItem label="状态" path="status">
            <NRadioGroup v-model:value="infoModel.status" name="status">
              <NSpace>
                <NRadio v-for="item in statusList" :key="item.id" :value="item.id">
                  {{ item.label }}
                </NRadio>
              </NSpace>
            </NRadioGroup>
          </NFormItem>
        </NForm>
        <template #footer>
          <NFlex justify="end">
            <NButton @click="handleUpdateModal">取消</NButton>
            <NButton type="primary" @click="updateByUserConfirm">确定</NButton>
          </NFlex>
        </template>
      </NCard>
    </NModal>

    <NModal v-model:show="showAddByUserModal">
      <NCard style="width: 600px" title="新增用户" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <template #header-extra>
          <NIcon size="20" class="cursor-pointer" @click="handleAddModal">
            <Close />
          </NIcon>
        </template>
        <NForm
          ref="formRef"
          :model="infoModel"
          :rules="addInfoRules"
          require-mark-placement="right-hanging"
          label-placement="left"
          label-width="auto"
        >
          <NFormItem label="归属部门" path="deptId">
            <NTreeSelect
              filterable
              key-field="id"
              label-field="label"
              children-field="children"
              v-model:value="infoModel.deptId"
              :options="treeData"
              :default-value="infoModel.deptId"
            />
          </NFormItem>
          <NFormItem label="用户账号" path="userName">
            <NInput v-model:value="infoModel.userName" clearable />
          </NFormItem>
          <NFormItem label="用户密码" path="password">
            <NInput v-model:value="infoModel.password" clearable />
          </NFormItem>
          <NFormItem label="用户昵称" path="nickName">
            <NInput v-model:value="infoModel.nickName" clearable />
          </NFormItem>
          <NFormItem label="手机号码" path="phonenumber">
            <NInput v-model:value="infoModel.phonenumber" clearable />
          </NFormItem>
          <NFormItem label="邮箱" path="email">
            <NInput v-model:value="infoModel.email" clearable />
          </NFormItem>
          <NFormItem label="性别" path="sex">
            <NRadioGroup v-model:value="infoModel.sex" name="sex">
              <NSpace>
                <NRadio v-for="item in statusList" :key="item.id" :value="item.id">
                  {{ item.sex }}
                </NRadio>
              </NSpace>
            </NRadioGroup>
          </NFormItem>
          <NFormItem label="状态" path="status">
            <NRadioGroup v-model:value="infoModel.status" name="status">
              <NSpace>
                <NRadio v-for="item in statusList" :key="item.id" :value="item.id">
                  {{ item.label }}
                </NRadio>
              </NSpace>
            </NRadioGroup>
          </NFormItem>
        </NForm>
        <template #footer>
          <NFlex justify="end">
            <NButton @click="handleAddModal">取消</NButton>
            <NButton type="primary" @click="addByUserConfirm">确定</NButton>
          </NFlex>
        </template>
      </NCard>
    </NModal>

  </div>
</template>

<style scoped></style>
