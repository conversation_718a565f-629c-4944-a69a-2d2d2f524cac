import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import { useEventListener } from '@vueuse/core';
import { SetupStoreId } from '@/enum';
import { localStg } from '@/utils/storage';

// store/task.ts
export const useTaskStore = defineStore(SetupStoreId.Task, () => {
  const droneControlStoreStatus = ref(false); // 无人机控制权获取状态 true-已获取 false-未获取
  const ptzControlStoreStatus = ref(false); // 云台控制权获取状态 true-已获取 false-未获取

  function updateDroneControlStoreStatus(newStatus:boolean) {
    droneControlStoreStatus.value = newStatus;
  }

  function updatePTZControlStoreStatus(newStatus:boolean) {
    ptzControlStoreStatus.value = newStatus;
  }

  return {
    droneControlStoreStatus,
    ptzControlStoreStatus,
    updateDroneControlStoreStatus,
    updatePTZControlStoreStatus
  };
});
