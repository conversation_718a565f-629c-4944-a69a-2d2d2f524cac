<template>
  <div class="h100vh relative">
    <div id="cesiumViewer" class="h100%"></div>
    <status-bar id="status-bar" class="w-300px h-max" v-if="loaded" :viewer="viewer"></status-bar>
    <!-- <n-icon size="22" class="cursor-pointer" /> -->
    <n-float-button :left="100" :top="10">
      <n-icon @click="() => { routerBack() }">
        <ArrowBack />
      </n-icon>
    </n-float-button>
    <!-- 整条航线设置 -->
    <airLineInfo :sumFlyDistance="sumFlyDistance" :waypointLatLngArray="waypointLatLngArray"
      :airLineConfig="airLineConfig" @save-airline="handleClickThrottle" />
    <!-- 单个航点设置 -->
    <airLineCreate v-show="airlineSetModalShow" :currentWaypointIndex="currentWaypointIndex"
      :airPointActionArr="airPointActionArr" :waypointLatLngArray="waypointLatLngArray"
      :heightArrParams="heightArrParams" @handel-creat-modal="handelAirlineSetModalShow"
      @update:wayActions="updateWayActions" ref="airLineCreateRef" @getHeight="getHeight" />
    <!-- 测量工具 -->
    <geoMeasureKit id="geoMeasureKit" :viewer="viewer" @changeStatus="handleClickStatus"
      :objSpace="{ width: 30, place: { bottom: 0, left: 100 }, direction: 'right' }"
      ref="childRef" :style="{
        position: 'fixed',
        bottom: '10px',
        left: '100px',
        zIndex: 1000
      }"></geoMeasureKit>
  </div>
</template>

<script setup lang="ts">
import * as Cesium from 'cesium';
import { Ref, h, onBeforeUnmount, onMounted, reactive, ref, toRefs, watch, provide } from 'vue';
import { MouthPositionHandler, cesiumConfig, josiahProvider, labelProvider, position, rectangle } from '@/config/cesium_config';
import marker from "@/assets/imgs/airport.png";
import { LocationQueryValue, useRoute, useRouter } from 'vue-router';
import { useRouterPush } from '@/hooks/common/router';
import { useAuthStore } from '@/store/modules/auth';
import { fetchAirLineSaveInfo, fetchAirportDeviceList, sendAirLineSaveInfo, uploadBase64Common } from '@/service/api';
import { Widget, DomUtil } from '@cesium-extends/common';
import ZoomController from '@cesium-extends/zoom-control';
import { MouseTooltip } from '@cesium-extends/tooltip';
import statusBar from '@/components/cesium/status-bar.vue';
import geoMeasureKit from '@/components/cesium/geo-measure-kit/index.vue';
import { useAirportStore } from '@/store/modules/airport';
import airLineInfo from './modules/airline-info.vue';
import airLineCreate from './modules/airline-creat.vue';
import { ArrowBack, InformationCircleSharp, Locate } from '@vicons/ionicons5';
import { NButton, useMessage, useModal } from 'naive-ui';

// import vis3d from "./vis3d.js";
// import mars3d from 'mars3d';

const airportStore = useAirportStore();
const authStore = useAuthStore();
const { sysInfo } = authStore;
const message = useMessage();
const route = useRoute();
const { routerBack } = useRouterPush();
const router = useRouter();
// cesium viewer 初始化
let viewer: Cesium.Viewer;
// const noFlyZoneDataSource = reactive(new Cesium.CustomDataSource('NoFlyZone'));
// const drawAirLineLayer = reactive(new Cesium.CustomDataSource('AirLine'));

let handler: Cesium.ScreenSpaceEventHandler | null = null;
const loaded = ref(false);
// 是否为编辑 编辑-false, 新增-true
const editAirLineID = ref<LocationQueryValue[] | string>();
// 机场列表
const airportDeviceList = reactive<Api.Airport.AirportDeviceInfo[]>([])
// 航点设置栏的展示
const airlineSetModalShow = ref(false);
const relativeHeight = ref(100);
let lengthArr = ref(0)

// ↓↓↓↓↓↓↓↓↓↓↓↓↓↓航点航线绘制相关↓↓↓↓↓↓↓↓↓↓↓↓↓↓

// 用于存储当前绘制的点坐标集合
const activeShapePoints = reactive<Cesium.Cartesian3[]>([]);
// 用于存储浮动点实体（即在鼠标移动时临时显示的点）
const floatingPoint = ref<Cesium.Entity | null>(null);
// 用于存储当前正在绘制的线实体
const activeShape = ref<Cesium.Entity | null>(null);
// 用于存储所有绘制的点实体
const pointEntities = ref<Cesium.Entity[]>([]);
// 用于计数绘制的点的数量
const pointCounter = ref<number>(1);
// 用于存储距离标签实体（标记两点之间的距离）
const distanceLabels = ref<Cesium.Entity[]>([]);

// ↑↑↑↑↑↑↑↑↑↑↑↑↑航点航线绘制相关↑↑↑↑↑↑↑↑↑↑↑↑↑↑

// 存储航点的经纬度数组
const waypointLatLngArray = ref<Api.AirLine.WayPointList[]>([]);
const currentWaypointIndex = ref<number>(0);
let heightArr: Array<number> = reactive([])
const heightArrParams = ref<number[]>([])

// 存储预计飞行里程
const sumFlyDistance = ref<number>(0);
const airLineCreateRef = ref();

// const billImage = ''
import billImage from '@/assets/imgs/waypoint-base64';
// 初始化地图
const childRef = ref();
const initCesium = () => {
  viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
  const data = childRef.value?.childMethod(viewer);
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; //修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  // viewer.scene.globe.depthTestAgainstTerrain = true;  //启用深度测试
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK); // 取消双击事件
  // loaded = true;
  // 修改图层
  // viewer.imageryLayers.removeAll();

  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider); // 添加注记图层
  // viewer.imageryLayers.raiseToTop(tdtNoteLayer);//将注记图层置顶

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置相机位置为当前定位点
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(parseFloat(sysInfo.webConfig.centerLongitude), parseFloat(sysInfo.webConfig.centerLatitude), 10000), // 设置目的地高度/米
    orientation: {
      // heading: Cesium.Math.ZERO,
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });

  // 加载禁飞区JSON文件并绘制禁飞区
  loadJSONAndDrawNoFlyZone();

  // 启动航线绘制
  drawLineRoad();

  // 添加鼠标移动事件监听
  const mouseTooltip = new MouseTooltip(viewer, {
    content: '右键删除该点，拖拽移动该点',
    offset: [0, -5]
  });

  viewer.screenSpaceEventHandler.setInputAction((movement: any) => {
    const pick = viewer.scene.pick(movement.endPosition);
    if (pick && pick.id && pick.id.description?.getValue() === "waypoint") {
      mouseTooltip.showAt(movement.endPosition, "右键删除该点，拖拽移动该点");
    } else {
      mouseTooltip.hide();
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

}


// 处理航点的点击事件，展示该点的航点信息
function handlePointClick(clickEvent: Cesium.Event, pickedObject: any) {
  // 获取点击的 ID，index = ID - 1
  const index = Number((pickedObject.id as Cesium.Entity as { id: string }).id) - 1;
  currentWaypointIndex.value = index;
  // 显示航点设置弹窗
  handelAirlineSetModalShow(true);
}

// 可以在父组件添加深度watch来调试
watch(heightArrParams.value, (newVal: any) => {
  console.log('高度数组变化:', newVal)
}, { deep: true })
// 新增响应式变量控制绘制状态
const isDrawingEnabled = ref(true);
// 控制绘制状态+
const handleClickStatus = (status: boolean) => {
  isDrawingEnabled.value = status
}
// 绘制点和线路
function drawLineRoad() {
  handler = new Cesium.ScreenSpaceEventHandler(viewer!.canvas);

  let selectedPoint: Cesium.Entity | null = null;
  let selectedPointIndex: number | null = null;
  let isDraggingPoint = false; // 新增：标记是否正在拖拽点

  // 鼠标左键点击事件
  handler.setInputAction((event: any) => {
    if (!isDrawingEnabled.value) return;
    // 获取点击位置的地球坐标
    const earthPosition = viewer!.scene.pickPosition(event.position);
    // 判断点击是否为已有的实体
    const pick = viewer.scene.pick(event.position);
    // 如果点击在空白处且位置有效
    if (Cesium.defined(earthPosition) && (!pick || pick.id.description._value !== "waypoint")) {
      heightArrParams.value.push(100)
      // 新增点
      if (activeShapePoints.length === 0) {
        // 如果是第一个点，创建浮动点并开始绘制线
        floatingPoint.value = createPoint(earthPosition);
        activeShapePoints.push(earthPosition);
        // 动态更新线的坐标
        const dynamicPositions = new Cesium.CallbackProperty(() => {
          return activeShapePoints;
        }, false);
        // 绘制
        drawShape(dynamicPositions);
      } else {
        // 添加新的点并计算距离
        const lastPoint = activeShapePoints[activeShapePoints.length - 1];
        if (lastPoint) {
          const { distanceLabel, distanceNumber } = createDistanceLabel(lastPoint, earthPosition);
          sumFlyDistance.value = (sumFlyDistance.value + Number(distanceNumber));
          distanceLabels.value.push(distanceLabel); // 将距离标签添加到数组中

          activeShapePoints.push(earthPosition); // 添加新的点
          createPoint(earthPosition); // 绘制新点
          updatelinePositions(); // 更新线的坐标
        }
      }
    } else {
      // 点击已有的点，展示该点的航点信息
      handlePointClick(event, pick);
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

  // 鼠标右键点击事件处理
  handler.setInputAction((event: any) => {
    if (!isDrawingEnabled.value) return;
    const pick = viewer.scene.pick(event.position);
    if (pick && pick.id && pick.id.description._value === "waypoint") {
      const index = Number((pick.id as Cesium.Entity as { id: string }).id) - 1;
      if (index >= 0 && index < activeShapePoints.length) {
        heightArrParams.value.splice(index, 1); // 删除索引的元素（修改原数组）
        // console.log('===shanchu', heightArrParams.value)
        const pointToRemove = activeShapePoints[index];

        if (pointToRemove) {
          const entityToRemove = pointEntities.value[index];
          viewer!.entities.remove(entityToRemove); // 移除点实体
          pointEntities.value.splice(index, 1); // 从数组中移除点实体

          // 删除第一个点
          if (index === 0) {
            // 删除第一个点
            if (activeShapePoints.length > 0) {
              const firstPointEntity = pointEntities.value.find(entity =>
                Cesium.Cartesian3.equals(entity?.position?.getValue(), activeShapePoints[0])
              );
              if (firstPointEntity) {
                viewer!.entities.remove(firstPointEntity); // 移除第一个点的实体
                activeShapePoints.splice(0, 1);
              }

              if (distanceLabels.value.length > 0) {
                const firstLabelToRemove = distanceLabels.value[0];
                if (firstLabelToRemove && firstLabelToRemove.label) {
                  const distanceNumber = Number(firstLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                  sumFlyDistance.value = parseFloat((sumFlyDistance.value - distanceNumber).toFixed(2));
                  viewer!.entities.remove(firstLabelToRemove); // 移除第一个距离标签
                  distanceLabels.value.splice(0, 1);
                }
              }
            }
          } else {
            if (index > 0) {
              const beforeLabelToRemove = distanceLabels.value[index - 1];
              if (beforeLabelToRemove && beforeLabelToRemove.label) {
                const distanceNumber = Number(beforeLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                sumFlyDistance.value = parseFloat((sumFlyDistance.value - distanceNumber).toFixed(2));
                viewer!.entities.remove(beforeLabelToRemove); // 移除 n-1 与 n 之间的距离标签
                distanceLabels.value.splice(index - 1, 1);
              }
            }

            if (index < activeShapePoints.length - 1) {
              const afterLabelToRemove = distanceLabels.value[index - 1];
              if (afterLabelToRemove && afterLabelToRemove.label) {
                const distanceNumber = Number(afterLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                sumFlyDistance.value = parseFloat((sumFlyDistance.value - distanceNumber).toFixed(2));
                viewer!.entities.remove(afterLabelToRemove); // 移除 n 与 n+1 之间的距离标签
                distanceLabels.value.splice(index - 1, 1);
              }
            }
          }

          pointCounter.value--;

          // 重新连接前一个点和后一个点
          if (index > 0 && index < activeShapePoints.length - 1) {
            const previousPoint = activeShapePoints[index - 1];
            const nextPoint = activeShapePoints[index + 1];
            if (previousPoint && nextPoint) {
              activeShapePoints.splice(index, 1); // 从数组中移除点
              const { distanceLabel, distanceNumber } = createDistanceLabel(previousPoint, nextPoint);
              sumFlyDistance.value = sumFlyDistance.value + Number(distanceNumber);
              distanceLabels.value.splice(index - 1, 0, distanceLabel); // 插入新的距离标签
            }
          } else {
            activeShapePoints.splice(index, 1); // 从数组中移除点
          }

          updatelinePositions(); // 更新线条位置
          waypointLatLngArray.value.splice(index, 1); // 从数组中移除点的经纬度

          // 创建一个新的实体数组
          const newPointEntities: Cesium.Entity[] = [];

          // 遍历现有的实体并重新创建
          pointEntities.value.forEach((entity, i) => {
            const position = entity?.position?.getValue();
            const label = `${i + 1}`;

            // 删除旧的实体
            viewer.entities.remove(entity);

            // 创建新的实体
            const newEntity = viewer.entities.add({
              id: `${i + 1}`,
              position: position,
              label: {
                text: label, // 标签内容为点的编号
                font: 'bold 14px sans-serif', // 字体加粗
                fillColor: Cesium.Color.WHITE, // 标签文本颜色为白色
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER, // 标签水平对齐方式
                verticalOrigin: Cesium.VerticalOrigin.CENTER, // 标签垂直对齐方式
                eyeOffset: new Cesium.Cartesian3(0, 0, 0), // 视觉偏移，使标签居中
                backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'), // 背景颜色
                showBackground: true, // 不显示默认背景
                backgroundPadding: new Cesium.Cartesian2(2, 2), // 修改边距
              },
              description: 'waypoint',
              billboard: {
                image: billImage, // 圆形背景图像的路径
                width: 35, // 圆形的宽度
                height: 35, // 圆形的高度
                horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                verticalOrigin: Cesium.VerticalOrigin.CENTER, // 将背景的垂直对齐方式也改为 CENTER
                scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5), // 根据距离缩放
              },
            });

            // 将新实体添加到新的数组中
            newPointEntities.push(newEntity);
          });

          // 替换旧的实体列表
          pointEntities.value = newPointEntities;

          airPointActionArr.splice(index, 1)
        }
      }
    }
  }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);


  // 鼠标按下事件
  handler.setInputAction((event: any) => {
    if (!isDrawingEnabled.value) return;
    const pick = viewer.scene.pick(event.position);
    if (pick && pick.id && pick.id.description && pick.id.description._value === "waypoint") {
      selectedPoint = pick.id;
      selectedPointIndex = Number(pick.id.id) - 1;
      isDraggingPoint = true; // 标记开始拖拽
      viewer.scene.screenSpaceCameraController.enableInputs = false; // 禁用地图拖拽

      // 隐藏被拖拽航点两侧的标签
      if (selectedPointIndex > 0) {
        distanceLabels.value[selectedPointIndex - 1].show = false;
      }
      if (selectedPointIndex < distanceLabels.value.length) {
        distanceLabels.value[selectedPointIndex].show = false;
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

  // 鼠标移动事件
  handler.setInputAction((movement: any) => {
    if (!isDrawingEnabled.value) return;
    if (isDraggingPoint && selectedPoint && selectedPointIndex !== null) {
      const newPosition = viewer.scene.pickPosition(movement.endPosition);
      if (newPosition) {
        // 更新点的位置
        selectedPoint.position = new Cesium.ConstantPositionProperty(newPosition);
        activeShapePoints[selectedPointIndex] = newPosition;

        // 更新经纬度数组
        const cartographic = Cesium.Cartographic.fromCartesian(newPosition);
        waypointLatLngArray.value[selectedPointIndex] = {
          latitude: Cesium.Math.toDegrees(cartographic.latitude),
          longitude: Cesium.Math.toDegrees(cartographic.longitude),
          altitude: cartographic.height
        };

        // 更新线的位置
        updatelinePositions();

        // 强制触发场景渲染
        viewer.scene.requestRender();
      }
    }
  }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

  // 鼠标释放事件
  handler.setInputAction(() => {
    if (!isDrawingEnabled.value) return;
    if (isDraggingPoint && selectedPoint && selectedPointIndex !== null) {
      isDraggingPoint = false; // 标记结束拖拽
      viewer.scene.screenSpaceCameraController.enableInputs = true; // 恢复地图拖拽

      // 显示被拖拽航点两侧的标签
      if (selectedPointIndex > 0) {
        distanceLabels.value[selectedPointIndex - 1].show = true;
      }
      if (selectedPointIndex < distanceLabels.value.length) {
        distanceLabels.value[selectedPointIndex].show = true;
      }

      // 更新标签的位置和内容
      const newPosition = selectedPoint.position?.getValue();
      if (selectedPointIndex > 0 && newPosition) {
        const prevPoint = activeShapePoints[selectedPointIndex - 1];
        const { distanceLabel, distanceNumber } = createDistanceLabel(prevPoint, newPosition);
        viewer.entities.remove(distanceLabels.value[selectedPointIndex - 1]);
        distanceLabels.value[selectedPointIndex - 1] = distanceLabel;
        sumFlyDistance.value += Number(distanceNumber);
      }

      if (selectedPointIndex < activeShapePoints.length - 1 && newPosition) {
        const nextPoint = activeShapePoints[selectedPointIndex + 1];
        const { distanceLabel, distanceNumber } = createDistanceLabel(newPosition, nextPoint);
        viewer.entities.remove(distanceLabels.value[selectedPointIndex]);
        distanceLabels.value[selectedPointIndex] = distanceLabel;
        sumFlyDistance.value += Number(distanceNumber);
      }
    }
  }, Cesium.ScreenSpaceEventType.LEFT_UP);
}
//获取高度
function getHeight(value: number[]) {

  heightArrParams.value = [...value] // 保持响应式
  console.log('更新高度111:', heightArrParams.value, value)
}
// 创建航点，支持在指定位置插入
function createPoint(worldPosition: Cesium.Cartesian3, index?: number) {

  console.log("createPoint: ", { worldPosition, index }, pointCounter.value);

  const label = index !== undefined ? (index + 1).toString() : pointCounter.value.toString();

  const point = viewer!.entities.add({
    id: label, // 使用 label 作为 id
    position: worldPosition,
    label: {
      text: label,
      font: 'bold 14px sans-serif',
      fillColor: Cesium.Color.WHITE,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      eyeOffset: new Cesium.Cartesian3(0, 0, 0),
      backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'),
      showBackground: true,
      backgroundPadding: new Cesium.Cartesian2(2, 2),
    },
    billboard: {
      image: billImage,
      width: 35,
      height: 35,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.CENTER,
      scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5),
    },
    description: 'waypoint',
  });

  const cartographicPosition = Cesium.Cartographic.fromCartesian(worldPosition);
  const latitude = Cesium.Math.toDegrees(cartographicPosition.latitude);
  const longitude = Cesium.Math.toDegrees(cartographicPosition.longitude);
  const altitude = cartographicPosition.height;

  waypointLatLngArray.value.push({ latitude, longitude, altitude });
  pointEntities.value.push(point);
  pointCounter.value++;
  return point;
}

// 绘制线
function drawShape(positionData: Cesium.CallbackProperty) {
  activeShape.value = viewer!.entities.add({
    polyline: {
      width: 5,
      material: Cesium.Color.YELLOW,
      show: true,
      clampToGround: true,
      positions: positionData,
    },
    description: 'wayline',
  });
  return activeShape.value;
}

// 更新线的位置
function updatelinePositions() {
  if (activeShape.value && activeShape.value.polyline) {
    activeShape.value.polyline.positions = new Cesium.CallbackProperty(() => activeShapePoints, false);
  }
}

// 计算两点之间的距离
function calculateDistance(p1: Cesium.Cartesian3, p2: Cesium.Cartesian3): number {
  return Cesium.Cartesian3.distance(p1, p2);
}

// 创建距离标签，显示在两点之间，并将距离(number)返回出去
function createDistanceLabel(p1: Cesium.Cartesian3, p2: Cesium.Cartesian3) {
  const midpoint = Cesium.Cartesian3.add(p1, p2, new Cesium.Cartesian3());
  Cesium.Cartesian3.multiplyByScalar(midpoint, 0.5, midpoint);

  const distance = calculateDistance(p1, p2);
  const distanceNumber = distance.toFixed(2);
  const label = `${distanceNumber} m`;

  // 使用 CallbackProperty 动态更新标签位置
  const positionProperty = new Cesium.CallbackProperty(() => {
    const midpoint = Cesium.Cartesian3.add(p1, p2, new Cesium.Cartesian3());
    Cesium.Cartesian3.multiplyByScalar(midpoint, 0.5, midpoint);
    return midpoint;
  }, false) as Cesium.CallbackPositionProperty;

  const distanceLabel = viewer!.entities.add({
    position: positionProperty, // 动态更新位置
    label: {
      text: label,
      font: '12px sans-serif',
      fillColor: Cesium.Color.BLACK,
      outlineColor: Cesium.Color.BLACK,
      outlineWidth: 1,
      horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
      verticalOrigin: Cesium.VerticalOrigin.TOP,
      eyeOffset: new Cesium.Cartesian3(0, 0, 1),
      showBackground: true,
      backgroundColor: Cesium.Color.WHITE.withAlpha(0.8),
      backgroundPadding: new Cesium.Cartesian2(2, 5),
      disableDepthTestDistance: Number.POSITIVE_INFINITY, // 禁用深度测试
    },
  });

  return { distanceLabel, distanceNumber };
}

// 生成机场范围椭圆的轮廓点
function computeEllipsePositions(
  center: { longitude: number; latitude: number },
  semiMajorAxis: number,
  semiMinorAxis: number,
  granularity: number
): Cesium.Cartesian3[] {
  const positions: Cesium.Cartesian3[] = [];
  for (let i = 0; i < 360; i += granularity) {
    const radians = Cesium.Math.toRadians(i);
    const x = semiMajorAxis * Math.cos(radians);
    const y = semiMinorAxis * Math.sin(radians);
    // 将米转换为经纬度
    const position = Cesium.Cartesian3.fromDegrees(center.longitude + x / 111319.9, center.latitude + y / 111319.9);
    positions.push(position);
  }
  // 闭合椭圆，使首尾相连
  positions.push(positions[0]);
  return positions;
}

// 绘制机场点位及范围
function drawAirportPoint(points: Api.Airport.MachineNestInfo[]) {
  points.forEach((point: Api.Airport.MachineNestInfo) => {
    const { longitude, latitude, distance, deviceName } = point;
    const center = { longitude, latitude }; // 明确指定中心点类型

    // 生成椭圆边界点
    const positions = computeEllipsePositions(center, distance, distance, 5);

    // 绘制机场范围（用Polygon模拟椭圆）
    viewer.entities.add({
      polygon: {
        hierarchy: positions,  // 多边形的顶点
        material: Cesium.Color.fromCssColorString('#1177fb').withAlpha(0.15), // 设置填充颜色
        perPositionHeight: false,  // 使多边形所有点贴地
      },
      description: 'airpoint',
    });

    // 绘制椭圆边框
    viewer.entities.add({
      polyline: {
        positions: positions,  // 线的顶点位置，与多边形顶点一致
        width: 5,  // 边框宽度
        material: Cesium.Color.fromCssColorString('#1177fb'),  // 边框颜色
        clampToGround: true,  // 使边框贴地
      },
      description: 'airpointborder',
    });

    // 绘制机场图标
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      billboard: {
        image: marker,
        scale: 0.13
      },
      description: 'airpointicon',
    });

    // 绘制机场标签
    viewer.entities.add({
      position: Cesium.Cartesian3.fromDegrees(longitude, latitude),
      label: {
        text: deviceName,
        font: 'bold 18px "微软雅黑", Arial, Helvetica, sans-serif',
        fillColor: Cesium.Color.fromCssColorString('#1177fb').withAlpha(1.0),
        outlineColor: Cesium.Color.WHITE.withAlpha(1.0),
        style: Cesium.LabelStyle.FILL_AND_OUTLINE,
        outlineWidth: 8.0,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        pixelOffset: new Cesium.Cartesian2(0, -50), // 向上偏移50像素
      },
      description: 'airpointtext',
    });
  });
}

// 清除绘制的航点和航线
function clearEntities() {
  // 清除之前绘制的点
  pointEntities.value.forEach((entity) => {
    viewer!.entities.remove(entity);
  });
  pointEntities.value = []; // 清空点实体数组

  // 清除之前绘制的线
  if (activeShape.value) {
    viewer!.entities.remove(activeShape.value);
    activeShape.value = null;
  }

  // 清除距离标签
  distanceLabels.value.forEach((label) => {
    viewer!.entities.remove(label);
  });
  distanceLabels.value = [];
}

// 获取已激活的机场列表
async function getAirportDeviceList() {
  const { data } = await fetchAirportDeviceList();
  Object.assign(airportDeviceList, data.rows);
  drawAirportPoint(data.rows);
}

// 控制航点设置弹窗的展示
function handelAirlineSetModalShow(flag: boolean) {
  airlineSetModalShow.value = flag;
}


// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
  try {
    const response = await fetch('/defaultNoFlyZone.json');
    const data = await response.json();
    // 绘制
    addPolygons(data.features);
  } catch (error) {
    console.error('Error loading JSON data:', error);
  }
};

// 绘制禁飞区
function addPolygons(features: any[]) {
  features.forEach(feature => {
    const polygon = feature.geometry.coordinates[0];
    const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    viewer.entities.add({
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString('red').withAlpha(0.35),
      },
      rectangle: { zIndex: 1 },
      description: 'nofly',
    });
  });
};

// ceium截图功能
async function takeScreenshot(): Promise<{ url: string }> {
  let screenshot;

  return new Promise((resolve, reject) => {
    const postRenderHandler = async function () {
      //screenshot = viewer.scene.canvas.toDataURL();

      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');

      canvas.width = viewer.scene.canvas.width / 2;
      canvas.height = viewer.scene.canvas.height / 2;

      ctx?.drawImage(viewer.scene.canvas, 0, 0, canvas.width, canvas.height);

      let compressedScreenshotUrl = canvas.toDataURL('image/png', 0.7);
      localStorage.setItem("newCoverBase64", compressedScreenshotUrl);
      viewer.scene.postRender.removeEventListener(postRenderHandler);
      const { data, error } = await uploadBase64Common(compressedScreenshotUrl);

      resolve({ url: data.newFileName });
    };

    viewer.scene.postRender.addEventListener(postRenderHandler);

    // 等待一帧渲染完成
    requestAnimationFrame(() => {
      viewer.scene.postRender.removeEventListener(postRenderHandler);
    });
  });
}


// 保存
const onSaveAirLine = async (lineInfo: { surfaceRelativeHeight: string, transitionalSpeed: string, flightName: string }) => {
  message.info("正在保存...");

  const { url } = await takeScreenshot()


  const { surfaceRelativeHeight, transitionalSpeed, flightName } = lineInfo;

  console.log("flightPointActionDTOList: ", airLineCreateRef.value.wayActionArr);


  let flightPointDTOList: { geoJson: { type: "Point", coordinates: Api.AirLine.WayPointList }, flightPointActionDTOList: [] }[] = []
  let arr: any[] = JSON.parse(JSON.stringify(airLineCreateRef.value.wayActionArr.values));

  flightPointDTOList = arr.map((actions, index) => {
    const coordinates = waypointLatLngArray.value[index];
    const processedActions = actions.map((item: { actionActuatorFunc: string; params: { payloadLensIndex: any[] | string; }; }) => {
      if (item && (item.actionActuatorFunc === 'gimbalRotate1' || item.actionActuatorFunc === 'gimbalRotate2')) {
        item.actionActuatorFunc = 'gimbalRotate';
      }
      if (item.actionActuatorFunc === "takePhoto" || item.actionActuatorFunc === "startRecord") {
        // item.params.payloadLensIndex = item.params.payloadLensIndex.join(',')
        if (Array.isArray(item.params.payloadLensIndex)) {
          item.params.payloadLensIndex = item.params.payloadLensIndex.join(','); // 将数组转换为字符串
        }
      }
      return item;
    });
    const heightParams = [...heightArrParams.value][index]
    return {
      height: heightParams,
      geoJson: {
        type: "Point", coordinates
      },
      flightPointActionDTOList: processedActions
    };
  });
  const params = {
    flightType: 1,
    flightImage: url,
    surfaceRelativeHeight: surfaceRelativeHeight,
    transitionalSpeed: transitionalSpeed,
    flightName: flightName,
    flightPointDTOList: flightPointDTOList,
    ...(editAirLineID ? { flightId: editAirLineID.value } : {})
  };
  const { error } = await sendAirLineSaveInfo(params);
  if (!error) {
    message.success('保存成功');
    router.back();
  }
}
// 创建节流包装器函数
const debouncedSave = (() => {
  let timer: ReturnType<typeof setTimeout> | null = null;
  let pendingExecution: (() => Promise<void>) | null = null;

  return async (lineInfo: Parameters<typeof onSaveAirLine>[0]) => {
    // 保存最新的调用
    pendingExecution = () => onSaveAirLine(lineInfo);

    // 清除之前的定时器
    if (timer) {
      clearTimeout(timer);
    }

    // 设置新的定时器
    timer = setTimeout(async () => {
      if (pendingExecution) {
        const exec = pendingExecution;
        pendingExecution = null;
        await exec();
      }
      timer = null;
    }, 500); // 500ms防抖延迟
  };
})();

// 使用方式
const handleClickThrottle = debouncedSave;
const airLineConfig = ref<Api.AirLine.AirLineConfig>({}); // 整条航线基本信息
const airPointActionArr = reactive<any[]>([]); // 各个航点的动作

// 修改更新航点动作的函数
function updateWayActions(newActions: any[]) {
  // 确保每个航点的动作数组都是独立的副本
  const updatedActions = newActions.map(actions => Array.isArray(actions) ? [...actions] : []);
  airPointActionArr.splice(0, airPointActionArr.length, ...updatedActions);
}

// 修改 getAirLine 函数
async function getAirLine(editAirLineID: any) {
  const { data } = await fetchAirLineSaveInfo(editAirLineID);
  const { surfaceRelativeHeight, transitionalSpeed, flightName, flightPointDTOList } = data;

  // 反显整条航线基本信息
  airLineConfig.value.surfaceRelativeHeight = surfaceRelativeHeight;
  airLineConfig.value.transitionalSpeed = transitionalSpeed;
  airLineConfig.value.flightName = flightName;

  // 反显 flightPointDTOList 数据
  const wayActionArr = flightPointDTOList.map((point: { flightPointActionDTOList: any[]; }) => {
    return point.flightPointActionDTOList.map(action => {
      const newAction = { ...action };
      if (newAction.actionActuatorFunc === 'gimbalRotate') {
        if (newAction.params.gimbalPitchRotateEnable !== undefined) {
          newAction.actionActuatorFunc = 'gimbalRotate1';
        } else if (newAction.params.gimbalYawRotateEnable !== undefined) {
          newAction.actionActuatorFunc = 'gimbalRotate2';
        }
      }
      return newAction;
    });
  });

  const arr = flightPointDTOList.map((item: { height: any; }) => item.height);
  heightArrParams.value = [...arr]

  // 使用深拷贝更新 airPointActionArr
  const updatedActions = wayActionArr.map((actions: any) => [...actions]);
  airPointActionArr.splice(0, airPointActionArr.length, ...updatedActions);

  // 更新坐标
  const waypointarr = flightPointDTOList.map((point: { geoJson: { coordinates: any; }; }) => point.geoJson.coordinates);

  // 如果有航点，将地图中心跳转到第一个点
  if (waypointarr.length > 0) {
    const { latitude, longitude } = waypointarr[0];

    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(longitude, latitude, 8000), // 经度、纬度、高度
      duration: 1.5 // 动画持续时间（秒）
    });
  }

  redrawLineRoad(waypointarr); // 重新绘制点和线
}

// 根据更新后的 `waypointLatLngArray` 重新绘制点和线
function redrawLineRoad(waypointarr: Api.AirLine.WayPointList[]) {
  // clearEntities();
  // 遍历 waypointLatLngArray，重新生成点和线

  waypointarr.forEach((point, index) => {
    const { latitude, longitude, altitude } = point;
    const cartesianPosition = Cesium.Cartesian3.fromDegrees(longitude, latitude, altitude);

    if (index === 0) {
      // 第一个点，创建浮动点并开始绘制线
      floatingPoint.value = createPoint(cartesianPosition);
      activeShapePoints.push(cartesianPosition);
    } else {
      // 添加新的点并计算距离
      const lastPoint = activeShapePoints[activeShapePoints.length - 1];
      if (lastPoint) {
        const { distanceLabel, distanceNumber } = createDistanceLabel(lastPoint, cartesianPosition);
        distanceLabels.value.push(distanceLabel); // 将距离标签添加到数组中
        sumFlyDistance.value = (sumFlyDistance.value + Number(distanceNumber));
      }
      activeShapePoints.push(cartesianPosition); // 添加新的点
      createPoint(cartesianPosition); // 绘制新点

    }

  });

  // 绘制线，动态更新线的坐标
  const dynamicPositions = new Cesium.CallbackProperty(() => {
    return activeShapePoints;
  }, false);

  drawShape(dynamicPositions); // 调用绘制线函数

}


onMounted(() => {
  // 初始化地图
  initCesium();
  // 获取已激活的机场列表
  getAirportDeviceList();
  if (route.query.id) { // 存在航线id则为编辑
    editAirLineID.value = route.query.id;
    getAirLine(editAirLineID.value);
  }
});

onBeforeUnmount(() => {
  // 卸载鼠标右键点击事件
  if (handler) {
    handler.destroy();
    handler = null;
  }
})
</script>

<style scoped>
#container {
  padding: 0;
}

/* #status-bar {
  bottom: 65px;
  left: 330px;
} */

.n-modal .n-card,
.n-drawer .n-card {
  background-color: var(--n-border-color);
}
</style>
