<script setup lang="ts">
import { computed, reactive, ref, onMounted } from 'vue';
import { $t } from '@/locales';
import { loginModuleRecord } from '@/constants/app';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useAuthStore } from '@/store/modules/auth';

defineOptions({
  name: 'PwdLogin'
});

const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();

interface FormModel {
  userName: string;
  password: string;
}

const model: FormModel = reactive({
  userName: '',
  password: ''
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  // 如果不应用 i18n，则可以在不使用计算的情况下定义它
  const { formRules } = useFormRules();

  return {
    userName: [
      { required: true, message: '请输入用户名' },
      {
        pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]{2,20}$/,
        message: '用户名需为2-20位字符(支持中英文、数字、下划线)'
      }
    ],
    password: [
      { required: true, message: '请输入密码' },
      { min: 6, max: 18, message: '密码长度为6-18位' }
    ]
  };
});

async function handleSubmit() {
  await validate();
  await authStore.login(model.userName, model.password);

  if (rememberMe.value) {
    localStorage.setItem('username', model.userName);
    localStorage.setItem('password', model.password);
  } else {
    localStorage.removeItem('username');
    localStorage.removeItem('password');
  }
}

type AccountKey = 'super' | 'admin' | 'user';

interface Account {
  key: AccountKey;
  label: string;
  userName: string;
  password: string;
}

const rememberMe = ref(false);

// 在组件挂载时检查本地存储
onMounted(() => {
  const savedUsername = localStorage.getItem('username');
  const savedPassword = localStorage.getItem('password');
  if (savedUsername) {
    model.userName = savedUsername;
  }
  if (savedPassword) {
    model.password = savedPassword;
  }
});


</script>

<template>
  <div>
    <NForm ref="formRef" :model="model" :rules="rules" @keyup.enter="handleSubmit" size="large" :show-label="false">
      <NFormItem path="userName">
        <NInput v-model:value="model.userName" :placeholder="$t('page.login.common.userNamePlaceholder')" />
      </NFormItem>
      <NFormItem path="password">
        <NInput v-model:value="model.password" type="password" show-password-on="click"
          :placeholder="$t('page.login.common.passwordPlaceholder')" />
      </NFormItem>
      <NSpace vertical :size="24">
        <div class="flex-y-center justify-between">
          <NCheckbox v-model="rememberMe">{{ $t('page.login.pwdLogin.rememberMe') }}</NCheckbox>
          <!-- <NButton quaternary @click="toggleLoginModule('reset-pwd')">
          {{ $t('page.login.pwdLogin.forgetPassword') }}
        </NButton> -->
        </div>
        <NButton type="primary" size="large" round block :loading="authStore.loginLoading" @click="handleSubmit">
          {{ $t('common.confirm') }}
        </NButton>
        <!-- 其他账号登录 -->
        <!-- <NDivider class="text-14px text-#666 !m-0">{{ $t('page.login.pwdLogin.otherAccountLogin') }}</NDivider>
      <div class="flex-center gap-12px">
        <NButton v-for="item in accounts" :key="item.key" type="primary" @click="handleAccountLogin(item)">
          {{ item.label }}
        </NButton>
      </div> -->
      </NSpace>
    </NForm>
    <div class="flex-y-center justify-between mt-20px">
      <NButton class="flex-1" block @click="toggleLoginModule('code-login')">
        {{ $t(loginModuleRecord['code-login']) }}
      </NButton>
      <!-- <NButton class="flex-1" block @click="toggleLoginModule('register')">
          {{ $t(loginModuleRecord.register) }}
        </NButton> -->
    </div>
  </div>
</template>

<style scoped></style>
