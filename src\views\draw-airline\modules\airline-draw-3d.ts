import * as Cesium from 'cesium';
import { ref, reactive } from 'vue';

export default class AirlineDraw3D {
  private viewer: Cesium.Viewer;
  private handler: Cesium.ScreenSpaceEventHandler | null = null;
  private isDrawingEnabled = true;
  private billImage: string;

  // 用于存储当前绘制的点坐标集合
  public activeShapePoints = reactive<Cesium.Cartesian3[]>([]);
  // 用于存储浮动点实体（即在鼠标移动时临时显示的点）
  public floatingPoint = ref<Cesium.Entity | null>(null);
  // 用于存储当前正在绘制的线实体
  public activeShape = ref<Cesium.Entity | null>(null);
  // 用于存储所有绘制的点实体
  public pointEntities = ref<Cesium.Entity[]>([]);
  // 用于计数绘制的点的数量
  public pointCounter = ref<number>(1);
  // 用于存储距离标签实体（标记两点之间的距离）
  public distanceLabels = ref<Cesium.Entity[]>([]);
  // 存储航点的经纬度数组
  public waypointLatLngArray = ref<Api.AirLine.WayPointList[]>([]);
  // 存储预计飞行里程
  public sumFlyDistance = ref<number>(0);
  // 存储高度参数
  public heightArrParams = ref<number[]>([]);
  // 存储垂直线实体
  public verticalLineEntities = ref<Cesium.Entity[]>([]);

  // 拖拽相关变量
  private selectedPoint: Cesium.Entity | null = null;
  private selectedPointIndex: number | null = null;
  private isDraggingPoint = false;
  private dragStartHeight = 0;
  private dragStartY = 0;
  private isAltDrag = false;

  constructor(viewer: Cesium.Viewer, billImage: string) {
    this.viewer = viewer;
    this.billImage = billImage;
  }

  /**
   * 设置绘制状态
   * @param status 是否启用绘制
   */
  public setDrawingEnabled(status: boolean): void {
    this.isDrawingEnabled = status;
  }

  /**
   * 初始化绘制功能
   * @param handlePointClick 点击航点的回调函数
   * @param defaultHeight 默认高度
   */
  public initDraw(handlePointClick: (event: Cesium.Event, pickedObject: any) => void, defaultHeight: number): void {
    this.handler = new Cesium.ScreenSpaceEventHandler(this.viewer.canvas);
    this.setupLeftClickHandler(handlePointClick, defaultHeight);
    this.setupRightClickHandler();
    this.setupDragHandlers();
    this.setupAltDragHandlers();
  }

  /**
   * 设置左键点击处理器
   */
  private setupLeftClickHandler(handlePointClick: (event: Cesium.Event, pickedObject: any) => void, defaultHeight: number): void {
    if (!this.handler) return;

    this.handler.setInputAction((event: any) => {
      if (!this.isDrawingEnabled) return;

      // 获取点击位置的地球坐标
      const earthPosition = this.viewer.scene.pickPosition(event.position);
      if (!Cesium.defined(earthPosition)) return;

      const cartographic = Cesium.Cartographic.fromCartesian(earthPosition);
      const height = defaultHeight;
      cartographic.height = height;

      // 重新生成带高度的三维坐标
      const fixedHeightPosition = Cesium.Cartesian3.fromRadians(
        cartographic.longitude,
        cartographic.latitude,
        cartographic.height
      );

      // 判断点击是否为已有的实体
      const pick = this.viewer.scene.pick(event.position);
      if (Cesium.defined(fixedHeightPosition) && (!pick || pick.id.description?._value !== "waypoint")) {
        this.heightArrParams.value.push(height);

        // 新增点
        if (this.activeShapePoints.length === 0) {
          // 如果是第一个点，创建浮动点并开始绘制线
          this.floatingPoint.value = this.createPoint(fixedHeightPosition);
          this.activeShapePoints.push(fixedHeightPosition);

          // 动态更新线的坐标
          const dynamicPositions = new Cesium.CallbackProperty(() => {
            return this.activeShapePoints;
          }, false);

          // 绘制
          this.drawShape(dynamicPositions);
        } else {
          // 添加新的点并计算距离
          const lastPoint = this.activeShapePoints[this.activeShapePoints.length - 1];
          if (lastPoint) {
            const { distanceLabel, distanceNumber } = this.createDistanceLabel(lastPoint, fixedHeightPosition);
            this.sumFlyDistance.value = (this.sumFlyDistance.value + Number(distanceNumber));
            this.distanceLabels.value.push(distanceLabel); // 将距离标签添加到数组中
            this.activeShapePoints.push(fixedHeightPosition); // 添加新的点

            this.updatelinePositions(); // 更新线的坐标
            this.createPoint(fixedHeightPosition); // 绘制新点
          }
        }

        // 绘制垂直线
        this.drawVerticalLine(fixedHeightPosition, this.activeShapePoints.length);
      } else {
        // 点击已有的点，展示该点的航点信息
        if (pick && pick.id && pick.id.description?._value === "waypoint") {
          handlePointClick(event, pick);
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
  }

  /**
   * 设置右键点击处理器（删除航点）
   */
  private setupRightClickHandler(): void {
    if (!this.handler) return;

    this.handler.setInputAction((event: any) => {
      if (!this.isDrawingEnabled) return;

      const pick = this.viewer.scene.pick(event.position);
      if (pick && pick.id && pick.id.description?._value === "waypoint") {
        const index = Number((pick.id as Cesium.Entity as { id: string }).id) - 1;

        // 删除垂直线相关实体
        this.removeVerticalLine(index);

        if (index >= 0 && index < this.activeShapePoints.length) {
          this.heightArrParams.value.splice(index, 1); // 删除索引的元素
          const pointToRemove = this.activeShapePoints[index];

          if (pointToRemove) {
            const entityToRemove = this.pointEntities.value[index];
            this.viewer.entities.remove(entityToRemove); // 移除点实体
            this.pointEntities.value.splice(index, 1); // 从数组中移除点实体

            // 处理删除第一个点的情况
            if (index === 0) {
              if (this.activeShapePoints.length > 0) {
                const firstPointEntity = this.pointEntities.value.find(entity =>
                  Cesium.Cartesian3.equals(entity?.position?.getValue(), this.activeShapePoints[0])
                );
                if (firstPointEntity) {
                  this.viewer.entities.remove(firstPointEntity); // 移除第一个点的实体
                  this.activeShapePoints.splice(0, 1);
                }

                if (this.distanceLabels.value.length > 0) {
                  const firstLabelToRemove = this.distanceLabels.value[0];
                  if (firstLabelToRemove && firstLabelToRemove.label) {
                    const distanceNumber = Number(firstLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                    this.sumFlyDistance.value = parseFloat((this.sumFlyDistance.value - distanceNumber).toFixed(2));
                    this.viewer.entities.remove(firstLabelToRemove); // 移除第一个距离标签
                    this.distanceLabels.value.splice(0, 1);
                  }
                }
              }
            } else {
              // 处理删除中间点的情况
              if (index > 0) {
                const beforeLabelToRemove = this.distanceLabels.value[index - 1];
                if (beforeLabelToRemove && beforeLabelToRemove.label) {
                  const distanceNumber = Number(beforeLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                  this.sumFlyDistance.value = parseFloat((this.sumFlyDistance.value - distanceNumber).toFixed(2));
                  this.viewer.entities.remove(beforeLabelToRemove); // 移除 n-1 与 n 之间的距离标签
                  this.distanceLabels.value.splice(index - 1, 1);
                }
              }

              if (index < this.activeShapePoints.length - 1) {
                const afterLabelToRemove = this.distanceLabels.value[index - 1];
                if (afterLabelToRemove && afterLabelToRemove.label) {
                  const distanceNumber = Number(afterLabelToRemove.label.text?.getValue().split('m')[0] || 0);
                  this.sumFlyDistance.value = parseFloat((this.sumFlyDistance.value - distanceNumber).toFixed(2));
                  this.viewer.entities.remove(afterLabelToRemove); // 移除 n 与 n+1 之间的距离标签
                  this.distanceLabels.value.splice(index - 1, 1);
                }
              }
            }

            this.pointCounter.value--;

            // 重新连接前一个点和后一个点
            if (index > 0 && index < this.activeShapePoints.length - 1) {
              const previousPoint = this.activeShapePoints[index - 1];
              const nextPoint = this.activeShapePoints[index + 1];
              if (previousPoint && nextPoint) {
                this.activeShapePoints.splice(index, 1); // 从数组中移除点
                const { distanceLabel, distanceNumber } = this.createDistanceLabel(previousPoint, nextPoint);
                this.sumFlyDistance.value = this.sumFlyDistance.value + Number(distanceNumber);
                this.distanceLabels.value.splice(index - 1, 0, distanceLabel); // 插入新的距离标签
              }
            } else {
              this.activeShapePoints.splice(index, 1); // 从数组中移除点
            }

            this.updatelinePositions(); // 更新线条位置
            this.waypointLatLngArray.value.splice(index, 1); // 从数组中移除点的经纬度

            // 重新创建所有点实体以更新编号
            this.recreatePointEntities();
          }
        }

        // 强制刷新场景
        this.viewer.scene.requestRender();
      }
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }

  /**
   * 设置拖拽处理器
   */
  private setupDragHandlers(): void {
    if (!this.handler) return;

    // 鼠标按下事件
    this.handler.setInputAction((event: any) => {
      if (!this.isDrawingEnabled) return;

      const pick = this.viewer.scene.pick(event.position);
      if (pick && pick.id && pick.id.description && pick.id.description._value === "waypoint") {
        const position = pick.id.position.getValue(Cesium.JulianDate.now());
        const cartographic = Cesium.Cartographic.fromCartesian(position);
        this.dragStartHeight = cartographic.height;
        this.isDraggingPoint = true;
        this.selectedPoint = pick.id;
        this.selectedPointIndex = Number(pick.id.id) - 1;

        // 标记开始拖拽
        this.viewer.scene.screenSpaceCameraController.enableInputs = false; // 禁用地图拖拽

        // 隐藏被拖拽航点两侧的标签
        if (this.selectedPointIndex > 0) {
          this.distanceLabels.value[this.selectedPointIndex - 1].show = false;
        }
        if (this.selectedPointIndex < this.distanceLabels.value.length) {
          this.distanceLabels.value[this.selectedPointIndex].show = false;
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN);

    // 鼠标移动事件
    this.handler.setInputAction((movement: any) => {
      if (!this.isDrawingEnabled) return;

      if (this.isDraggingPoint && this.selectedPoint && this.selectedPointIndex !== null) {
        const newPosition = this.viewer.scene.pickPosition(movement.endPosition);
        if (newPosition) {
          // 获取新位置的经纬度和高度
          const cartographic = Cesium.Cartographic.fromCartesian(newPosition);
          const fixedPosition = Cesium.Cartesian3.fromRadians(
            cartographic.longitude,
            cartographic.latitude,
            this.dragStartHeight // 使用拖拽开始时的高度
          );

          // 更新航点位置
          this.selectedPoint.position = new Cesium.ConstantPositionProperty(fixedPosition);
          this.activeShapePoints[this.selectedPointIndex] = fixedPosition;

          // 更新经纬度数组
          this.waypointLatLngArray.value[this.selectedPointIndex] = {
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            altitude: this.dragStartHeight,
          };

          // 更新垂直线的位置
          const verticalLine = this.verticalLineEntities.value.find(e =>
            e.id === `vertical_${this.selectedPointIndex}_line`
          );
          if (verticalLine?.polyline) {
            verticalLine.polyline.positions = new Cesium.CallbackProperty(() => [
              fixedPosition,
              Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                0 // 地面高度
              )
            ], false);
          }

          // 更新垂直线上的标签位置和内容
          const heightLabel = this.verticalLineEntities.value.find(e =>
            e.id === `vertical_${this.selectedPointIndex}_label`
          );
          if (heightLabel?.label) {
            heightLabel.position = new Cesium.ConstantPositionProperty(
              Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                this.dragStartHeight / 2 // 标签位置在垂直线中间
              )
            );
          }

          // 更新垂直地面的点位置
          const groundPoint = this.verticalLineEntities.value.find(e =>
            e.id === `vertical_${this.selectedPointIndex}_point`
          );
          if (groundPoint?.position) {
            groundPoint.position = new Cesium.ConstantPositionProperty(
              Cesium.Cartesian3.fromRadians(
                cartographic.longitude,
                cartographic.latitude,
                0 // 地面高度
              )
            );
          }

          // 更新航点之间的连线
          this.updatelinePositions();

          // 更新与前后点的距离标签
          if (this.selectedPointIndex > 0) {
            const prevPoint = this.activeShapePoints[this.selectedPointIndex - 1];
            const { distanceLabel } = this.createDistanceLabel(prevPoint, fixedPosition);
            if (this.distanceLabels.value[this.selectedPointIndex - 1]) {
              this.viewer.entities.remove(this.distanceLabels.value[this.selectedPointIndex - 1]);
              this.distanceLabels.value[this.selectedPointIndex - 1] = distanceLabel;
            }
          }

          if (this.selectedPointIndex < this.activeShapePoints.length - 1) {
            const nextPoint = this.activeShapePoints[this.selectedPointIndex + 1];
            const { distanceLabel } = this.createDistanceLabel(fixedPosition, nextPoint);
            if (this.distanceLabels.value[this.selectedPointIndex]) {
              this.viewer.entities.remove(this.distanceLabels.value[this.selectedPointIndex]);
              this.distanceLabels.value[this.selectedPointIndex] = distanceLabel;
            }
          }

          // 强制触发场景渲染
          this.viewer.scene.requestRender();
        }
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

    // 鼠标释放事件
    this.handler.setInputAction(() => {
      if (!this.isDrawingEnabled) return;

      if (this.isDraggingPoint && this.selectedPoint && this.selectedPointIndex !== null) {
        this.isDraggingPoint = false; // 标记结束拖拽
        this.viewer.scene.screenSpaceCameraController.enableInputs = true; // 恢复地图拖拽

        // 显示被拖拽航点两侧的标签
        if (this.selectedPointIndex > 0 && this.selectedPointIndex - 1 < this.distanceLabels.value.length) {
          this.distanceLabels.value[this.selectedPointIndex - 1].show = true;
        }
        if (this.selectedPointIndex < this.distanceLabels.value.length) {
          this.distanceLabels.value[this.selectedPointIndex].show = true;
        }

        // 获取当前点的位置
        const newPosition = this.selectedPoint.position?.getValue();
        if (!newPosition) return;

        // 重新计算总距离
        let totalDistance = 0;

        // 遍历所有距离标签，累加距离
        this.distanceLabels.value.forEach(label => {
          if (label && label.label) {
            const distanceText = label.label.text?.getValue() || "0 m";
            const distance = Number(distanceText.split('m')[0].trim());
            totalDistance += distance;
          }
        });

        // 更新总距离
        this.sumFlyDistance.value = totalDistance;

        // 更新航点的经纬度和高度信息
        const cartographic = Cesium.Cartographic.fromCartesian(newPosition);
        if (this.waypointLatLngArray.value[this.selectedPointIndex]) {
          this.waypointLatLngArray.value[this.selectedPointIndex] = {
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            altitude: cartographic.height,
          };
        }

        // 强制触发场景渲染
        this.viewer.scene.requestRender();
      }
    }, Cesium.ScreenSpaceEventType.LEFT_UP);
  }

  /**
   * 设置Alt+拖拽处理器（用于调整高度）
   */
  private setupAltDragHandlers(): void {
    if (!this.handler) return;

    // Alt+鼠标按下事件
    this.handler.setInputAction((event: any) => {
      if (!this.isDrawingEnabled) return;

      const pick = this.viewer.scene.pick(event.position);
      if (pick && pick.id && pick.id.description && pick.id.description._value === "waypoint") {
        const position = pick.id.position.getValue(Cesium.JulianDate.now());
        const cartographic = Cesium.Cartographic.fromCartesian(position);
        this.dragStartHeight = cartographic.height;
        this.dragStartY = event.position.y;
        this.isAltDrag = true;
        this.selectedPoint = pick.id;
        this.selectedPointIndex = Number(pick.id.id) - 1;

        // 标记开始拖拽
        this.viewer.scene.screenSpaceCameraController.enableInputs = false; // 禁用地图拖拽

        // 隐藏被拖拽航点两侧的标签
        if (this.selectedPointIndex > 0) {
          this.distanceLabels.value[this.selectedPointIndex - 1].show = false;
        }
        if (this.selectedPointIndex < this.distanceLabels.value.length) {
          this.distanceLabels.value[this.selectedPointIndex].show = false;
        }
      }
    }, Cesium.ScreenSpaceEventType.LEFT_DOWN, Cesium.KeyboardEventModifier.ALT);

    // Alt+鼠标移动事件
    this.handler.setInputAction((movement: any) => {
      if (!this.isDrawingEnabled) return;

      if (this.isAltDrag && this.selectedPoint && this.selectedPointIndex !== null) {
        // 计算高度变化
        const deltaY = movement.endPosition.y - this.dragStartY;
        const sensitivity = 1; // 高度变化灵敏度
        const newHeight = Math.max(this.dragStartHeight - deltaY * sensitivity, 10); // 限制最小高度为10米

        // 更新点高度
        const cartographic = Cesium.Cartographic.fromCartesian(this.activeShapePoints[this.selectedPointIndex]);

        // 生成新坐标（保持经纬度不变）
        const newPosition = Cesium.Cartesian3.fromRadians(
          cartographic.longitude,
          cartographic.latitude,
          newHeight
        );

        // 更新航点位置
        this.activeShapePoints[this.selectedPointIndex] = newPosition;
        if (this.selectedPoint) {
          this.selectedPoint.position = new Cesium.ConstantPositionProperty(newPosition);
        }

        // 更新经纬度数组中的高度
        if (this.waypointLatLngArray.value[this.selectedPointIndex]) {
          this.waypointLatLngArray.value[this.selectedPointIndex].altitude = newHeight;
        }

        // 更新高度参数数组
        this.heightArrParams.value[this.selectedPointIndex] = newHeight;

        // 更新垂直线
        const verticalLine = this.verticalLineEntities.value.find(e =>
          e.id === `vertical_${this.selectedPointIndex}_line`
        );
        if (verticalLine?.polyline) {
          verticalLine.polyline.positions = new Cesium.CallbackProperty(() => [
            newPosition,
            Cesium.Cartesian3.fromRadians(
              cartographic.longitude,
              cartographic.latitude,
              0
            )
          ], false);
        }

        // 更新高度标签
        const heightLabel = this.verticalLineEntities.value.find(e =>
          e.id === `vertical_${this.selectedPointIndex}_label`
        );
        if (heightLabel?.label) {
          (heightLabel.label as Cesium.LabelGraphics).text = new Cesium.ConstantProperty(
            `${newHeight.toFixed(0)}m`
          );
        }
        if (heightLabel) {
          heightLabel.position = new Cesium.CallbackProperty(() => {
            return Cesium.Cartesian3.fromRadians(
              cartographic.longitude,
              cartographic.latitude,
              newHeight / 2
            );
          }, false) as unknown as Cesium.PositionProperty;
        }

        // 更新航点之间的连线
        this.updatelinePositions();

        // 更新与前后点的距离标签
        if (this.selectedPointIndex > 0) {
          const prevPoint = this.activeShapePoints[this.selectedPointIndex - 1];
          const { distanceLabel } = this.createDistanceLabel(prevPoint, newPosition);
          if (this.distanceLabels.value[this.selectedPointIndex - 1]) {
            this.viewer.entities.remove(this.distanceLabels.value[this.selectedPointIndex - 1]);
            this.distanceLabels.value[this.selectedPointIndex - 1] = distanceLabel;
          }
        }

        if (this.selectedPointIndex < this.activeShapePoints.length - 1) {
          const nextPoint = this.activeShapePoints[this.selectedPointIndex + 1];
          const { distanceLabel } = this.createDistanceLabel(newPosition, nextPoint);
          if (this.distanceLabels.value[this.selectedPointIndex]) {
            this.viewer.entities.remove(this.distanceLabels.value[this.selectedPointIndex]);
            this.distanceLabels.value[this.selectedPointIndex] = distanceLabel;
          }
        }

        // 强制触发场景渲染
        this.viewer.scene.requestRender();
      }
    }, Cesium.ScreenSpaceEventType.MOUSE_MOVE, Cesium.KeyboardEventModifier.ALT);

    // Alt+鼠标释放事件
    this.handler.setInputAction(() => {
      if (!this.isAltDrag || !this.selectedPoint || this.selectedPointIndex === null) return;

      // 标记结束拖拽
      this.isAltDrag = false;

      // 恢复地图拖拽
      this.viewer.scene.screenSpaceCameraController.enableInputs = true;

      // 显示被拖拽航点两侧的标签
      if (this.selectedPointIndex > 0 && this.selectedPointIndex - 1 < this.distanceLabels.value.length) {
        this.distanceLabels.value[this.selectedPointIndex - 1].show = true;
      }
      if (this.selectedPointIndex < this.distanceLabels.value.length) {
        this.distanceLabels.value[this.selectedPointIndex].show = true;
      }

      // 获取当前点的位置
      const newPosition = this.selectedPoint.position?.getValue();
      if (!newPosition) return;

      // 重新计算总距离
      let totalDistance = 0;

      // 遍历所有距离标签，累加距离
      this.distanceLabels.value.forEach(label => {
        if (label && label.label) {
          const distanceText = label.label.text?.getValue() || "0 m";
          const distance = Number(distanceText.split('m')[0].trim());
          totalDistance += distance;
        }
      });

      // 更新总距离
      this.sumFlyDistance.value = totalDistance;

      // 更新航点的经纬度和高度信息
      const cartographic = Cesium.Cartographic.fromCartesian(newPosition);
      if (this.waypointLatLngArray.value[this.selectedPointIndex]) {
        this.waypointLatLngArray.value[this.selectedPointIndex].altitude = cartographic.height;
        this.heightArrParams.value[this.selectedPointIndex] = cartographic.height;
      }

      // 强制触发场景渲染
      this.viewer.scene.requestRender();
    }, Cesium.ScreenSpaceEventType.LEFT_UP, Cesium.KeyboardEventModifier.ALT);
  }

  /**
   * 创建航点
   * @param position 航点位置
   * @returns 创建的实体
   */
  public createPoint(position: Cesium.Cartesian3): Cesium.Entity {
    const label = this.pointCounter.value.toString();
    const point = this.viewer.entities.add({
      id: label,
      position: Cesium.Cartesian3.fromRadians(
        Cesium.Cartographic.fromCartesian(position).longitude,
        Cesium.Cartographic.fromCartesian(position).latitude,
        Cesium.Cartographic.fromCartesian(position).height,
      ),
      point: {
        heightReference: Cesium.HeightReference.NONE,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
      label: {
        text: label,
        font: 'bold 14px sans-serif',
        fillColor: Cesium.Color.WHITE,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'),
        showBackground: true,
        backgroundPadding: new Cesium.Cartesian2(2, 2),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
      billboard: {
        image: this.billImage,
        width: 35,
        height: 35,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
        scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
      description: 'waypoint',
    });

    const cartographicPosition = Cesium.Cartographic.fromCartesian(position);
    const latitude = Cesium.Math.toDegrees(cartographicPosition.latitude);
    const longitude = Cesium.Math.toDegrees(cartographicPosition.longitude);
    const altitude = cartographicPosition.height;

    this.waypointLatLngArray.value.push({ latitude, longitude, altitude });
    this.pointEntities.value.push(point);
    this.pointCounter.value++;

    return point;
  }

  /**
   * 绘制航线
   * @param positionData 位置数据
   * @returns 创建的实体
   */
  public drawShape(positionData: Cesium.CallbackProperty): Cesium.Entity {
    this.activeShape.value = this.viewer.entities.add({
      polyline: {
        positions: positionData,
        width: 5,
        material: Cesium.Color.YELLOW,
        clampToGround: false, // 3D模式下不贴地
        arcType: Cesium.ArcType.RHUMB, // 使用RHUMB类型，与原始代码保持一致
        depthFailMaterial: Cesium.Color.RED.withAlpha(0.5),
        shadows: Cesium.ShadowMode.ENABLED,
      },
      description: 'wayline',
    });

    return this.activeShape.value;
  }

  /**
   * 计算两点之间的距离
   * @param p1 点1
   * @param p2 点2
   * @returns 距离（米）
   */
  private calculateDistance(p1: Cesium.Cartesian3, p2: Cesium.Cartesian3): number {
    return Cesium.Cartesian3.distance(p1, p2);
  }

  /**
   * 创建距离标签
   * @param p1 点1
   * @param p2 点2
   * @returns 距离标签实体和距离数值
   */
  public createDistanceLabel(p1: Cesium.Cartesian3, p2: Cesium.Cartesian3): { distanceLabel: Cesium.Entity, distanceNumber: string } {
    const midpoint = Cesium.Cartesian3.add(p1, p2, new Cesium.Cartesian3());
    Cesium.Cartesian3.multiplyByScalar(midpoint, 0.5, midpoint);

    const distance = this.calculateDistance(p1, p2);
    const distanceNumber = distance.toFixed(2);
    const label = `${distanceNumber} m`;

    // 使用 CallbackProperty 动态更新标签位置
    const positionProperty = new Cesium.CallbackProperty(() => {
      const midpoint = Cesium.Cartesian3.add(p1, p2, new Cesium.Cartesian3());
      Cesium.Cartesian3.multiplyByScalar(midpoint, 0.5, midpoint);
      return midpoint;
    }, false) as unknown as Cesium.PositionProperty;

    const distanceLabel = this.viewer.entities.add({
      position: positionProperty,
      label: {
        text: label,
        font: '12px sans-serif',
        fillColor: Cesium.Color.BLACK,
        outlineColor: Cesium.Color.BLACK,
        outlineWidth: 1,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.TOP,
        eyeOffset: new Cesium.Cartesian3(0, 0, 1),
        showBackground: true,
        backgroundColor: Cesium.Color.WHITE.withAlpha(0.8),
        backgroundPadding: new Cesium.Cartesian2(2, 5),
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      },
    });

    return { distanceLabel, distanceNumber };
  }

  /**
   * 更新航线位置
   */
  public updatelinePositions(): void {
    // 遍历点数组，逐对绘制线条
    for (let i = 0; i < this.activeShapePoints.length - 1; i++) {
      const start = this.activeShapePoints[i];
      const end = this.activeShapePoints[i + 1];
      const lineId = `line_${i}`;

      // 检查是否已有该线条
      let existingLine = this.viewer.entities.getById(lineId);
      if (!existingLine) {
        // 创建新的线条
        this.viewer.entities.add({
          id: lineId,
          polyline: {
            positions: new Cesium.CallbackProperty(() => [start, end], false),
            width: 5,
            material: Cesium.Color.YELLOW,
            clampToGround: false, // 3D模式下不贴地
            arcType: Cesium.ArcType.RHUMB, // 使用RHUMB类型，与原始代码保持一致
            shadows: Cesium.ShadowMode.ENABLED,
          },
          description: 'wayline',
        });
      } else {
        // 更新现有线条
        existingLine.polyline!.positions = new Cesium.CallbackProperty(() => [start, end], false);

        // 确保clampToGround属性正确设置
        if (existingLine.polyline!.clampToGround) {
          // 如果之前是贴地的（可能是从2D模式切换过来），现在需要设置为不贴地
          existingLine.polyline!.clampToGround = new Cesium.ConstantProperty(false);
        }

        // 确保arcType属性正确设置
        if (existingLine.polyline!.arcType) {
          existingLine.polyline!.arcType = new Cesium.ConstantProperty(Cesium.ArcType.RHUMB);
        }
      }
    }

    // 移除多余的线条
    const totalLines = this.activeShapePoints.length - 1;
    let extraLineIndex = totalLines;
    while (true) {
      const extraLine = this.viewer.entities.getById(`line_${extraLineIndex}`);
      if (!extraLine) break;
      this.viewer.entities.remove(extraLine);
      extraLineIndex++;
    }

    this.viewer.scene.requestRender();
  }

  /**
   * 绘制垂直线
   * @param position 航点位置
   * @param index 航点索引
   */
  public drawVerticalLine(position: Cesium.Cartesian3, index: number): void {
    // 计算实际索引
    const actualIndex = Number(index) - 1;

    // 删除旧实体组
    this.removeVerticalLine(actualIndex);

    // 获取位置的经纬度和高度
    const cartographic = Cesium.Cartographic.fromCartesian(position);
    const centerLng = Cesium.Math.toDegrees(cartographic.longitude);
    const centerLat = Cesium.Math.toDegrees(cartographic.latitude);
    const height = cartographic.height;

    // 创建垂直线的动态位置回调
    const positionsCallback = new Cesium.CallbackProperty(() => {
      return [
        position, // 使用原始position对象以保持一致性
        Cesium.Cartesian3.fromDegrees(centerLng, centerLat, 0) // 地面高度
      ];
    }, false);

    // 创建垂直线实体
    const verticalLine = this.viewer.entities.add({
      id: `vertical_${actualIndex}_line`,
      show: true,
      polyline: {
        positions: positionsCallback,
        width: 1,
        material: Cesium.Color.WHITE,
      },
    });

    // 创建地面点实体
    const groundPoint = this.viewer.entities.add({
      id: `vertical_${actualIndex}_point`,
      position: Cesium.Cartesian3.fromDegrees(centerLng, centerLat, 0),
      show: true,
      point: {
        pixelSize: 4,
        color: Cesium.Color.WHITE,
        heightReference: Cesium.HeightReference.CLAMP_TO_GROUND,
      },
    });

    // 创建高度标签实体
    const heightLabel = this.viewer.entities.add({
      id: `vertical_${actualIndex}_label`,
      position: new Cesium.CallbackProperty(() => {
        return Cesium.Cartesian3.fromDegrees(centerLng, centerLat, height / 2);
      }, false) as unknown as Cesium.PositionProperty,
      show: true,
      label: {
        text: `${height.toFixed(0)}m`,
        font: 'bold 12px monospace',
        fillColor: Cesium.Color.WHITE,
        backgroundColor: Cesium.Color.BLACK.withAlpha(0.5),
        showBackground: true,
        horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: Cesium.VerticalOrigin.CENTER,
      },
    });

    // 将实体添加到 `verticalLineEntities` 数组
    this.verticalLineEntities.value.push(verticalLine, groundPoint, heightLabel);
  }

  /**
   * 删除垂直线
   * @param index 航点索引
   */
  public removeVerticalLine(index: number): void {
    // 查找与该索引相关的所有垂直线实体
    const verticalPrefix = `vertical_${index}_`;
    const toRemove = this.verticalLineEntities.value.filter(e =>
      e.id?.startsWith(verticalPrefix)
    );

    // 批量删除实体
    toRemove.forEach(e => this.viewer.entities.remove(e));

    // 更新数组
    this.verticalLineEntities.value = this.verticalLineEntities.value.filter(e =>
      !e.id?.startsWith(verticalPrefix)
    );
  }

  /**
   * 重新创建所有点实体以更新编号
   */
  private recreatePointEntities(): void {
    const newPointEntities: Cesium.Entity[] = [];

    // 遍历现有的实体并重新创建
    this.pointEntities.value.forEach((entity, i) => {
      const position = entity?.position?.getValue();
      const label = `${i + 1}`;

      // 删除旧的实体
      this.viewer.entities.remove(entity);

      if (position) {
        // 创建新的实体
        const newEntity = this.viewer.entities.add({
          id: `${i + 1}`,
          position: position,
          label: {
            text: label,
            font: 'bold 14px sans-serif',
            fillColor: Cesium.Color.WHITE,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            backgroundColor: Cesium.Color.fromCssColorString('#24a4fe'),
            showBackground: true,
            backgroundPadding: new Cesium.Cartesian2(2, 2),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
          },
          description: 'waypoint',
          billboard: {
            image: this.billImage,
            width: 35,
            height: 35,
            horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
            verticalOrigin: Cesium.VerticalOrigin.CENTER,
            scaleByDistance: new Cesium.NearFarScalar(1e3, 1, 2e6, 0.5),
            disableDepthTestDistance: Number.POSITIVE_INFINITY,
          },
        });

        // 将新实体添加到新的数组中
        newPointEntities.push(newEntity);

        // 重新绘制垂直线
        this.drawVerticalLine(position, i + 1);
      }
    });

    // 替换旧的实体列表
    this.pointEntities.value = newPointEntities;
  }

  /**
   * 获取当前状态的副本
   * @returns 当前状态的副本
   */
  public getState(): {
    activeShapePoints: Cesium.Cartesian3[];
    waypointLatLngArray: Api.AirLine.WayPointList[];
    heightArrParams: number[];
    sumFlyDistance: number;
  } {
    return {
      activeShapePoints: [...this.activeShapePoints],
      waypointLatLngArray: [...this.waypointLatLngArray.value],
      heightArrParams: [...this.heightArrParams.value],
      sumFlyDistance: this.sumFlyDistance.value
    };
  }

  /**
   * 设置当前状态
   * @param state 要设置的状态
   */
  public setState(state: {
    activeShapePoints: Cesium.Cartesian3[];
    waypointLatLngArray: Api.AirLine.WayPointList[];
    heightArrParams: number[];
    sumFlyDistance: number;
  }): void {
    // 清除现有实体
    this.clearAllEntities();

    // 设置新状态
    this.activeShapePoints.length = 0;
    this.activeShapePoints.push(...state.activeShapePoints);

    this.waypointLatLngArray.value = [...state.waypointLatLngArray];
    this.heightArrParams.value = [...state.heightArrParams];
    this.sumFlyDistance.value = state.sumFlyDistance;

    // 重新绘制航线
    if (this.activeShapePoints.length > 0) {
      // 重新创建点实体
      this.activeShapePoints.forEach((position, index) => {
        if (index === 0) {
          this.floatingPoint.value = this.createPoint(position);
        } else {
          this.createPoint(position);
        }

        // 绘制垂直线
        this.drawVerticalLine(position, index + 1);
      });

      // 重新创建距离标签
      for (let i = 0; i < this.activeShapePoints.length - 1; i++) {
        const start = this.activeShapePoints[i];
        const end = this.activeShapePoints[i + 1];
        const { distanceLabel } = this.createDistanceLabel(start, end);
        this.distanceLabels.value.push(distanceLabel);
      }

      // 重新绘制航线
      const dynamicPositions = new Cesium.CallbackProperty(() => {
        return this.activeShapePoints;
      }, false);

      // 先移除现有的航线实体
      if (this.activeShape.value) {
        this.viewer.entities.remove(this.activeShape.value);
      }

      // 重新创建航线实体，确保使用正确的属性
      this.activeShape.value = this.viewer.entities.add({
        polyline: {
          positions: dynamicPositions,
          width: 5,
          material: Cesium.Color.YELLOW,
          clampToGround: false, // 3D模式下不贴地
          arcType: Cesium.ArcType.RHUMB, // 使用RHUMB类型，与原始代码保持一致
          depthFailMaterial: Cesium.Color.RED.withAlpha(0.5),
          shadows: Cesium.ShadowMode.ENABLED,
        },
        description: 'wayline',
      });
    }
  }

  /**
   * 清除所有实体
   */
  public clearAllEntities(): void {
    // 清除之前绘制的点
    this.pointEntities.value.forEach((entity) => {
      this.viewer.entities.remove(entity);
    });
    this.pointEntities.value = [];

    // 清除之前绘制的线
    if (this.activeShape.value) {
      this.viewer.entities.remove(this.activeShape.value);
      this.activeShape.value = null;
    }

    // 清除距离标签
    this.distanceLabels.value.forEach((label) => {
      this.viewer.entities.remove(label);
    });
    this.distanceLabels.value = [];

    // 清除垂直线实体
    this.verticalLineEntities.value.forEach((entity) => {
      this.viewer.entities.remove(entity);
    });
    this.verticalLineEntities.value = [];

    // 重置其他状态
    this.activeShapePoints.length = 0;
    this.waypointLatLngArray.value = [];
    this.heightArrParams.value = [];
    this.sumFlyDistance.value = 0;
    this.pointCounter.value = 1;
  }

  /**
   * 销毁处理器
   */
  public destroy(): void {
    if (this.handler) {
      this.handler.destroy();
      this.handler = null;
    }
    this.clearAllEntities();
  }
}
