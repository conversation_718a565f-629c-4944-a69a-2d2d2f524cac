import { Ray } from '@/typings/Cesium';
import * as Cesium from 'cesium';


// 地理位置对象
interface Position {
  coords: {
    longitude: number;
    latitude: number;
  };
}

// Cesium的token
Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJhMGYxODk1ZS0yYWViLTQxN2EtODNhZC04N2FlNDhhNTUyMGIiLCJpZCI6MjI1MTE1LCJpYXQiOjE3MTk1NDI4NjB9.ZXZQ-Wgn_gzRLvSU0ZN06vG5VLcpUDdPNYwCShFeAjo';
// Cesium.Ion.defaultAccessToken = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJqdGkiOiJiNDk1N2RlMC0wNzJlLTQ1MmItYjQ3NS1kN2FmNjA4MjAyZmIiLCJpZCI6MjUxODc0LCJpYXQiOjE3MzAzMzU0NTh9.CstB9AT8zrgGDs2jcgnyEaGKM5C74qsvlFTCNwE6gHs';

// 获取用户的地理位置信息
// const position: Position = await new Promise((resolve, reject) => {
//   navigator.geolocation.getCurrentPosition(resolve, reject);
// });

// 获取用户的经纬度
// const longitude = position.coords.longitude;
// const latitude = position.coords.latitude;

// 设置相机位置到用户当前位置附近
// const rectangle = Cesium.Rectangle.fromDegrees(longitude, latitude, longitude, latitude);

// 中国经纬度信息
const position: Position = { coords: { longitude: 113.071169, latitude: 28.230261 } }
// 定位到中国
const rectangle = Cesium.Cartesian3.fromDegrees(113.071169, 28.230261, 10000000.0);;


// const imagerySources = new Cesium.ImageMaterialProperty();
// const selectedIndex = imagerySources.length - 1;


// -----------------天地图设置-----------------
// var token = '185d781becb567e2c31186b00a26c6b4';
// var token = '0f91800d4d36946dcc0b07142466075a';
// var token = '3a798e034f12e37aef2884c0505e7971';
var token = '2ebde960216378f6c28d8da79c67304a'; // Business
// 服务域名
var tdtUrl = 'https://t{s}.tianditu.gov.cn/';
// 服务负载子域
var subdomains = ['0', '1', '2', '3', '4', '5', '6', '7'];
// 卫星影像图层
const tiandituJosiahMapUrl = tdtUrl + 'DataServer?T=img_w&x={x}&y={y}&l={z}&tk=' + token;
// const tiandituJosiahMapUrl = tdtUrl + 'DataServer?T=vec_c&x={x}&y={y}&l={z}&tk=' + token;
// 天地图道路注记图层
const tiandituLabelMapUrl = tdtUrl + 'DataServer?T=cia_w&x={x}&y={y}&l={z}&tk=' + token;
// 'https://t6.tianditu.gov.cn/DataServer?T=cia_w&X=26682&Y=13708&L=15&tk=e3788fe8026e62adde40cad134a3d993'


// -----------------高德地图设置-----------------
// 高德地图卫星影像  style 6-影像 7-矢量 8-标注
const gaodeJosiahMapUrl = 'http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=6';
// 高德地图道路注记地图
const gaodeLabelMapUrl = 'http://webst02.is.autonavi.com/appmaptile?x={x}&y={y}&z={z}&lang=zh_cn&size=1&scale=1&style=8'



// Cesium.Camera.DEFAULT_VIEW_RECTANGLE = rectangle;

// -----------------图层设置-----------------
// 卫星影像图层
const josiahProvider = new Cesium.UrlTemplateImageryProvider({
  url: tiandituJosiahMapUrl,
  subdomains: subdomains,
  maximumLevel: 18
});

// 道路图层
const labelProvider = new Cesium.UrlTemplateImageryProvider({
  url: tiandituLabelMapUrl,
  subdomains: subdomains,
});

// Cesium配置对象，用于定制Cesium Viewer
const cesiumConfig = {
  // 禁用信息框
  infoBox: false,
  // 左下角显示视图的播放数度
  animation: false,
  // 禁用基础图层选择器
  baseLayerPicker: false,
  // 禁用全屏按钮
  fullscreenButton: false,
  // 禁用地理编码服务
  geocoder: false,
  // 禁用home按钮
  homeButton: false,
  // 禁用场景模式选择器
  sceneModePicker: false,
  // 禁用选择指示器
  selectionIndicator: false,
  // 禁用时间线
  timeline: false,
  // 禁用导航帮助按钮
  navigationHelpButton: false,
  terrainProvider: undefined,
  // 初始时不显示导航指示
  navigationInstructionsInitiallyVisible: false,
  // 不显示渲染循环中的错误
  showRenderLoopErrors: false,
  // 禁用顺序独立的半透明功能
  orderIndependentTranslucency: false,
  // 启用动画效果
  shouldAnimate: true,
  // 设置场景模式为2D和3D切换模式
  sceneMode: Cesium.SceneMode.SCENE2D,
  usePreCachedTilesIfAvailable: false,
  // imageryProvider: josiahProvider,
  // selectedImageryProviderViewModel: imagerySources[selectedIndex]
  // 自定义地图源
  // imageryProvider: new Cesium.WebMapTileServiceImageryProvider({
  //   url: tiandituJosiahMapUrl,
  //   subdomains: ["0", "1", "2", "3", "4", "5", "6", "7"], //服务负载子域
  //   layer: "tdtImgLayer",
  //   style: "default",
  //   format: "image/jpeg",
  //   tileMatrixSetID: "GoogleMapsCompatible", //使用谷歌的瓦片切片方式
  //   // show: true,
  // })
};

/**
 * 通过鼠标位置计算用户当前经纬度
 * @param MounthEvent
 * @param viewer
 */
class MouthPositionHandler {
  private _position: { longitude: number; latitude: number };

  constructor() {
    this._position = { longitude: 0, latitude: 0 };
  }

  get position() {
    return this._position;
  }

  setPosition(position: { longitude: number; latitude: number }) {
    this._position = position;
  }

  // throttle 函数包裹的方法
  public countMouthPosition = throttle((movement: { position: Cesium.Cartesian2 }, viewer: Cesium.Viewer) => {
    // 获取鼠标在屏幕上的位置
    const screenPosition = movement.position;
    let position = { longitude: 0, latitude: 0 };

    // 将屏幕位置转换为经纬度
    const cartesian = viewer?.scene.globe.pick(
      (viewer.camera.getPickRay(screenPosition) as Cesium.Ray),
      viewer.scene
    );
    if (cartesian) {
      const cartographic = viewer?.scene.globe.ellipsoid.cartesianToCartographic(cartesian);
      position.longitude = Cesium.Math.toDegrees(cartographic.longitude);
      position.latitude = Cesium.Math.toDegrees(cartographic.latitude);
    }

    // 将 position 的值赋给内部属性
    this.setPosition(position);

    //
    return position;
  }, 200);

}

// MouthPositionHandler的节流函数
type ThrottleOrDebounceFunction<T extends (...args: any[]) => any> = T & {
  cancel?: () => void;
  flush?: () => void;
};
function throttle<T extends (...args: any[]) => any>(
  func: T,
  wait: number,
): ThrottleOrDebounceFunction<T> {
  let timeout: ReturnType<typeof setTimeout> | null = null;
  let previous = 0;

  const throttled = function (this: any, ...args: Parameters<T>): void {
    const context = this;
    const now = Date.now();

    if (now - previous > wait) {
      if (timeout) {
        clearTimeout(timeout);
        timeout = null;
      }
      func.apply(context, args);
      previous = now;
    } else if (!timeout) {
      timeout = setTimeout(() => {
        func.apply(context, args);
        previous = now;
      }, wait - (now - previous));
    }
  };

  throttled.cancel = function (): void {
    if (timeout) {
      clearTimeout(timeout);
      timeout = null;
    }
  };

  return throttled as ThrottleOrDebounceFunction<T>;
}



export {
  cesiumConfig,
  josiahProvider,
  labelProvider,
  position,
  // longitude,
  // latitude,
  rectangle,
  MouthPositionHandler
};
