<script setup lang="ts">
import { h, onMounted, ref } from 'vue';
import { NTag, NText } from 'naive-ui';
import { fetchDroneBatteryList } from '@/service/api';
import DataBatteryNumber from './modules/data-battery-number.vue';
import DataBatteryProportion from './modules/data-battery-proportion.vue';

const onlineTableLoading = ref(false);
const onlineTableData = ref([]);
const onlineColumns = [
  {
    title: '无人机',
    key: 'deviceName'
  },
  {
    title: '无人机名称',
    key: 'droneName'
  },
  {
    title: '电池序列号',
    key: 'batterySn'
  },
  {
    title: '电池型号',
    key: 'batteryModel',
    render(row: TableItem) {
      return h(NText, {}, { default: () => (row.batteryModel ? row.batteryModel : 'Li-ion 4S') });
    }
  },
  {
    title: '当前电量',
    key: 'capacityPercent',
    render(row: TableItem) {
      return row.capacityPercent + '%';
    }
  },
  {
    title: '循环次数',
    key: 'loopTimes',
    render(row: TableItem) {
      const { loopTimes } = row;
      const defaultLoopTimes = loopTimes || 0;
      let typeClass;
      if (defaultLoopTimes < 300) {
        typeClass = 'success';
      } else if (defaultLoopTimes < 400) {
        typeClass = 'warning';
      } else {
        typeClass = 'error';
      }
      return h(NTag, { type: typeClass as Api.DictData.tagType }, { default: () => defaultLoopTimes });
    }
  }
];

interface TableItem {
  deviceName: string;
  droneName: string;
  batterySn: string;
  batteryModel: string;
  capacityPercent: number;
  loopTimes: number;
}

const warningTableLoading = ref(false);
const warningTableData = ref([]);
const warningColumns = [
  {
    title: '电池序列号',
    key: 'batterySn'
  },
  {
    title: '型号',
    key: 'batteryModel'
  },
  {
    title: '循环次数',
    key: 'loopTimes'
  }
];

const listTableLoading = ref(false);
const listTableData = ref([]);
const listColumns = [
  {
    title: '电池序列号',
    key: 'batterySn'
  },
  {
    title: '电池型号',
    key: 'batteryModel'
  },
  {
    title: '最近搭载无人机',
    key: 'droneName'
  },
  {
    title: '出厂容量(mAh)',
    key: 'factoryCapacity',
    render(row: TableItem) {
      return h(NText, {}, { default: () => (row.loopTimes !== 0 ? 7811 : 0) });
    }
  },
  {
    title: '当前容量(mAh)',
    key: 'currentCapacity'
  },
  {
    title: '最大循环数',
    key: 'maxLoopTimes',
    render(row: TableItem) {
      return h(NText, {}, { default: () => (row.loopTimes !== 0 ? 400 : 0) });
    }
  },
  {
    title: '当前循环数',
    key: 'loopTimes'
  },
  {
    title: '状态',
    key: 'status',
    render(row: TableItem) {
      const { loopTimes } = row;
      const defaultLoopTimes = loopTimes || 0;
      let typeClass;
      let defaultValue;
      if (defaultLoopTimes < 300) {
        typeClass = 'success';
        defaultValue = '健康';
      } else if (defaultLoopTimes < 400) {
        typeClass = 'warning';
        defaultValue = '告警';
      } else {
        typeClass = 'error';
        defaultValue = '待报废';
      }
      return h(NTag, { type: typeClass as Api.DictData.tagType }, { default: () => defaultValue });
    }
  }
];

function warningClass(row: TableItem) {
  return row.loopTimes < 300 ? 'too-success' : row.loopTimes > 400 ? 'too-error' : 'too-warning';
}

function renderCell(value: string | number) {
  if (!value) {
    return h(NText, { depth: 3 }, { default: () => '---' });
  }
  return value;
}

async function getOnlineBattery() {
  const { error, data } = await fetchDroneBatteryList(1);
  if (!error) {
    onlineTableLoading.value = false;
    onlineTableData.value = data.rows;
  }
}

async function getListBattery() {
  const { error, data } = await fetchDroneBatteryList(0);
  if (!error) {
    listTableLoading.value = false;
    listTableData.value = data.rows;

    warningTableLoading.value = false;
    warningTableData.value = data.rows;
  }
}

onMounted(() => {
  getOnlineBattery();
  getListBattery();
});
</script>

<template>
  <div>
    <NGrid class="mt-10px mx-10px" :x-gap="12" :y-gap="12" :cols="4" layout-shift-disabled>
      <NGi :span="3">
        <div>
          <NCard title="在线电池" :bordered="false" class="h-400px">
            <NScrollbar class="max-h-350px">
              <NDataTable
                ref="onlineTable"
                remote
                :columns="onlineColumns"
                :data="onlineTableData"
                :loading="onlineTableLoading"
                :render-cell="renderCell"
              />
            </NScrollbar>
          </NCard>
        </div>
        <NGrid class="mt-10px" :x-gap="12" :y-gap="12" :cols="2" layout-shift-disabled>
          <NGi>
            <NCard title="当前系统内各状态电池比例" size="small" class="h-300px">
              <DataBatteryProportion />
            </NCard>
          </NGi>
          <NGi>
            <NCard title="当前系统内各状态电池数量" size="small" class="h-300px">
              <DataBatteryNumber />
            </NCard>
          </NGi>
        </NGrid>
      </NGi>
      <NGi>
        <div>
          <NCard title="电池告警" :bordered="false" class="h-710px">
            <NScrollbar >
              <NDataTable
                ref="warningTable"
                remote
                :columns="warningColumns"
                :data="warningTableData"
                :loading="warningTableLoading"
                :render-cell="renderCell"
                :row-class-name="warningClass"
              />
            </NScrollbar>
          </NCard>
        </div>
      </NGi>
    </NGrid>
    <NGrid class="mt-10px mx-10px" :x-gap="12" :y-gap="12" :cols="2" layout-shift-disabled>
      <NGi :span="2">
        <div>
          <NCard title="电池列表" :bordered="false">
            <NScrollbar class="max-h-300px">
              <NDataTable
                ref="listTable"
                remote
                :columns="listColumns"
                :data="listTableData"
                :loading="listTableLoading"
                :render-cell="renderCell"
              />
            </NScrollbar>
          </NCard>
        </div>
      </NGi>
    </NGrid>
  </div>
</template>

<style scoped>
:deep(.too-error td) {
  color: rgba(255, 0, 0, 0.75) !important;
}
:deep(.too-warning td) {
  color: orange;
}
:deep(.too-success td) {
  color: green;
}
</style>
