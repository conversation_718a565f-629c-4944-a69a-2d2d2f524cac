<template>
  <div class="container">
    <div class="search-row">
      <div class="label" @click="changeSearchStatus(!showSearch)">
        <n-icon color="white">
          <Search />
        </n-icon>
      </div>
      <div :style="{ position: 'absolute', ...(objSpace.direction === 'right' ? { right: '40px' } : { left: '40px' })}">
        <n-auto-complete class="search-box" v-if="showSearch" v-model:value="searchKeyword" placeholder="输入地名搜索"
          :options="searchOptions" :input-props="{ style: { color: '#333' } }" @update:value="debouncedHandleInput"
          @select="handleSearch" :render-option="renderOption" :menu-props="{ style: { background: 'white' } }">
          <template #prefix>
            <n-icon :component="Search" />
          </template>
          <template #suffix>
            <n-icon v-if="searchKeyword" @click="changeSearchStatus(true)" class="close-icon" color="#333"
              :component="CloseCircleSharp">
            </n-icon>
          </template>
        </n-auto-complete>
      </div>
    </div>
    <div class="empty-row" @click="drawDistance">
      <n-popover trigger="hover" placement="right">
        <template #trigger>
          <n-icon color="white">
            <Locate />
          </n-icon>
        </template>
        <span>距离测量</span>
      </n-popover>
    </div>
    <div class="empty-row" @click="drawArea">
      <n-popover trigger="hover" placement="right">
        <template #trigger>
          <n-icon color="white">
            <Crop />
          </n-icon>
        </template>
        <span>面积测量</span>
      </n-popover>
    </div>
    <div class="empty-row" @click="clearData('initiative')">
      <n-popover trigger="hover" placement="right">
        <template #trigger>
          <n-icon color="white">
            <CloseSharp />
          </n-icon>
        </template>
        <span>清除标记</span>
      </n-popover>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { onMounted, ref, onUnmounted, reactive, PropType, toRefs, toRaw, watch, h, VNode } from 'vue';
import { Locate, Search, Crop, CloseSharp, CloseCircleSharp } from '@vicons/ionicons5';
import { NIcon, NAutoComplete, NPopover } from 'naive-ui';
import { fetchTianDiMapSearch } from '@/service/api';
import CesiumMeasures from './cesium-measure-draw.js';
import DynamicPolylineDrawer from './cesium-polyline-drawer';
import dingweiImage from "@/assets/imgs/dingwei.png";
import * as Cesium from 'cesium';
import { position } from '@/config/cesium_config.js';
interface Position {
  left?: number | undefined;
  top?: number | undefined;
  right?: number | undefined;
  bottom?: number | undefined;
}
interface spaceInfo {
  width: number;
  place: Position;
  direction: string;
}
const props = defineProps({
  viewer: {
    type: Cesium.Viewer,
    required: true
  },
  objSpace: {
    type: Object as PropType<spaceInfo>, // 关键类型约束
    required: true,
    default: () => ({ width: 20, place: { left: "", top: "", right: "", bottom: "" }, direction: "" }) // 设置默认值
  }
});
// 父组件需要切换状态状态时调用
const emit = defineEmits(['changeStatus']);
const emitChangeStatus = (status: boolean) => {
  emit('changeStatus', status);
};
// 搜索关键词
const searchKeyword = ref<string>('');
// 搜索选项
const searchOptions: { label: string; value: string; disabled?: boolean }[] = reactive([]);
// 是否显示搜索框
const showSearch = ref<boolean>(false);

// 获取位置信息
const { objSpace } = toRefs(props);
const { place, width } = objSpace.value
// 声明测量对象
let measure: CesiumMeasures;
// 监听 viewer 初始化完成
let viewer: Cesium.Viewer;
let handler: Cesium.ScreenSpaceEventHandler | null = null;
const mapBound = ref<string>('110.57117,26.23026,115.57117,30.23026');
const childMethod = (viewerdata: Cesium.Viewer) => {
  viewer = viewerdata;
  if (viewer) {
    let lastUpdate = 0;
    viewer.scene.preRender.addEventListener(function () {
      const now = Date.now();
      if (now - lastUpdate < 1000 || !showSearch.value) return; // 至少间隔1秒重新获取当前位置且显示搜索框才获取
      lastUpdate = now;
      // 当相机位置或视角发生变化时触发
      const cartographic = viewer.camera.positionCartographic; // Cartographic对象
      const longitude = Cesium.Math.toDegrees(cartographic.longitude); // 经度
      const latitude = Cesium.Math.toDegrees(cartographic.latitude); // 纬度
      const lngAdd = (longitude + 2.5).toFixed(5);
      const latAdd = (latitude + 2).toFixed(5);
      const lngSub = (longitude - 2.5).toFixed(5);
      const latSub = (latitude - 2).toFixed(5);
      mapBound.value = `${lngSub},${latSub},${lngAdd},${latAdd}`;
    });
    handler = new Cesium.ScreenSpaceEventHandler(viewer!.canvas);
    // 鼠标右键点击事件处理
    handler.setInputAction((event: any) => {
      emitChangeStatus(true);
    }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);
  }
};
defineExpose({ childMethod });
onMounted(() => {
  viewer = props.viewer;
});
// 添加防抖 不确定传递的函数参数类型所以用any
const debounce = <T extends (...args: any[]) => any>(fn: T, delay: number) => {
  let timer: ReturnType<typeof setTimeout> | null = null;
  return function (this: ThisParameterType<T>, ...args: Parameters<T>) {
    if (timer) clearTimeout(timer);
    timer = setTimeout(() => fn.apply(this, args), delay);
  };
};
// 搜索输入
const handleInput = async (keyWord: string) => {
  const data = { keyWord, mapBound: mapBound.value };
  if (keyWord) {
    try {
      const res = await fetchTianDiMapSearch(data);
      const resultType = res.data?.resultType;
      if (resultType === 1) {
        const pois = res.data?.pois || [];
        if (pois.length === 0) {
          searchOptions.splice(0, searchOptions.length, { label: '未找到结果', value: '', disabled: true });
        }
        const options: { label: string; value: string }[] = pois.map((item: { name: string; lonlat: string; address: string, index: number }, index: number) => ({
          label: item.name,
          value: item.lonlat,
          disabled: false,
          address: item.address,
          index: index
        }));
        if (pois.length > 0) {
          searchOptions.splice(0, searchOptions.length, ...options);
          console.log('searchOptions', searchOptions)
        }
      } else if (resultType === 3) {
        const area = res.data?.area || {};
        const options: { label: string; value: string }[] = [{ label: area.name, value: area.lonlat }];
        searchOptions.splice(0, searchOptions.length, ...options);
      } else {
        searchOptions.splice(0, searchOptions.length, { label: '未找到结果', value: '', disabled: true });
      }
      // 根据value进行去重
      const optionsValue = searchOptions.map(item => item.value);
      const uniqueoptions: { label: string; value: string }[] = optionsValue.filter((item, index) => optionsValue.indexOf(item) === index).map(value => searchOptions.find(item => item.value === value)!);
      searchOptions.splice(0, searchOptions.length, ...uniqueoptions);
    } catch (err) {
      console.log('err', err);
    }
  }
};
let connector: DynamicPolylineDrawer;
// 使用防抖包装请求函数 (延迟500ms)
const debouncedHandleInput = debounce(handleInput, 500);
const markerEntities: Cesium.Entity[] = [];
// 搜索
const handleSearch = (value: string) => {
  const [longitudeStr, latitudeStr] = value.split(',') || [];
  const longitude = Number.parseFloat(longitudeStr) || 0;
  const latitude = Number.parseFloat(latitudeStr) || 0;
  if (!connector) {
    connector = new DynamicPolylineDrawer(viewer);
  }
  const isDuplicate: boolean = connector.addPoint(longitude, latitude);
  if (isDuplicate) return; // 防止重复添加
  // 定位到搜索结果
  viewer.camera.flyTo({
    destination: Cesium.Cartesian3.fromDegrees(longitude * 1 || 0, latitude || 0, 10000),
    orientation: {
      heading: 0,
      pitch: -Cesium.Math.PI_OVER_THREE, // 调整俯角
      roll: 0
    }
  });
  const entity = viewer.entities.add({
    position: Cesium.Cartesian3.fromDegrees(longitude, latitude, 10000),
    billboard: {
      image: dingweiImage,
      width: 36,
      height: 36,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      heightReference: Cesium.HeightReference.NONE,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    }
  });
  markerEntities.push(entity); // 存储引用
  connector.zoomToPoints();
};
// 测量面积
const drawArea = () => {
  // clearData('passivity');
  emitChangeStatus(false);
  if (!measure) {
    measure = new CesiumMeasures(viewer);
  }
  measure.drawAreaMeasureGraphics({
    clampToGround: false,
    measure: true,
    style: {
      line: {
        width: 2,
        material: Cesium.Color.RED.withAlpha(0.8)
      },
      point: {
        pixelSize: 5,
        outlineColor: Cesium.Color.BLUE,
        outlineWidth: 2,
        show: true // 默认为true
      },
      polygon: {
        material: Cesium.Color.GREEN.withAlpha(0.1)
      },
      // 如果不设置centerPoint则会把测量的位置现在在最后一个点击的位置
      centerPoint: {
        pixelSize: 5,
        outlineColor: Cesium.Color.RED,
        outlineWidth: 2
      }
    }
  });
};
// 测量距离
const drawDistance = () => {
  // clearData('passivity');
  emitChangeStatus(false);
  if (!measure) {
    measure = new CesiumMeasures(viewer);
  }
  measure.drawLineMeasureGraphics({
    clampToGround: false,
    measure: true,
    style: {
      line: {
        width: 2,
        material: Cesium.Color.BLUE.withAlpha(0.8)
      },
      point: {
        color: Cesium.Color.RED,
        pixelSize: 5,
        outlineColor: Cesium.Color.GREEN,
        outlineWidth: 3
      }
    }
  });
};
// 自定义搜索选项
const renderOption = ({ node, option }: { node: VNode, option: { label: string; address: string, index: number, value: string } }) => {
  const color = ref<string>('#F54336');
  return h('div', {
    ...node.props,
    style: {
      display: 'flex',
      alignItems: 'center',
      gap: '8px',
      cursor: 'pointer',
      transition: 'background-color 0.3s',
      padding: '6px',
      borderRadius: '4px',
      width: '250px'
    },
    onMouseenter: (e: Event) => {
      (e.currentTarget as HTMLElement).style.backgroundColor = '#eee';
    },
    onMouseleave: (e: Event) => {
      (e.currentTarget as HTMLElement).style.backgroundColor = 'transparent';
    },
  }, [
    h('div', {
      style: {
        width: '20px',
        height: '20px',
        lineHeight: '20px',
        borderRadius: '50%',
        background: color.value,
        color: '#fff',
        textAlign: 'center',
        transition: 'all 0.3s',
        border: '1px solid transparent'
      },
    }, option.index !== undefined ? option.index + 1 : ''),
    h('div', {
      style: {
        display: 'flex',
        flexDirection: 'column'
      }
    }, [
      h('span', {
        style: {
          color: '#1890ff',
          fontSize: '12px',
          lineHeight: '1.5'
        }
      }, option.label),
      h('span', {
        style: {
          color: '#000',
          width: '200px',
          fontSize: '10px',
          lineHeight: '1.5',
          overflow: 'hidden',
          whiteSpace: 'nowrap',
          textOverflow: 'ellipsis'
        }
      }, option.address)
    ])
  ])
};
// 清除数据
const clearData = (type: string) => {
  emitChangeStatus(true); // 显示状态栏
  measure && measure.clearAll(); // 清除测量数据
  changeSearchStatus(false); // 隐藏搜索框
  if (type === 'initiative') { // 主动清除
    markerEntities.forEach(entity => {
      viewer.entities.remove(entity);
    });
    markerEntities.length = 0;
    if (!connector) connector = new DynamicPolylineDrawer(viewer);
    connector.clear(); // 清除绘制线
  }
};
// 切换搜索框状态
const changeSearchStatus = (status: boolean) => {
  showSearch.value = status;
  searchKeyword.value = '';
  searchOptions.splice(0, searchOptions.length);
};
// 清理工作
onUnmounted(() => {
  if(measure) {
    measure.clearAll();
    measure = null;
  }
 });
</script>

<style scoped lang="scss">
:deep(.n-input .n-input__placeholder) {
  color: #333;
  opacity: 1;
}

:deep(.n-input .n-input__prefix .n-icon) {
  color: #333;
}

:deep(.n-input .n-input__clear) {
  color: #333;
}

:deep(.n-input .n-input__clear .n-icon) {
  color: #333;
}

.close-icon {
  cursor: pointer;
}

.container {
  width: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.search-row {
  display: flex;
  align-items: center;
  gap: 8px;
  height: 30px;
  border-radius: 50%;
  background-color: rgb(72, 72, 78);
  justify-content: center;
  position: reactive;
  cursor: pointer;
}

.search-box {
  background: rgba(255, 255, 255, 0.95) !important;
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  width: 250px;
  height: 30px;
  z-index: 9999;
}

.empty-row {
  height: 30px;
  border-radius: 50%;
  background-color: rgb(72, 72, 78);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
</style>
