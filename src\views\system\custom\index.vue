<script setup lang="ts">
import { computed, onMounted, reactive, ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { updateGetSysInfo } from '@/service/api';
import { useMessage } from 'naive-ui'
import type { UploadFileInfo } from 'naive-ui'
import AImodules from '../../senior/config/index.vue'


const appStore = useAppStore();
const authStore = useAuthStore();
const { sysInfo, token } = authStore;
const serviceBaseURL = import.meta.env.VITE_SERVICE_BASE_URL;
const message = useMessage()

async function handleSaveClick() {
  const data: Api.Auth.SysInfo = { webConfig: { webName: '', webLogoName: '', webLogoUrl: '', webLogo: '' }, dockConfig: {} };
  Object.assign(data, sysInfo);
  data.webConfig.webLogo = tableVal.webLogoName;
  data.webConfig.webName = tableVal.webName;
  console.log(data);
  const { error } = await updateGetSysInfo(data);
  if (!error) {
    message.success('修改成功！');
    authStore.sysInfo.webConfig.webLogoUrl = tableVal.webLogoUrl;
  } else {
    message.error('修改失败！');
  }
};

const tableVal = reactive({
  webName: '',
  webLogoName: '',
  webLogoUrl: ''
})
function beforeUpload(data: { file: UploadFileInfo, fileList: UploadFileInfo[] }) {
  // const message = useMessage();
  if (data.file.file?.type === 'image/png' || data.file.file?.type === 'image/jpeg') {
    return true
  } else {
    message.error('只能上传png或jpg格式的图片文件，请重新上传')
    return false
  }
}

// 上传成功
function handleFinish({ file, event }: { file: UploadFileInfo, event?: ProgressEvent }) {
  console.log({ file, event })
  if (event && event.target) {
    try {
      // 确保 event.target 是 XMLHttpRequest 类型
      const xhr = event.target as XMLHttpRequest;
      let target = JSON.parse(xhr.response);
      console.log("handleFinish ~ target:", target)
      tableVal.webLogoName = target.data.newFileName;
      tableVal.webLogoUrl = target.data.fileName;
      message.success('上传成功！');
    } catch (error) {
      console.error('解析 ProgressEvent JSON 失败:', error);
    }
  } else {
    console.warn('event 或 event.target 不存在');
  }

  return file
}

onMounted(() => {
  console.log("onMounted ~ sysInfo.webConfig:", sysInfo.webConfig)
  tableVal.webLogoName = sysInfo.webConfig.webLogoName;
  tableVal.webName = sysInfo.webConfig.webName;
  tableVal.webLogoUrl = sysInfo.webConfig.webLogoUrl;
})

</script>

<template>
  <div>
    <NCard title="基础信息" :bordered="false">
      <!-- <n-divider /> -->
      <n-form ref="formRef" :model="tableVal" label-placement="left" label-width="auto"
        require-mark-placement="right-hanging" class="mt-5 w-45vw">

        <n-form-item label="图标更换" path="tableVal.webLogoUrl">
          <n-thing content-indented>
            <template #avatar>
              <n-avatar :size="100" class="border-(1px solid #e0e0e6)" :src="tableVal.webLogoUrl">
              </n-avatar>
            </template>
            <template #header>
              <n-upload :action="serviceBaseURL + '/common/upload'" :headers="{ Authorization: token }" :max="1"
                @finish="handleFinish" @before-upload="beforeUpload" :show-file-list="false">
                <n-button>更换图标</n-button>
              </n-upload>
            </template>
            支持图片类型：png、jpg大小不超过2MB
          </n-thing>
        </n-form-item>

        <n-form-item label="系统名称" path="tableVal.webName">
          <n-input v-model:value="tableVal.webName" placeholder="输入系统名称" :maxlength="30" show-count />
        </n-form-item>
        <n-form-item>
          <n-button attr-type="button" type="primary" @click="handleSaveClick">
            保存
          </n-button>
        </n-form-item>
      </n-form>
    </NCard>
    <AImodules />
  </div>

</template>

<style scoped></style>
