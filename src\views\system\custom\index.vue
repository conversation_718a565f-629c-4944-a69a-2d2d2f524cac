<script setup lang="ts">
import { computed, onMounted, reactive, ref, onUnmounted } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { useAuthStore } from '@/store/modules/auth';
import { updateGetSysInfo } from '@/service/api';
import { useMessage } from 'naive-ui';
import type { UploadFileInfo } from 'naive-ui';
import AImodules from '../../senior/config/index.vue';
import dingweiImage from "@/assets/imgs/dingweiblue.png";
import { cesiumConfig, josiahProvider, labelProvider, position, } from '@/config/cesium_config';
import * as Cesium from 'cesium';

const appStore = useAppStore();
const authStore = useAuthStore();
const { sysInfo, token } = authStore;
const serviceBaseURL = import.meta.env.VITE_SERVICE_BASE_URL;
const message = useMessage()
let viewer: Cesium.Viewer;
async function handleSaveClick() {
  const data: Api.Auth.SysInfo = { webConfig: { webName: '', webLogoName: '', webLogoUrl: '', webLogo: '', centerLongitude: '', centerLatitude: '' }, dockConfig: {} };
  Object.assign(data, sysInfo);
  data.webConfig.webLogo = tableVal.webLogoName;
  console.log(" handleSaveClick ~ tableVal:", tableVal)
  data.webConfig.webName = tableVal.webName;
  data.webConfig.centerLatitude = tableVal.centerLatitude !== null ? tableVal.centerLatitude.toString() : '';
  data.webConfig.centerLongitude = tableVal.centerLongitude !== null ? tableVal.centerLongitude.toString() : '';
  const { error } = await updateGetSysInfo(data);
  if (!error) {
    message.success('修改成功！');
    authStore.sysInfo.webConfig.webLogoUrl = tableVal.webLogoUrl;
  } else {
    message.error('修改失败！');
  }
};

interface TableVal {
  webName: string;
  webLogoName: string;
  webLogoUrl: string;
  centerLatitude: number | null;
  centerLongitude: number | null;
}

const tableVal = reactive<TableVal>({
  webName: '',
  webLogoName: '',
  webLogoUrl: '',
  centerLatitude: null,
  centerLongitude: null,
});
function beforeUpload(data: { file: UploadFileInfo, fileList: UploadFileInfo[] }) {
  // const message = useMessage();
  if (data.file.file?.type === 'image/png' || data.file.file?.type === 'image/jpeg') {
    return true
  } else {
    message.error('只能上传png或jpg格式的图片文件，请重新上传')
    return false
  }
}

// 上传成功
function handleFinish({ file, event }: { file: UploadFileInfo, event?: ProgressEvent }) {
  if (event && event.target) {
    try {
      // 确保 event.target 是 XMLHttpRequest 类型
      const xhr = event.target as XMLHttpRequest;
      let target = JSON.parse(xhr.response);
      tableVal.webLogoName = target.data.newFileName;
      tableVal.webLogoUrl = target.data.fileName;
      message.success('上传成功！');
    } catch (error) {
      console.error('解析 ProgressEvent JSON 失败:', error);
    }
  } else {
    console.warn('event 或 event.target 不存在');
  }

  return file
}

onMounted(() => {
  tableVal.webLogoName = sysInfo.webConfig.webLogoName;
  tableVal.webName = sysInfo.webConfig.webName;
  tableVal.webLogoUrl = sysInfo.webConfig.webLogoUrl;
  tableVal.centerLatitude = sysInfo.webConfig.centerLatitude ? parseFloat(sysInfo.webConfig.centerLatitude) : null;
  tableVal.centerLongitude = sysInfo.webConfig.centerLongitude ? parseFloat(sysInfo.webConfig.centerLongitude) : null;
  initCesium();
})
const initCesium = () => {
  viewer = new Cesium.Viewer('cesiumViewer', { ...cesiumConfig });
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; //修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.cesiumWidget.screenSpaceEventHandler.removeInputAction(Cesium.ScreenSpaceEventType.LEFT_DOUBLE_CLICK); // 取消双击事件


  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider); // 添加注记图层

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 200;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置相机位置为当前定位点
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(
      tableVal.centerLongitude ?? 0,
      tableVal.centerLatitude ?? 0,
      1500
    ),
    orientation: {
      // heading: Cesium.Math.ZERO,
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });
  // 绘制中心点
  drawCenterPoint();
  viererHandler();
}
// 绘制中心点
const drawCenterPoint = () => {
  const centerPoint = Cesium.Cartesian3.fromDegrees(tableVal.centerLongitude ?? 0, tableVal.centerLatitude ?? 0);
  viewer.entities.add({
    id: 'centerPoint',
    position: centerPoint,
    billboard: {
      image: dingweiImage,
      width: 32,
      height: 32,
      verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
      heightReference: Cesium.HeightReference.NONE,
      disableDepthTestDistance: Number.POSITIVE_INFINITY,
    }
  });
};

// 监听经纬度变化
const watchPosition = (moveStatus: boolean = true) => {
  if (!tableVal.centerLongitude || !tableVal.centerLatitude) {
    return;
  }
  const longitude = tableVal.centerLongitude ?? 0;
  const latitude = tableVal.centerLatitude ?? 0;
  if (isNaN(longitude) || isNaN(latitude)) {
    message.error('请输入有效的经纬度');
    return;
  }
  // 更新中心点位置
  const centerPoint = Cesium.Cartesian3.fromDegrees(longitude, latitude, 31000);
  let centerEntity = viewer.entities.getById('centerPoint');
  if (centerEntity) {
    centerEntity.position = new Cesium.ConstantPositionProperty(centerPoint);
  } else {
    centerEntity = viewer.entities.add({
      id: 'centerPoint',
      position: centerPoint,
      billboard: {
        image: dingweiImage,
        width: 32,
        height: 32,
        verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
        heightReference: Cesium.HeightReference.NONE,
        disableDepthTestDistance: Number.POSITIVE_INFINITY,
      }
    });
  }
  if (moveStatus) {
    viewer.camera.setView({
      destination: centerPoint,
      orientation: {
        pitch: -Cesium.Math.PI_OVER_TWO,
        roll: 0
      }
    });
  }
};
// 绑定左键事件
const viererHandler = () => {
  const handler = new Cesium.ScreenSpaceEventHandler(viewer.scene.canvas);
  handler.setInputAction((movement: Cesium.ScreenSpaceEventHandler.PositionedEvent) => {
    const cartesian = viewer.camera.pickEllipsoid(movement.position, viewer.scene.globe.ellipsoid);
    if (cartesian) {
      const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
      const longitude = Cesium.Math.toDegrees(cartographic.longitude);
      const latitude = Cesium.Math.toDegrees(cartographic.latitude);
      tableVal.centerLongitude = parseFloat(longitude.toFixed(9));
      tableVal.centerLatitude = parseFloat(latitude.toFixed(9));
      watchPosition(false);
    }
  }, Cesium.ScreenSpaceEventType.LEFT_CLICK);
};
// 重置经纬度
const resetPosition = () => {
  tableVal.centerLongitude = sysInfo.webConfig.centerLongitude ? parseFloat(sysInfo.webConfig.centerLongitude) : null;
  tableVal.centerLatitude = sysInfo.webConfig.centerLatitude ? parseFloat(sysInfo.webConfig.centerLatitude) : null;
  watchPosition();
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(tableVal.centerLongitude ?? 0, tableVal.centerLatitude ?? 0, 2500),
    orientation: {
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });
};
onUnmounted(() => {
  if (viewer) {
    viewer.destroy();
  }
});
</script>

<template>
  <div>
    <NCard title="基础信息" :bordered="false">
      <n-form ref="formRef" :model="tableVal" label-placement="left" label-width="auto"
        require-mark-placement="right-hanging" class="mt-5">
        <n-flex justify="space-between">
          <div>
            <n-form-item label="图标更换" path="tableVal.webLogoUrl">
              <n-thing content-indented>
                <template #avatar>
                  <n-avatar :size="100" class="border-(1px solid #e0e0e6)" :src="tableVal.webLogoUrl">
                  </n-avatar>
                </template>
                <template #header>
                  <n-upload :action="serviceBaseURL + '/common/upload'" :headers="{ Authorization: token }" :max="1"
                    @finish="handleFinish" @before-upload="beforeUpload" :show-file-list="false">
                    <n-button>更换图标</n-button>
                  </n-upload>
                </template>
                支持图片类型：png、jpg大小不超过2MB
              </n-thing>
            </n-form-item>

            <n-form-item label="系统名称" path="tableVal.webName">
              <n-input v-model:value="tableVal.webName" placeholder="输入系统名称" :maxlength="30" show-count />
            </n-form-item>

          </div>
          <n-form-item label="中心点">
            <n-form-item class="rightBox">
              <n-form ref="formRef" :model="tableVal" label-placement="left" label-width="auto"
                require-mark-placement="right-hanging" class="formBox">
                <n-form-item label="经度" path="tableVal.centerLongitude">
                  <n-input-number v-model:value="tableVal.centerLongitude" placeholder="输入经度"
                    @update:value="watchPosition(true)" :show-button="false" :max="136" :min="73.05" />
                </n-form-item>
                <n-form-item label="纬度" path="tableVal.centerLatitude">
                  <n-input-number v-model:value="tableVal.centerLatitude" placeholder="输入纬度"
                    @update:value="watchPosition(true)" :show-button="false" :max="54" :min="3.51" />
                </n-form-item>
                <n-form-item>
                  <n-button attr-type="button" type="primary" @click="resetPosition">
                    重置
                  </n-button>
                </n-form-item>
              </n-form>
              <div id="cesiumViewer" class="centerPoint"></div>
            </n-form-item>
          </n-form-item>
        </n-flex>
        <n-form-item>
          <n-button attr-type="button" type="primary" @click="handleSaveClick">
            保存
          </n-button>
        </n-form-item>
      </n-form>
    </NCard>

    <NCard title="AI配置" :bordered="false" class="mt-10px">
      <AImodules />
    </NCard>
  </div>

</template>

<style scoped lang="scss">
.centerPoint {
  width: 400px;
  height: 250px;
  border-radius: 10px;
  margin-left: 20px;
}

.rightBox {
  margin-top: 5px;
  margin-right: 50px;
  font-size: 14px;
  display: flex;
  align-self: center;

  p {

    margin-bottom: 10px;
  }

  .formBox {
    margin-right: 10px;
  }
}
</style>
