import { computed, reactive, ref } from 'vue';
import { defineStore } from 'pinia';
import { useEventListener } from '@vueuse/core';
import { SetupStoreId } from '@/enum';
import { localStg } from '@/utils/storage';

// store/ai.ts
export const useAIServerStore = defineStore(SetupStoreId.AIServer, () => {
  const aiList = ref<Api.AI.AIItem[]>([]); // 无人机控制权获取状态 true-已获取 false-未获取
  function updateAIList(newList:Api.AI.AIItem[]) {
    aiList.value = newList;
  }

  function updateAIItemStatus(id: number, status: boolean) {
    if (aiList.value[id]) {
      aiList.value[id].status = status;
    }
  }

  return {
    aiList,
    updateAIList,
    updateAIItemStatus
  };
});
