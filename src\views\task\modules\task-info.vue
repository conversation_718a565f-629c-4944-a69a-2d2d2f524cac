<template>
  <n-modal :show="taskInfoModalShow">
    <n-card title="任务详情" class="w-max" closable @close="handleTaskInfoModalShow">
      <n-descriptions label-placement="left" :column="2" content-class="pr-20px" label-class="pl-20px">
        <n-descriptions-item label="任务名称">{{ props.taskInfoItemData.flightJobName }} </n-descriptions-item>
        <n-descriptions-item label="任务类型">
          <n-tag type="info" size="small" v-if="props.taskInfoItemData.flightJobType === 1">立即执行</n-tag>
          <n-tag type="info" size="small" v-if="props.taskInfoItemData.flightJobType === 2">定时执行</n-tag>
          <n-tag type="info" size="small" v-if="props.taskInfoItemData.flightJobType === 3">周期执行</n-tag>
        </n-descriptions-item>
        <n-descriptions-item label="执行机场">{{ props.taskInfoItemData.myDeviceName }} </n-descriptions-item>
        <n-descriptions-item label="航线名称"><RouterLink style="color: #026DE2;" :to="{ name: 'draw-airline', query: { id: props.taskInfoItemData.flightId?.toString() || ''  } }" >{{ props.taskInfoItemData.flightName }} </RouterLink></n-descriptions-item>
        <n-descriptions-item label="执行时间">{{ props.taskInfoItemData.execTime }} </n-descriptions-item>
        <n-descriptions-item label="返航高度">{{ props.taskInfoItemData.returnHomeHeight }}m </n-descriptions-item>
        <n-descriptions-item v-if="props.taskInfoItemData.flightJobType === 3" label="执行周期">
          {{ props.taskInfoItemData.periodUnit === 1 ? '每天' : '每周' }}
        </n-descriptions-item>
      </n-descriptions>
    </n-card>
  </n-modal>
</template>

<script setup lang="ts">
import { PropType, VNodeChild, computed, h, nextTick, onMounted, onUpdated, reactive, ref, watch } from 'vue';
import dayjs from 'dayjs';
import { darkTheme, useMessage } from 'naive-ui';
import type { AutoCompleteInst, DropdownOption, FormInst, SelectOption } from 'naive-ui';
import { CloseCircleOutline, Add } from '@vicons/ionicons5';
import { useAirportStore } from '@/store/modules/airport';
import { fetchAirLineList, fetchAirportDeviceList, sendTaskSaveInfo } from '@/service/api';
import { RouterLink } from 'vue-router';

defineOptions({ name: 'TaskInfo' });

// 定义组件接受的属性
const props = defineProps<{
  taskInfoModalShow: boolean;
  taskInfoItemData: Api.Task.TaskItem;
}>();

const emits = defineEmits(['task-info-visible']);
const message = useMessage();
const airportStore = useAirportStore();
const formRef = ref<FormInst | null>(null)

function handleTaskInfoModalShow() {
  emits('task-info-visible');
}

onMounted(() => { })

onUpdated(() => {})

// defineExpose({ wayActionArr })
const autoCompleteInstRef = ref<AutoCompleteInst | null>(null)
watch(autoCompleteInstRef, (value) => {
  if (value)
    nextTick(() => value.focus())
})

</script>

<style scoped></style>