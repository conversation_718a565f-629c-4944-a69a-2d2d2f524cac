<script setup lang="ts">
import * as Cesium from 'cesium';
import { onMounted, ref } from 'vue';
import { useMessage, darkTheme } from 'naive-ui';
import { cesiumConfig, josiahProvider, labelProvider, position } from '@/config/cesium_config';
import { fetchDockList } from '@/service/api';
import { MouseTooltip } from '@cesium-extends/tooltip';

const message = useMessage();
let viewer: Cesium.Viewer;
const loaded = ref(false);

const points = ref<Api.Airport.MachineNestInfo[]>([]);
// 初始化地图
const initCesium = () => {
  viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; // 修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  loaded.value = true;

  // 修改图层
  // viewer.imageryLayers.removeAll();
  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider);

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置相机位置为当前定位点
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(position.coords.longitude, position.coords.latitude, 15000), // 设置目的地高度/米
    orientation: {
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });
};
const pointList = [
  {
    level: 1,
    id: 1,
    name: '雨量过大',
    geoJson: {
      longtitude: 113.071169,
      latitude: 28.230261,
    }
  },
  {
    level: 1,
    id: 2,
    name: '风速过快',
    geoJson: {
      longtitude: 113.091169,
      latitude: 28.235261,
    }
  },
  {
    level: 2,
    id: 3,
    name: '指南针收到干扰',
    geoJson: {
      longtitude: 113.073169,
      latitude: 28.250261,
    }
  },
  {
    level: 3,
    id: 4,
    name: 'RTK信号异常',
    geoJson: {
      longtitude: 113.072169,
      latitude: 28.200261,
    }
  }
]
const getSafety = async () => {
  const { error, data } = await fetchDockList();
  if (!error) {
    pointList.forEach(item => {
      console.log("item", item)
      const color = item.level === 1 ? Cesium.Color.YELLOW : item.level === 2 ? Cesium.Color.ORANGE : Cesium.Color.RED;
      const mouseTooltip = new MouseTooltip(viewer, {
        content: item.name,
        offset: [0, -5]
      });
      mouseTooltip.showAt(Cesium.Cartesian3.fromDegrees(item.geoJson.longtitude, item.geoJson.latitude, 50), item.name);
      viewer.entities.add({
        name: item.name,
        position: Cesium.Cartesian3.fromDegrees(item.geoJson.longtitude, item.geoJson.latitude, 0),
        point: {
          pixelSize: 16,
          color: color,
          outlineColor: Cesium.Color.WHITE,
          outlineWidth: 3,
        },
        label: {
          text: item.name,
          font: '14px sans-serif',      // 简化字体设置
          fillColor: Cesium.Color.WHITE,
          verticalOrigin: Cesium.VerticalOrigin.TOP, // 顶部对齐
          pixelOffset: new Cesium.Cartesian2(0, 15), // 向上偏移20像素
          style: Cesium.LabelStyle.FILL,            // 明确指定样式类型
          showBackground: true,                     // 可选：添加背景
          backgroundColor: Cesium.Color.BLACK.withAlpha(0.7),
        }
      })
    })
  }
}
onMounted(async () => {
  initCesium();
  getSafety();
});
</script>
<template>
  <div class="position-relative h100vh p-0!">
    <div id="cesiumViewer" class="h100% p-0!"></div>
  </div>
</template>

<style scoped></style>
