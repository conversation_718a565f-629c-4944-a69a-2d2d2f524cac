<template>
  <NForm ref="formRef" :model="model" :rules="rules" @keyup.enter="handleSubmit" size="large" :show-label="false">
    <NFormItem path="phone">
      <NInput v-model:value="model.phone" :placeholder="$t('page.login.common.phonePlaceholder')" />
    </NFormItem>
    <NFormItem path="phoneCode">
      <div class="w-full flex-y-center gap-16px">
        <NInput v-model:value="model.phoneCode" placeholder="请输入验证码" />
        <NButton size="large" :disabled="isCounting" :loading="loading" @click="getCaptcha(model.phone)">
          {{ label }}
        </NButton>
      </div>
    </NFormItem>
    <NSpace vertical :size="18" class="w-full">
      <NButton type="primary" size="large" round block @click="handleSubmit">
        {{ $t('common.confirm') }}
      </NButton>
      <NButton size="large" round block @click="toggleLoginModule('pwd-login')">
        {{ $t('page.login.common.back') }}
      </NButton>
    </NSpace>
  </NForm>
</template>

<script setup lang="ts">
import { computed, reactive, ref } from 'vue';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { useFormRules, useNaiveForm } from '@/hooks/common/form';
import { useCaptcha } from '@/hooks/business/captcha';
import { fetchPhoneCode, fetchPhoneLogin } from '@/service/api';
import { useAuthStore } from '@/store/modules/auth';
import { useMessage } from 'naive-ui';

defineOptions({
  name: 'codeLogin'
});

const authStore = useAuthStore();
const { toggleLoginModule } = useRouterPush();
const { formRef, validate } = useNaiveForm();
const { label, isCounting, loading, getCaptcha } = useCaptcha();
const message = useMessage();

interface FormModel {
  phone: string;
  phoneCode: string;
}

const model: FormModel = reactive({
  phone: '',
  phoneCode: ''
});

const rules = computed<Record<keyof FormModel, App.Global.FormRule[]>>(() => {
  const { formRules } = useFormRules();

  return {
    phone: formRules.phone,
    phoneCode: formRules.code
  };
});

async function handleSubmit() {
  try {
    await validate();
    await authStore.loginByCode(model.phone, model.phoneCode);
  } catch (error) {
    return;
  }
}
</script>

<style scoped></style>
