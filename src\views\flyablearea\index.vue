<script setup lang="ts">
import * as Cesium from 'cesium';
import { onMounted, ref } from 'vue';
import { useMessage, darkTheme } from 'naive-ui';
import { cesiumConfig, j<PERSON><PERSON><PERSON><PERSON><PERSON>, labelProvider, position } from '@/config/cesium_config';
import { fetchAllFlyArea } from '@/service/api';

const message = useMessage();
let viewer: Cesium.Viewer;
const loaded = ref(false);
const allFlyAreaList = ref<any[]>([]); // 禁飞区列表
// const points = ref<Api.Airport.MachineNestInfo[]>([]);
const point = [
  {
    id: 1,
    longitude: 113.06921788911033,
    latitude: 28.331747945471026,
  },
  {
    id: 2,
    longitude: 113.16671264089479,
    latitude: 28.29865009615674,
  },
  {
    id: 3,
    longitude: 113.19153602788047,
    latitude: 28.23389343445492,
  },
  {
    id: 4,
    longitude: 113.15843817856623,
    latitude: 28.14611218192573,
  },
  {
    id: 5,
    longitude: 113.0199308743706,
    latitude: 28.12776446111021,
  },
  {
    id: 6,
    longitude: 112.95229613881531,
    latitude: 28.157984236571075,
  },
  {
    id: 7,
    longitude: 112.9278325110613,
    latitude: 28.224179935199622,
  },
  {
    id: 8,
    longitude: 112.98971109890972,
    latitude: 28.299369614620105,
  },
]

// 初始化地图
const initCesium = () => {
  viewer = new Cesium.Viewer('cesiumViewer', cesiumConfig);
  (viewer.cesiumWidget.creditContainer as HTMLElement).style.display = 'none'; // 隐藏logo
  viewer.scene.globe.baseColor = Cesium.Color.BLACK; // 修改地图背景
  viewer.scene.postProcessStages.fxaa.enabled = true; // 开启抗锯齿
  loaded.value = true;

  // 修改图层
  // viewer.imageryLayers.removeAll();
  viewer.imageryLayers.addImageryProvider(josiahProvider);
  viewer.imageryLayers.addImageryProvider(labelProvider);

  // 设置最大和最小缩放
  viewer.scene.screenSpaceCameraController.minimumZoomDistance = 400;
  viewer.scene.screenSpaceCameraController.maximumZoomDistance = 8000000;

  // 设置相机位置为当前定位点
  viewer.camera.setView({
    destination: Cesium.Cartesian3.fromDegrees(position.coords.longitude, position.coords.latitude, 50000), // 设置目的地高度/米
    orientation: {
      pitch: -Cesium.Math.PI_OVER_TWO,
      roll: 0
    }
  });
  loadJSONAndDrawNoFlyZone();
};

// 加载禁飞区JSON文件并绘制禁飞区
async function loadJSONAndDrawNoFlyZone() {
  try {
    const response = await fetch('/defaultNoFlyZone.json');
    const data = await response.json();
    // 绘制
    addPolygons(data.features);
  } catch (error) {
    console.error('Error loading JSON data:', error);
  }
}

// 绘制禁飞区
function addPolygons(features: any[]) {
  features.forEach(feature => {
    const polygon = feature.geometry.coordinates[0];
    const positions = polygon.map((coord: [number, number]) => Cesium.Cartesian3.fromDegrees(coord[0], coord[1]));
    viewer.entities.add({
      polygon: {
        hierarchy: positions,
        material: Cesium.Color.fromCssColorString('red').withAlpha(0.35)
      }
    });
  });
}

// 获取空域
async function getDockList() {
  const { error, data } = await fetchAllFlyArea();
  if (!error) {
    console.log("data", data)
    allFlyAreaList.value = data.rows;
    drawFlyableArea();
  }
}
// 绘制空域
const drawFlyableArea = () => {
  allFlyAreaList.value.forEach((zone, index) => {
    if (zone.geoJson.type === 'circle') {
      drawCircleZone(zone);
    } else if (zone.geoJson.type === 'polygon') {
      drawPolygonZone(zone);
    }
  });
}
const drawPolygonZone = (zone: Api.Device.NoflyInfo) => {
  console.log("zone", zone)
  const allpositions = [...zone.geoJson.geometry.coordinates, zone.geoJson.geometry.coordinates[0]].map((pos) => {
    return { longitude: pos[0], latitude: pos[1] }
  }
  );
  console.log("allpositions", allpositions)
  const sum = allpositions.reduce((acc, point) => {
    return {
      lon: acc.lon + point.longitude,
      lat: acc.lat + point.latitude
    };
  }, { lon: 0, lat: 0 });
  const centerLon = sum.lon / allpositions.length;
  const centerLat = sum.lat / allpositions.length;
  const centerPosition = Cesium.Cartesian3.fromDegrees(centerLon, centerLat);
  const boundaryPositions = [...allpositions, allpositions[0]].map(p =>
    Cesium.Cartesian3.fromDegrees(p.longitude, p.latitude)
  );
  viewer.entities.add({
    position: centerPosition,
    polygon: {
      hierarchy: boundaryPositions,
      // material: Cesium.Color.BLUE.withAlpha(0.3),
      material: Cesium.Color.fromCssColorString('#1177fb').withAlpha(0.15),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    polyline: {
      positions: boundaryPositions,
      width: 5,
      material: Cesium.Color.fromCssColorString('#1177fb'),
      clampToGround: true // 贴地显示
    },
    label: {
      text: `${zone.noFlyName.replace('禁飞区', '适飞区')}`,
      font: '14px sans-serif',      // 简化字体设置
      fillColor: Cesium.Color.WHITE,
      verticalOrigin: Cesium.VerticalOrigin.TOP, // 顶部对齐
      pixelOffset: new Cesium.Cartesian2(0, 15), // 向上偏移20像素
      style: Cesium.LabelStyle.FILL,            // 明确指定样式类型
      showBackground: true,                     // 可选：添加背景
      backgroundColor: Cesium.Color.BLACK.withAlpha(0.7),
    }
  });
};
const drawCircleZone = (zone: Api.Device.NoflyInfo) => {
  // 生成椭圆顶点坐标
  const allpositions = computeEllipsePositions(
    { longitude: zone.centerGeoJson.coordinates[0], latitude: zone.centerGeoJson.coordinates[1] },
    zone.radius || 0,
    zone.radius || 0,
    6
  );
  const centerPosition = Cesium.Cartesian3.fromDegrees(zone.centerGeoJson.coordinates[0], zone.centerGeoJson.coordinates[1]);
  viewer.entities.add({
    id: `${zone.id}`,
    polygon: {
      hierarchy: allpositions,
      // material: Cesium.Color.RED.withAlpha(0.3),
      material: Cesium.Color.fromCssColorString('#1177fb').withAlpha(0.15),
      heightReference: Cesium.HeightReference.CLAMP_TO_GROUND
    },
    description: 'noflyarea'
  })
  viewer.entities.add({
    polyline: {
      positions: allpositions,
      width: 2.5,
      // material: Cesium.Color.RED.withAlpha(0.9),
      material: Cesium.Color.fromCssColorString('#1177fb'),
      clampToGround: true,
    },
    description: 'noflyareaborder',
  });
  viewer.entities.add({
    position: centerPosition,
    label: {
      text: `${zone.noFlyName.replace('禁飞区', '适飞区')}`,
      font: '14px sans-serif',      // 简化字体设置
      fillColor: Cesium.Color.WHITE,
      verticalOrigin: Cesium.VerticalOrigin.TOP, // 顶部对齐
      pixelOffset: new Cesium.Cartesian2(0, 15), // 向上偏移20像素
      style: Cesium.LabelStyle.FILL,            // 明确指定样式类型
      showBackground: true,                     // 可选：添加背景
      backgroundColor: Cesium.Color.BLACK.withAlpha(0.7),
    }
  });
}
// 计算圆形禁飞区轮廓点
function computeEllipsePositions(
  center: { longitude: number; latitude: number },
  semiMajorAxis: number,
  semiMinorAxis: number,
  granularity: number
): Cesium.Cartesian3[] {
  const positions: Cesium.Cartesian3[] = [];
  for (let i = 0; i < 360; i += granularity) {
    const radians = Cesium.Math.toRadians(i);
    const x = semiMajorAxis * Math.cos(radians);
    const y = semiMinorAxis * Math.sin(radians);
    // 将米转换为经纬度
    const position = Cesium.Cartesian3.fromDegrees(center.longitude + x / 111319.9, center.latitude + y / 111319.9);
    positions.push(position);
  }
  // 闭合椭圆，使首尾相连
  positions.push(positions[0]);
  return positions;
}
onMounted(async () => {
  initCesium();
  await getDockList();
});
</script>
<template>
  <div class="position-relative h100vh p-0!">
    <div id="cesiumViewer" class="h100% p-0!"></div>
  </div>
</template>

<style scoped></style>
