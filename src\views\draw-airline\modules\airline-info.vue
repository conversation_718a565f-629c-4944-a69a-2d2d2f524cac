<template>
  <div class="absolute zindex-1 bg-dark-1 right-0 top-0 w-330px h-100% text-light ">
    <div class="bg-dark-1 text-18px pl-15px py-15px">
      航点模式
    </div>
    <div class="py-10px bg-dark-8">
      <n-flex justify="space-between ">
        <div class="text-center flex-1">
          <n-text class="text-light font-700 text-16px">{{ props.waypointLatLngArray.length || "--" }}</n-text><br />
          <n-text class="text-coolgray">航点数</n-text>
        </div>
        <div class="text-center flex-1">
          <n-text class="text-light font-700 text-16px">{{ props.sumFlyDistance.toFixed(2) }} m</n-text><br />
          <n-text class="text-coolgray">预计飞行里程</n-text>
        </div>
        <div class="text-center flex-1">
          <n-text class="text-light font-700 text-16px">{{ predictFlyTime }}</n-text><br />
          <n-text class="text-coolgray">预计飞行时间</n-text>
        </div>
      </n-flex>
    </div>
    <!-- 任务设置 -->
    <n-flex class="" vertical justify="space-between" align="center">
      <n-config-provider :theme="darkTheme">
        <div class="bg-dark-3 text-14px pl-15px py-15px">
          任务设置
        </div>
        <n-card embedded :bordered="false" class="text-light rd-0 ">
          任务航线名称
          <n-input v-model:value="flightName" placeholder="输入航线名称" class="bg-dark-3  mt-10px">
            <template #suffix>
              <n-icon :component="Pencil" />
            </template>
          </n-input>
        </n-card>
        <n-card embedded :bordered="false" class="text-light rd-0 mt-1px">
          相对起飞点高度（m）
          <n-input-number v-model:value="surfaceRelativeHeight" class="bg-dark-3  mt-10px" :min="10"
          :max="500"/>
        </n-card>
        <!-- <n-card embedded :bordered="false" class="text-light rd-0 mt-1px">
          飞往航线首航点速度（m/s）
          <n-input-number :value="100" class="bg-dark-3  mt-10px" />
        </n-card> -->
        <n-card embedded :bordered="false" class="text-light rd-0 mt-1px">
          全局航线速度（m/s）
          <n-input-number v-model:value="transitionalSpeed" class="bg-dark-3  mt-10px" :min="2" :max="15"/>
        </n-card>
        <n-card embedded :bordered="false" class="text-light rd-0 mt-1px">
          航点类型
          <n-select v-model:value="destinationType" class="bg-dark-3  mt-10px" :options="options" :render-label="renderLabel" :render-tag="renderTag" />
        </n-card>
      </n-config-provider>
      <n-button @click="onSaveAirLine" type="info" :loading="buttonLoading" class="w-80%">保存设置</n-button>
    </n-flex>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, onMounted, onUpdated, ref, watch, reactive, VNodeChild, h } from 'vue';
import { darkTheme, NIcon, NImage, NText, SelectRenderTag, SelectOption, useMessage } from 'naive-ui'
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { Pencil } from '@vicons/ionicons5'
import { useAirportStore } from '@/store/modules/airport';
const destinationType = ref<string>('toPointAndStopWithDiscontinuityCurvature');
  interface Option {
  src: string;
  label: string;
  value: string;
}
const straightFlightUrl = new URL('../../../assets/svg-icon/straight-flight.svg', import.meta.url).href;
const coordinatedTurnUrl = new URL('../../../assets/svg-icon/coordinated-turn.svg', import.meta.url).href;
const curveFlight1Url = new URL('../../../assets/svg-icon/curve-flight1.svg', import.meta.url).href;
const curveFlight2Url = new URL('../../../assets/svg-icon/curve-flight2.svg', import.meta.url).href;
const options = reactive<Option[]>([
  {
    label: '直线飞行，飞行器到点停',
    value: 'toPointAndStopWithDiscontinuityCurvature',
    src: straightFlightUrl
  },
  {
    label: '协调转弯，不过点，提前转弯',
    value: 'coordinateTurn',
    src: coordinatedTurnUrl
  },
  {
    label: '曲线飞行，飞行器到点停',
    value: 'toPointAndStopWithContinuityCurvature',
    src: curveFlight1Url
  },
  {
    label: '曲线飞行，飞行器过点不停',
    value: 'toPointAndPassWithContinuityCurvature',
    src: curveFlight2Url
  },
])

const renderLabel = (option: Option) => {
  return h(
    'div',
    {
      class: 'custom-option',
      style: 'display: flex; align-items: center; padding: 8px;'
    },
    [
      h('div', { class: 'label-content' }, [
      h(NText, { depth: 1 }, option.label),
      h('img', {
        width: '100%',
        src: option.src,
        style: 'margin-right: 12px;'
      }),
      ])
    ]
  );
};
interface RenderTagProps {
  option: {
    label: string;
    src: string;
  };
}
const renderTag = ({ option }: { option: SelectOption }) => {
  return h(NText, { depth: 1 }, () => ((option as unknown) as Option).label);
};
const airportStore = useAirportStore();
const message = useMessage();
defineOptions({
  name: 'AirLineInfo'
});
const emits = defineEmits(['save-airline']);
const surfaceRelativeHeight = ref(100);
const transitionalSpeed = ref(10);
const flightName = ref('');

// 计算飞行时间（单位：分钟）
const predictFlyTime = computed(() => {
  if (props.sumFlyDistance && transitionalSpeed.value) {
    const speedInMetersPerHour = transitionalSpeed.value * 3600; // 将速度从米/秒转换为米/小时
    const hours = props.sumFlyDistance / speedInMetersPerHour;
    const minutes = hours * 60 + 5; // 将小时转换为分钟 再+5分钟
    return minutes.toFixed(1) + 'min';
  }
  return '--';
});

// 定义组件接受的属性
const props = defineProps<{
  sumFlyDistance: number;
  waypointLatLngArray: Api.AirLine.WayPointList[];
  airLineConfig?: Api.AirLine.AirLineConfig;
}>();

const buttonLoading = ref<boolean>(false);
const changeButtonLoading = ((value: boolean) => {
  buttonLoading.value = value;
});
defineExpose({ changeButtonLoading,surfaceRelativeHeight });
function onSaveAirLine() {
  // console.log("onSaveAirLine: ", surfaceRelativeHeight, transitionalSpeed, flightName)
  if (!Boolean(flightName.value)) {
    message.error("请输入航线名称");
    return
  }
  buttonLoading.value = true;
  emits('save-airline', {
    surfaceRelativeHeight: surfaceRelativeHeight.value,
    transitionalSpeed: transitionalSpeed.value,
    flightName: flightName.value,
    destinationType: destinationType.value
  });
  setTimeout(() => {
    buttonLoading.value = false;
  }, 5000); // 5秒后强制重置，避免长时间卡住
  // airportStore.addVideoBox(props.airportFlyInfo[index]);
}
onMounted(() => {
})

onUpdated(() => {
})

watch(() => props.airLineConfig, (newConfig) => {
  console.log('airLineConfig updated:', newConfig);
  if (newConfig && newConfig.flightName) {
    surfaceRelativeHeight.value = newConfig.surfaceRelativeHeight || 100;
    transitionalSpeed.value = newConfig.transitionalSpeed || 10;
    flightName.value = newConfig.flightName || '';
  }
},
  { immediate: true, deep: true } // 立即触发，并且进行深度监听
);
</script>

<style scoped>
.n-card.n-card--embedded {
  background-color: rgb(24 24 24 / var(--un-bg-opacity));
}
</style>
<!-- <style>
.n-input .n-input__input-el,
.n-input .n-input__textarea-el {
  color: rgb(246 246 246 / var(--un-text-opacity));
}
</style> -->
