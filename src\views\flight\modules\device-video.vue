<template>
  <div class="device_video bg-dark-theme color-light p-15px w-max rd-8px">
    <!-- 机场信息 -->
    <div class="flex justify-between pb-10px">
      <n-text class="color-light">{{ newDeviceItem.deviceName }}</n-text>
      <div class="flex">
        <n-tag size="small" v-if="newDeviceItem.host?.modeCode === '0'" type="success">
          空闲中<template #icon><n-icon :component="Ellipse" size="10" /> </template>
        </n-tag>
        <n-tag size="small" v-else-if="newDeviceItem.host?.modeCode === '1' || newDeviceItem.host?.modeCode === '2'"
          type="warning">
          {{ newDeviceItem.host?.modeCode === '1' ? '现场调试' : '远程调试' }}
          <template #icon><n-icon :component="Ellipse" size="10" /> </template>
        </n-tag>
        <n-tag size="small" v-else-if="newDeviceItem.host?.modeCode === '3'" type="error">
          固件升级中<template #icon><n-icon :component="Ellipse" size="10" /> </template>
        </n-tag>
        <n-tag size="small" v-else-if="newDeviceItem.host?.modeCode === '4'" type="info">
          作业中<template #icon><n-icon :component="Ellipse" size="10" /> </template>
        </n-tag>
        <n-tag size="small" v-else>离线<template #icon><n-icon :component="Ellipse" size="10" /> </template> </n-tag>
        <div class="inline-block ml-10px mr-10px">
          <n-tag @click="goToAirportDetailPage" :bordered="false" size="small" class="cursor-pointer">详情</n-tag>
        </div>
        <n-icon @click="handleClose" class="cursor-pointer" size="20" color="#ffffff" :component="Close" />
      </div>
    </div>
    <!-- 飞行任务 -->
    <div class="flex justify-between pb-5px">
      <n-text class="color-light pr-5px"><n-icon size="14" color="#52C41A" :component="List" />
        {{ newDeviceItem?.host?.taskName || '暂无任务' }}
      </n-text>
      <n-dropdown v-if="errOptionsText" label-field="messageZh" trigger="click" placement="bottom"
        :options="newDeviceItem?.errorMsgArray">
        <n-text
          class="inline-block w-180px overflow-hidden h-20px line-height-20px color-light font-size-12px ellipsis-text text-right">
          <n-icon size="14" color="#EA8712" :component="Warning" /> {{ errOptionsText }}</n-text>
      </n-dropdown>
    </div>

    <!-- 无人机信息 -->
    <div class="flex justify-between">
      <div>
        <n-text class="color-light pr-5px"><n-icon size="14" color="#fff" :component="AirplaneOutline" />
          {{ newDeviceItem?.childDevice?.droneName }}</n-text>
        <n-tag class="mr-10px" size="small" :type="droneStatus?.listClass || 'default'">
          {{ droneStatus?.dictLabel || '离线' }}
          <template #icon><n-icon :component="Ellipse" size="10" /> </template>
        </n-tag>

        <n-tag size="small">{{ newDeviceItem?.host?.capacityPercent || 0 }}%
          <template #icon><n-icon :component="BatteryHalfOutline" size="10" /> </template>
        </n-tag>
      </div>
    </div>

    <div class="mt-10px w-360px h-205px">

      <!-- <video ref="videoPlayer" class=" " controls preload="auto">
      </video> -->

      <video :id="`player-container-${newDeviceItem.deviceSn}`" class="object-fill w-100% h-100% pt-0" preload="auto"
        playsinline webkit-playsinline>
      </video>

    </div>
    <div class="flex justify-between mt-10px">
      <n-text class="color-light">风速 {{ newDeviceItem.host?.windSpeed }} m/s</n-text>
      <n-text class="color-light">雨量
        {{ newDeviceItem.host?.rainfall === '1' ? '小' : newDeviceItem.host?.rainfall === '2' ? '中' :
          newDeviceItem.host?.rainfall === '3' ? '大' : '无' }}
      </n-text>
      <n-text class="color-light">舱内温度 {{ newDeviceItem.host?.temperature }}℃</n-text>
    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, h, inject, onBeforeUnmount, onMounted, onUnmounted, onUpdated, reactive, ref, watch } from 'vue';
import TCPlayer, { type TCPlayerConfig } from 'tcplayer.js';
import type { Component, Ref } from 'vue'
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { Close, Ellipse, Warning, List, AirplaneOutline, BatteryHalfOutline } from '@vicons/ionicons5'
import { useAirportStore } from '@/store/modules/airport';
import { NIcon, useMessage } from 'naive-ui'
import { fetchLivestreamInfo, startLivestream, stopLivestream, takeOffToPoint } from '@/service/api';
import { useSocketStore } from '@/store/modules/socket';
import { useDeviceStore } from '@/store/modules/device';

const airportStore = useAirportStore();
const deviceStore = useDeviceStore();
const { routerPushByKey } = useRouterPush();
const message = useMessage();
const socketStore = useSocketStore();
const socket = inject<{ chatMessage: Ref<string> }>('useSocket');  // 通过 inject 获取 provide 的 socket 对象

defineOptions({
  name: 'DeviceVideo'
});
// 定义组件接受的属性
const props = defineProps<{ airportVideoIndex: number; }>();

const airportVideoItem = ref<Api.Airport.AirportDeviceInfo>({});
const newDeviceItem = reactive<Api.Airport.AirportDeviceInfo>({});

// 将 player 相关状态统一管理
const playerState = reactive({
  instance: null as InstanceType<typeof TCPlayer> | null,
  config: {
    licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1329835514_1/v_cube.license',
    ProgressMarker: false,
    muted: true,
    controls: false,
    autoplay: true,
  }
});

function renderIcon(icon: Component) {
  return () => {
    return h(NIcon, { class: 'icon_warning' }, {
      default: () => h(icon)
    })
  }
}
// 使用计算属性合并错误信息
const errOptionsText = computed(() => {
  return newDeviceItem.errorMsgArray?.map(option => option.messageZh).join('---');
});

// 获取无人机状态
const droneStatus = computed(() => {
  // console.log("droneStatus ~ deviceStore.droneStatusList:", deviceStore.droneStatusList)
  return deviceStore.droneStatusList[Number(newDeviceItem?.childDevice?.host?.modeCode)];
});

// 窗口关闭事件
function handleClose() {
  airportStore.removeVideoBoxByID(airportVideoItem.value.id || -1);
}

// 去往详情页
function goToAirportDetailPage() {
  let droneParam = { camerasCameraMode: '0', camerasPhotostate: '0', camerasZoomFactor: '0', camerasRecordingState: '0', cameraType: 'ir' };
  if (newDeviceItem.childDevice && newDeviceItem.childDevice.host) {
    const { camerasCameraMode, camerasPhotostate, camerasZoomFactor, camerasRecordingState, cameraType } = newDeviceItem.childDevice.host;
    droneParam = {
      camerasCameraMode, camerasPhotostate, camerasZoomFactor, camerasRecordingState, cameraType
    }
  }
  deviceStore.setDockInfo({
    latitude: newDeviceItem.latitude || 0,
    longitude: newDeviceItem.longitude || 0,
    altitude: newDeviceItem.altitude || 0,
    droneParam
  });
  console.log('deviceStore.dockInfo:', deviceStore.dockInfo);
  routerPushByKey('fly-detail', {
    query: {
      sn: newDeviceItem.deviceSn || '', dsn: newDeviceItem.childDevice?.droneSn || ''
    }
  });
}

// 一键起飞
async function onClickToTakeOff() {
  await takeOffToPoint(newDeviceItem.deviceSn || '', { targetHeight: 120 });
}


let remoteUsers: any = {};

const agoraPara = reactive({ appid: '', token: '', channel: '', uid: 0, stream: {} });

const dronePara = reactive({
  livestreamSource: [],
  droneSelected: undefined as string | undefined,
  cameraSelected: undefined as string | undefined,
  videoSelected: undefined as string | undefined,
  claritySelected: 0,
  lensList: [] as string[],
  lensSelected: undefined as string | undefined,
  isDockLive: false
})

const livePara = reactive({ url: '' });

const onStreamStop = async () => {
  // 销毁播放器实例
  if (playerState.instance) {
    playerState.instance.dispose();
    playerState.instance = null;
  }
  const { error } = await stopLivestream({ sn: newDeviceItem.deviceSn });
}

const onStreamStart = async () => {
  const { data } = await startLivestream({ sn: newDeviceItem.deviceSn });
  if(!data || !data.url){
    return
  }
  livePara.url = data.url;
  playerState.config.licenseUrl = data.license;
  // 如果已存在播放器实例，先销毁
  if (playerState.instance) {
    playerState.instance.dispose();
  }

  console.log("onStreamStart ~ newDeviceItem.deviceSn:", `player-container-${newDeviceItem.deviceSn}`)
  // 创建新的播放器实例
  playerState.instance = new TCPlayer(`player-container-${newDeviceItem.deviceSn}`, {
    ...playerState.config,
    sources: [{
      src: livePara.url,
    }],
  });
}

// 处理socket消息
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  if (biz_code === 'device_osd' && newDeviceItem.childDevice && newDeviceItem.childDevice.droneSn === sn) { // 无人机消息
    newDeviceItem.childDevice.host = data.host;
  } else if (biz_code === 'dock_osd' && newDeviceItem?.deviceSn === sn) { // 机场消息
    newDeviceItem.host = data.host;
  } else if (biz_code === 'device_hms' && newDeviceItem.deviceSn === sn) { // 设备告警
    newDeviceItem.errorMsgArray = data.host;
  }
}

// 监听Socket消息
watch(() => socketStore?.chatMessage?.value, (nVal, oVal) => {
  if (nVal) {
    onProcessMessage(nVal);
  }
}, { deep: true, immediate: true });

onUpdated(() => {
  // newDeviceItem.host
  // console.log("onUpdated ~ airportStore.videoBoxes:", airportStore.videoBoxes)
  // airportVideoItem.value = airportStore.videoBoxes[props.airportVideoIndex]
});

onMounted(() => {
  airportVideoItem.value = airportStore.videoBoxes[props.airportVideoIndex];
  Object.assign(newDeviceItem, airportStore.videoBoxes[props.airportVideoIndex]);
  onStreamStart();
});

onBeforeUnmount(() => {
  onStreamStop();
  // airportStore.removeAllVideoBox();
})


</script>

<style>
/* .device_video { */
.icon_warning {
  color: rgb(234, 135, 18);
}

#player>div:not(:last-child) {
  display: none;
}

/* } */
</style>
