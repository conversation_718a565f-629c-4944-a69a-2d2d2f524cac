import { computed, reactive, ref } from 'vue';
import { useRoute } from 'vue-router';
import { defineStore } from 'pinia';
import { useLoading } from '@sa/hooks';
import { SetupStoreId } from '@/enum';
import { useRouterPush } from '@/hooks/common/router';
import { fetchGetUserInfo, fetchLogin, fetchGetSysInfo, fetchPhoneLogin, fetchUserConfigInfo } from '@/service/api';
import { localStg } from '@/utils/storage';
import { $t } from '@/locales';
import { useRouteStore } from '../route';
import { useTabStore } from '../tab';
import { clearAuthStorage, getToken } from './shared';
import useSocket from "@/service/websocket/useSocket"
import { useAirportStore } from '../airport';

export const useAuthStore = defineStore(SetupStoreId.Auth, () => {
  const route = useRoute();
  const routeStore = useRouteStore();
  const tabStore = useTabStore();
  const { toLogin, redirectFromLogin } = useRouterPush(false);
  const { loading: loginLoading, startLoading, endLoading } = useLoading();

  const token = ref(getToken());

  const userInfo: Api.Auth.UserInfo = reactive({
    user: {},
    roles: [],
  });

  const sysInfo: Api.Auth.SysInfo = reactive({
    webConfig: {
      webName: '',
      webLogoName: '',
      webLogoUrl: '',
      webLogo: '',
      centerLongitude: '',
      centerLatitude: '',
    },
    dockConfig: {},
  });

  /** is super role in static route */
  const isStaticSuper = computed(() => {
    const { VITE_AUTH_ROUTE_MODE, VITE_STATIC_SUPER_ROLE } = import.meta.env;

    return VITE_AUTH_ROUTE_MODE === 'static' && userInfo.roles.includes(VITE_STATIC_SUPER_ROLE);
  });

  /** Is login */
  const isLogin = computed(() => Boolean(token.value));

  /** Reset auth store */
  async function resetStore() {
    const authStore = useAuthStore();
    const airportStore = useAirportStore();

    clearAuthStorage();

    authStore.$reset();
    airportStore.$reset();

    if (!route.meta.constant) {
      await toLogin();
    }

    tabStore.cacheTabs();
    routeStore.resetStore();
  }

  /**
   * Login
   *
   * @param userName User name
   * @param password Password
   * @param [redirect=true] Whether to redirect after login. Default is `true`
   */
  async function login(userName: string, password: string, redirect = true) {
    startLoading();

    const { data: loginToken, error } = await fetchLogin(userName, password);

    if (!error) {
      const pass = await loginByToken(loginToken);
      if (pass) {
        handelLoginSuccess(redirect);
      }
    } else {
      resetStore();
    }

    endLoading();
  }


  // 使用验证码登录
  async function loginByCode(phone: string, phoneCode: string, redirect = true) {
    startLoading();

    const { data: loginToken, error } = await fetchPhoneLogin({ phone, phoneCode });

    // 检查 data 是否为 LoginToken 类型
    if (!error) {
      const pass = await loginByToken(loginToken);
      if (pass) {
        handelLoginSuccess(redirect);
      }
    } else {
      resetStore();
    }

    endLoading();
  }

  // 登录成功后操作
  async function handelLoginSuccess(redirect: boolean = true) {
    await routeStore.initAuthRoute();

    await redirectFromLogin(redirect);

    if (routeStore.isInitAuthRoute) {
      useSocket().retryConnect();
      window.$notification?.success({
        title: $t('page.login.common.loginSuccess'),
        content: $t('page.login.common.welcomeBack', { userName: userInfo.user.nickName }),
        duration: 4500
      });
    }
  }


  async function loginByToken(loginToken: Api.Auth.LoginToken) {
    // 1. stored in the localStorage, the later requests need it in headers
    localStg.set('token', loginToken.token);
    console.log("loginByToken ~ token:", JSON.stringify(loginToken.token))
    // localStg.set('refreshToken', loginToken.refreshToken);

    // 2. get user info
    const pass = await getUserInfo();
    // 3. get user config
    await getUserConfigInfo();

    if (pass) {
      token.value = loginToken.token;

      return true;
    }

    return false;
  }

  async function getUserInfo() {
    const { data: info, error } = await fetchGetUserInfo();

    if (!error) {
      // update store
      Object.assign(userInfo, info);

      return true;
    }

    return false;
  }

  async function getUserConfigInfo() {
    const { data: info, error } = await fetchUserConfigInfo();

    if (!error) {
      // update store
      Object.assign(sysInfo, info);

      return true;
    }

    return false;
  }

  // 会在页面刷新时调用
  async function initUserInfo() {
    const hasToken = getToken();

    if (hasToken) {
      const pass = await getUserInfo();
      await getUserConfigInfo();

      if (!pass) {
        resetStore();
      }
    }
  }

  return {
    token,
    userInfo,
    sysInfo,
    isStaticSuper,
    isLogin,
    loginLoading,
    resetStore,
    login,
    loginByCode,
    initUserInfo
  };
});
