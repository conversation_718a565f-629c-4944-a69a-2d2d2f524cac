<script setup lang="ts">
import { h, onMounted, reactive, ref } from 'vue';
import { type FormInst, NButton, NIcon, NInput, NSwitch, NText, useDialog, useMessage, NCheckbox } from 'naive-ui';
import { addDept, delDept, editDept, fetchDeptList } from '@/service/api/dept';
import { Close } from "@vicons/ionicons5";
import { FormattedValue } from 'naive-ui/es/date-picker/src/interface';
import { useAuthStore } from '@/store/modules/auth';
import { getDeptAiByID, updateDeptAiItem } from '@/service/api';

const tableLoading = ref(false);
const message = useMessage();
const dialog = useDialog();
const authStore = useAuthStore();
const { userInfo, token } = authStore;

const formValue = reactive<{ deptName: string }>({
  deptName: ''
});

const columns = [
  {
    title: '部门名称',
    key: 'deptName'
  },
  {
    title: '显示顺序',
    key: 'orderNum'
  },
  {
    title: '负责人',
    key: 'leader'
  },
  {
    title: '联系号码',
    key: 'phone'
  },
  {
    title: '状态',
    key: 'status',
    render(row: any) {
      return h(NSwitch, {
        value: row.status === '0',
        onUpdateValue: value => handleSwitchChange(row, value)
      });
    }
  },
  {
    title: '创建时间',
    key: 'createTime'
  },
  {
    title: '有效期',
    key: 'validityDate'
  },
  {
    title: '操作',
    key: 'action',
    render(row: Api.Dept.DeptItem) {
      return h('span', [
        h(
          NText,
          {
            type: 'info',
            class: 'cursor-pointer',
            onClick: () => AISettingByDept(row)
          },
          { default: () => 'AI配置' }
        ),
        h(
          NText,
          {
            type: 'info',
            class: 'cursor-pointer ml-10px',
            onClick: () => updateByDept(row)
          },
          { default: () => '修改' }
        ),
        h(
          NText,
          {
            type: 'error',
            class: 'cursor-pointer ml-10px',
            onClick: () => deleteByDept(row)
          },
          { default: () => '删除' }
        )
      ]);
    }
  }
];

const showAddByDeptModal = ref(false);
const treeData = ref<Api.Dept.DeptItem[]>([]);
const updateOrAddTitle = ref('');
const formRef = ref<FormInst | null>(null)

const infoRules: any = {
  deptName: [{ key: 'deptName', required: true, trigger: ['blur', 'input'], message: '请填写组织名称' }],
  validityDate: [{ key: 'validityDate', required: true, trigger: ['blur', 'input'], message: '请填写有效时间' }],
  orderNum: [{ key: 'orderNum', required: true, type: 'number', trigger: ['blur', 'input'], message: '请填写排序顺序' }],
  parentId: [{ key: 'parentId', required: true, type: 'number', trigger: ['blur', 'select'], message: '请选择所属部门' }],
};

const infoModel = ref<Api.Dept.DeptItem>({
  parentId: null,
  deptId: null,
  deptName: '',
  orderNum: null,
  phone: '',
  email: '',
  leader: '',
  status: 0,
  validityDate: null,
});

const statusList = [
  {
    id: '0',
    label: '正常'
  },
  {
    id: '1',
    label: '停用'
  }
];

// 点击新增组织
function addOrgOpen() {
  showAddByDeptModal.value = true;
  updateOrAddTitle.value = '新增组织';
  infoModel.value = {
    parentId: null,
    deptId: null,
    deptName: '',
    orderNum: null,
    phone: '',
    email: '',
    leader: '',
    status: 0,
    validityDate: null
  }
}

function updateByDept(row: Api.Dept.DeptItem) {
  showAddByDeptModal.value = true;
  updateOrAddTitle.value = '修改组织';
  const data = {
    ...row
  }
  infoModel.value = data;
}

// 删除
async function deleteByDept(row: Api.Dept.DeptItem) {
  dialog.error({
    title: '警告',
    content: `确定要删除[${row.deptName}]的组织吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const { error } = await delDept(row.deptId || 0);
      if (!error) {
        message.success('删除成功');
        tableLoading.value = true;
        await handleQueryClick();
      }
    }
  });
}

const showAISettingModal = ref(false); // AI配置弹窗展示
const AISettingDept = ref(0); // 当前修改AI配置的公司ID
const AISettingList = ref<Api.AI.AIItem[]>([]); // 确保指定正确的类型

// 点击某个子公司AI配置
async function AISettingByDept(row: Api.Dept.DeptItem) {
  showAISettingModal.value = true;
  AISettingDept.value = row.deptId || 0;
  const { data } = await getDeptAiByID(row.deptId || 0);
  AISettingList.value = data.rows;
}

// 保存AI配置
async function saveAISetting() {
  const list = AISettingList.value;
  // 筛选出 status 为 true 的项
  let arr = list.filter((item: any) => item.status).map((item: any) => {
    return {
      ...item,
      'workspaceId': AISettingDept.value // 添加 workspaceId
    };
  });
  const { error } = await updateDeptAiItem(arr);
  if(!error){
    message.success('保存成功');
    handleAISettingMode(false);
  }
}

function handleAISettingMode(flag: boolean) {
  showAISettingModal.value = flag;
}

// 处理开关状态变化
async function handleSwitchChange(option: any, value: boolean) {
  const parpentDeptId = option.deptId;
  if (parpentDeptId === 100) {
    message.warning('该组织不能修改状态');
    return;
  }
  const mess = value ? '启用' : '停用';
  dialog.warning({
    title: '警告',
    content: `确定要${mess}${option.deptName} 的组织吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const { error } = await editDept({
        deptId: option.deptId,
        status: option.status,
        orderNum: option.orderNum,
        email: option.email,
        deptName: option.deptName,
        parentId: option.parentId
      });
      if (!error) {
        // 更新数据状态
        option.status = value ? '0' : '1'; // 根据开关状态更新 status 字段
        message.success(value ? '启用成功' : '停用成功');
        tableLoading.value = true;
        await handleQueryClick();
      }
    }
  });
}

function addByDeptConfirm() {
  formRef.value?.validate(errors => {
    if (errors) {
      return;
    }
    addOrUpdateByDept();
  })
}

async function addOrUpdateByDept() {
  const data = {
    ...infoModel.value
  }
  if (data.deptId != null) {
    const { error } = await editDept(data);
    if (!error) {
      message.success('修改成功');
    }
  } else {
    const { error } = await addDept(data);
    if (!error) {
      message.success('新增成功');
    }
  }
  tableLoading.value = true;
  showAddByDeptModal.value = false;
  await handleQueryClick();

}

function handleAddModal() {
  showAddByDeptModal.value = false;
}

async function handleQueryClick() {
  tableLoading.value = true;
  const { data: deptItem } = await fetchDeptList({
    deptName: formValue.deptName
  });
  const rootParentId = findRootParentId(deptItem)
  treeData.value = buildTree(deptItem, rootParentId);
  tableLoading.value = false;
}

function findRootParentId(data: Api.Dept.DeptItem[]) {
  const parentIds = data.map(item => item.parentId).filter(id => id !== null);
  return Math.min(...parentIds); // 找到最小的 parentId
}

function buildTree(data: Api.Dept.DeptItem[], parentId = 0): Api.Dept.DeptItem[] {
  return data
    .filter(item => item.parentId === parentId)
    .map(item => ({
      ...item,
      key: item.deptId,
      label: item.deptName,
      children: buildTree(data, item.deptId ?? 0)
    }));
}

onMounted(() => {
  handleQueryClick();
});
</script>

<template>
  <div>
    <NCard title="组织管理" :bordered="false">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :x-gap="12" :cols="3">
          <NGridItem>
            <NButton type="primary" attr-type="button" @click="addOrgOpen">新建组织</NButton>
          </NGridItem>
          <NGridItem :offset="1">
            <NFormItem label="组织名称：" path="name">
              <NInput v-model:value="formValue.deptName" placeholder="输入组织名称" clearable />
              <NButton attr-type="button" class="ml-10" @click="handleQueryClick">查询</NButton>
            </NFormItem>
          </NGridItem>
        </NGrid>
      </NForm>
      <NDataTable :columns="columns" :data="treeData" :default-expand-all="true" />
    </NCard>

    <NModal v-model:show="showAddByDeptModal">
      <NCard style="width: 600px" :title="updateOrAddTitle" :bordered="false" size="huge" role="dialog"
        aria-modal="true">
        <template #header-extra>
          <NIcon size="20" class="cursor-pointer" @click="handleAddModal">
            <Close />
          </NIcon>
        </template>
        <NForm ref="formRef" :model="infoModel" :rules="infoRules" require-mark-placement="right-hanging"
          label-placement="left" label-width="120px">
          <NFormItem v-show="infoModel.deptId !== 100" label="归属部门" path="parentId">
            <NTreeSelect filterable key-field="key" label-field="label" children-field="children"
              v-model:value="infoModel.parentId" :options="treeData" :default-value="infoModel.parentId" />
          </NFormItem>
          <NFormItem label="部门名称" path="deptName">
            <NInput v-model:value="infoModel.deptName" clearable />
          </NFormItem>
          <NFormItem label="显示顺序" path="orderNum">
            <NInputNumber v-model:value="infoModel.orderNum" clearable />
          </NFormItem>
          <NFormItem label="手机号码" path="phone">
            <NInput v-model:value="infoModel.phone" clearable />
          </NFormItem>
          <NFormItem label="负责人" path="leader">
            <NInput v-model:value="infoModel.leader" clearable />
          </NFormItem>
          <NFormItem label="邮箱" path="email">
            <NInput v-model:value="infoModel.email" clearable />
          </NFormItem>
          <NFormItem v-show="infoModel.deptId !== 100" label="状态" path="status">
            <NRadioGroup v-model:value="infoModel.status" name="status">
              <NSpace>
                <NRadio v-for="item in statusList" :key="item.id" :value="item.id">
                  {{ item.label }}
                </NRadio>
              </NSpace>
            </NRadioGroup>
          </NFormItem>
          <NFormItem label="有效期" path="validityDate">
            <NDatePicker v-model:formatted-value="infoModel.validityDate" value-format="yyyy-MM-dd" type="date" />
          </NFormItem>
        </NForm>
        <template #footer>
          <NFlex justify="end">
            <NButton @click="handleAddModal">取消</NButton>
            <NButton type="primary" @click="addByDeptConfirm">确定</NButton>
          </NFlex>
        </template>
      </NCard>
    </NModal>

    <NModal v-model:show="showAISettingModal">
      <NCard style="width: 600px" title="修改AI配置" :bordered="false" size="huge" role="dialog" aria-modal="true">
        <template #header-extra>
          <NIcon size="20" class="cursor-pointer" @click="handleAISettingMode(false)">
            <Close />
          </NIcon>
        </template>
        <div class="flex flex-col">
          <NGrid :cols="4" :x-gap="12" :y-gap="12">
            <NGridItem v-for="item in AISettingList" :key="item.id">
              <div class="flex items-center">
                <NCheckbox v-model:checked="item.status" />
                <span class="pl-5px">{{ item.name }}</span>
              </div>
            </NGridItem>
          </NGrid>
          <NButton @click="saveAISetting" type="primary" class="mt-4 ml-auto">保存</NButton>
        </div>
      </NCard>
    </NModal>

  </div>
</template>

<style scoped></style>
