<script setup lang="ts">
import { computed } from 'vue';
import type { VNode } from 'vue';
import { useAuthStore } from '@/store/modules/auth';
import { useRouterPush } from '@/hooks/common/router';
import { useSvgIcon } from '@/hooks/common/icon';
import { $t } from '@/locales';
import { RouteMap } from 'vue-router';

defineOptions({
  name: 'UserAvatar'
});

const authStore = useAuthStore();
const { routerPushByKey, toLogin } = useRouterPush();
const { SvgIconVNode } = useSvgIcon();

function loginOrRegister() {
  toLogin();
}

type DropdownKey = 'logout' | 'userCenter';

type DropdownOption =
  | {
      key: DropdownKey;
      label?: string;
      icon?: () => VNode;
    }
  | {
      type: 'divider';
      key: string;
    };

const options = computed(() => {
  const opts: DropdownOption[] = [
    {
      label: $t('common.userCenter'),
      key: 'userCenter',
      icon: SvgIconVNode({ icon: 'mdi:account-box', fontSize: 18 })
    },
    {
      label: $t('common.logout'),
      key: 'logout',
      icon: SvgIconVNode({ icon: 'ph:sign-out', fontSize: 18 })
    }
  ];

  return opts;
});

function logout() {
  window.$dialog?.info({
    title: $t('common.tip'),
    content: $t('common.logoutConfirm'),
    positiveText: $t('common.confirm'),
    negativeText: $t('common.cancel'),
    onPositiveClick: () => {
      authStore.resetStore();
    }
  });
}

function handleDropdown(key: DropdownKey) {
  if (key === 'logout') {
    logout();
  } else if (key === 'userCenter') {
    // 根据其他options进行路由跳转
    routerPushByKey('system_base'); // 明确类型转换
  }
}
</script>

<template>
  <!-- <NButton v-if="!authStore.isLogin" quaternary @click="loginOrRegister">
    {{ $t('page.login.common.loginOrRegister') }}
  </NButton> -->
  <NDropdown placement="bottom" trigger="click" :options="options" @select="handleDropdown">
    <div class="text-center">
      <ButtonIcon class="w-full">
        <SvgIcon icon="ph:user-circle" class="text-icon-large" />
        <!-- <span class="text-16px font-medium">{{ authStore.userInfo.userName }}</span> -->
      </ButtonIcon>
    </div>
  </NDropdown>
</template>

<style scoped></style>
