// 机场相关接口
import { LocationQueryValue } from 'vue-router';
import { request, pRequest } from '../request';

// 预览查询
export function getDetail(id: string | LocationQueryValue[]) {
    return request({ url: '/webjars/getDetail/' + id, method: 'get' });
}
// 获取列表
export function fileList(params: object) {
    return request({ url: '/webjars/fileList', method: 'get', params });
}
//获取oss配置
export function getOssConfig() {
    return request({ url: '/webjars/create', method: 'get' });
}

//上传完成
export function finish(data: object) {
    return request({ url: '/webjars/finish', method: 'post', data });
}

//封面
export function uploadUrl(data: object) {
    return request({ url: '/webjars/upload', method: 'post', data });
}


