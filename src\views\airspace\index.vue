<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import { NEmpty, NSpin, useDialog, useMessage } from 'naive-ui';
import dayjs from 'dayjs';
import type { UploadFileInfo, UploadOnFinish } from 'naive-ui';
import { ArrowDown20Regular, Delete24Regular } from '@vicons/fluent';
import { useAuthStore } from '@/store/modules/auth';
import { useRouterPush } from '@/hooks/common/router';

import { deleteAirLineByID,fetchAirLineList, fetchDownloadKMZ,analysisAirLine} from '@/service/api';

import {deptTree} from "@/service/api/user";


const serviceBaseURL = import.meta.env.VITE_SERVICE_BASE_URL;
const authStore = useAuthStore();
const { sysInfo, token } = authStore;

const { routerPushByKey } = useRouterPush();
const message = useMessage();

const airLineList = ref<Api.AirLine.AirLineItem[]>([]);
const treeData = ref([]);
const creatAirLineModalShow = ref(false);

const loading = ref(true);
const dialog = useDialog();

const formValue = reactive<{
  deptId: number | null;
  flightName: string;
  flightType: string;
  dateRange: [number, number] | null;
  regionCode: [];
  pageSize: number;
  pageNum: number;
  count: number;
}>({
  deptId: null,
  flightName: '',
  flightType: '',
  dateRange: null,
  regionCode: [],
  pageSize: 16,
  pageNum: 1,
  count: 0
});

const disablePreviousDate = (ts: number) => {
  return ts > Date.now();
};

// 航线类型
const generalAirportOptions: { label: string; value: string }[] = [
  '航点模式',
  '多边形测绘',
  '倾斜摄影',
  '环形测绘'
].map((v, index) => ({
  label: v,
  value: (index + 1).toString()
}));

function onClickEditAirLine(airlineItem: Api.AirLine.AirLineItem) {
  routerPushByKey('draw-airline', { query: { id: airlineItem.flightId?.toString() || '' } });
}

// 控制创建航线弹窗展示
const handelCreatAirLineModalShow = () => {
  creatAirLineModalShow.value = !creatAirLineModalShow.value;
};

// 添加上传按钮状态控制
const uploadDisabled = ref(false);

function beforeUpload(data: { file: UploadFileInfo; fileList: UploadFileInfo[] }) {
  console.log(data.file);
  if (data.file.name && data.file.name.toLowerCase().endsWith('.kmz')) {
    uploadDisabled.value = true; // 开始上传时禁用
    return true;
  }
  message.error('只能上传kmz格式的文件，请重新上传');
  return false;
}

async function handleFinish({ event }: { file: UploadFileInfo; event?: ProgressEvent }) {
  // console.log({ file, event });
  if (event && event.target) {
    try {
      const xhr = event.target as XMLHttpRequest;
      const target = JSON.parse(xhr.response);
      // console.log('handleFinish ~ target:', target.data.newFileName);

      // 解析航线
      const { error } = await analysisAirLine(target.data.newFileName);
      if (!error) {
        message.success('导入成功');
        await getAirLineList();
      }
    } catch (error) {
      message.error('导入失败');
    }
  } else {
    message.error('导入失败');
  }
  uploadDisabled.value = false;
  upload.value?.clear();
  return undefined; //
};

// 选择需要创建的航线种类 1-航点 2-多边形 3-倾斜 4-环形
const onSelectAirLineOptions = (type: number) => {
  switch (type) {
    case 1:
      routerPushByKey('draw-airline');
      break;

    default:
      message.info('该功能正在开发中...');
      break;
  }
};

async function rollAirLine() {
  const { flightName, flightType, dateRange, regionCode, pageSize, pageNum } = formValue;
  const json = {
    flightName,
    flightType,
    startDate: dateRange && dateRange[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : null,
    endDate: dateRange && dateRange[1] ? dayjs(dateRange[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null,
    regionCode,
    pageSize,
    pageNum
  };
  const { data, error } = await fetchAirLineList(json);
  if (!error) {
    formValue.count = data.total;
    if ((pageNum - 1) * pageSize < formValue.count) {
      airLineList.value.push(...data.rows);
    } else {
      formValue.pageNum = 1;
      airLineList.value = data.rows;
    }
  }
}

// 获取航线列表
const getAirLineList = async () => {
  loading.value = true;
  const { flightName, flightType, dateRange, regionCode, pageSize, deptId } = formValue;
  const json = {
    flightName,
    flightType,
    deptId,
    startDate: dateRange && dateRange[0] ? dayjs(dateRange[0]).format('YYYY-MM-DD') : null,
    endDate: dateRange && dateRange[1] ? dayjs(dateRange[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null,
    regionCode,
    pageSize,
    pageNum: 1
  };
  const { data, error } = await fetchAirLineList(json);
  if (!error) {
    formValue.count = data.total;
    airLineList.value = data.rows;
    for (let i = 0; i < airLineList.value.length; i++) {
      if (!airLineList.value[i].flightImage) {
        airLineList.value[i].flightImage = `${localStorage.getItem('newCoverBase64')}`;
        localStorage.removeItem('newCoverBase64');
        break;
      }
    }
  }
  loading.value = false;
};

const handleLoad = () => {
  const { pageSize, pageNum, count } = formValue;
  if (pageNum * pageSize < count) {
    formValue.pageNum += 1;
    rollAirLine();
  }
};

// 实现删除某条航线逻辑
function onDeleteAirLine(item: any) {
  dialog.warning({
    title: '确认删除',
    content: `确认删除 ${item.flightName} 航线？`,
    positiveText: '确认',
    negativeText: '取消',
    onPositiveClick: async () => {
      // 从 airLineList 中移除该项
      const { error } = await deleteAirLineByID(item.flightId);
      if (!error) {
        airLineList.value = airLineList.value.filter(line => line !== item);
        message.success('删除成功');
      }
    },
    onNegativeClick: () => {
      // 取消操作
    }
  });
}

// 实现下载航线逻辑
async function onDownloadAirLine(item: any) {
  const { data, error } = await fetchDownloadKMZ(item.flightId);
  if (!error && data.url) {
    const link = document.createElement('a');
    link.href = data.url;
    link.setAttribute('download', `${item.flightName}.kmz`);
    document.body.appendChild(link);
    link.click();
    link.remove();
    message.success('下载成功');
  } else {
    message.error('下载失败');
  }
}

// 查询
const handleQueryClick = () => {
  getAirLineList();
};

// 重置
const handleResetClick = () => {
  airLineList.value = [];
  formValue.deptId = null;
  formValue.flightName = '';
  formValue.flightType = '';
  formValue.dateRange = null;
  formValue.pageSize = 16;
  getAirLineList();
};

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
};

const upload = ref();

onMounted(() => {
  getDeptTree();
  getAirLineList();
});
</script>

<template>
  <div>
    <NCard :bordered="false" title="航线规划">
      <NForm ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <NGrid :cols="6" :x-gap="24">
          <NFormItemGi label="组织选择：" path="deptId">
            <NTreeSelect
              clearable
              v-model:value="formValue.deptId"
              filterable
              key-field="id"
              label-field="label"
              children-field="children"
              :options="treeData"
              :default-value="formValue.deptId"
            />
          </NFormItemGi>
          <NFormItemGi label="航线名称：" path="flightName">
            <NInput v-model:value="formValue.flightName" placeholder="输入航线名称" clearable />
          </NFormItemGi>
          <NFormItemGi label="航线类型：" path="flightType">
            <NSelect
              v-model:value="formValue.flightType"
              placeholder="请选择航线类型"
              clearable
              :options="generalAirportOptions"
            />
          </NFormItemGi>
          <NFormItemGi :span="2" label="日期范围：" path="dateRange">
            <NDatePicker
              v-model:value="formValue.dateRange"
              type="daterange"
              :is-date-disabled="disablePreviousDate"
              clearable
            />
          </NFormItemGi>
          <NFormItemGi>
            <NButton attr-type="button" class="mr2" @click="handleResetClick">重置</NButton>
            <NButton type="primary" attr-type="button" @click="handleQueryClick">查询</NButton>
          </NFormItemGi>
          <NFormItemGi :span="8">
            <NButton type="primary" attr-type="button" class="mr2" @click="handelCreatAirLineModalShow">
              创建航线
            </NButton>

            <NUpload
              ref="upload"
              :action="serviceBaseURL + '/common/upload'"
              :headers="{ Authorization: token }"
              :max="1"
              :show-file-list="false"
              :disabled="uploadDisabled"
              @finish="handleFinish"
              @before-upload="beforeUpload"
            >
              <NButton type="primary" attr-type="button" class="mr2" :disabled="uploadDisabled">导入航线</NButton>
            </NUpload>
          </NFormItemGi>
        </NGrid>
      </NForm>
      <NDivider />

      <NSpin :show="loading">
        <NInfiniteScroll style="height: 68vh" :distance="1" @load="handleLoad">
          <NEmpty v-if="!loading && airLineList.length === 0" description="暂无航线数据" size="large">
            <template #extra>
              <NButton size="small" @click="handleQueryClick">重新加载</NButton>
            </template>
          </NEmpty>
          <NGrid v-else x-gap="50" y-gap="20" cols="600:2 900:3 1200:4">
            <NGi v-for="(i, index) in airLineList" :key="index" class="group relative">
              <NCard class="relative p-5" @click="onClickEditAirLine(i)">
                <div class="flex">
                  <NImage preview-disabled :src="i.flightImage" class="h-32 w-45%" />
                  <div class="w-50% flex-col justify-between pl-20px">
                    <div class="pb-10px font-size-18px font-bold">
                      <NTooltip trigger="hover">
                        <template #trigger>
                          <h3 class="w-98% truncate">{{ i.flightName }}</h3>
                        </template>
                        {{ i.flightName }}
                      </NTooltip>
                    </div>
                    <div>
                      <h4>
                        {{
                          i.flightType === 1
                            ? '航点航线'
                            : i.flightType === 2
                              ? '多边形测绘'
                              : i.flightType === 3
                                ? '倾斜摄影'
                                : '环形测绘'
                        }}
                      </h4>
                      <h4>{{ i.createName }}</h4>
                      <h4>{{i.workspaceName}}</h4>
                      <h4>{{ i.createTime }}</h4>
                    </div>
                  </div>
                </div>
                <NButton
                  circle
                  class="absolute right-5 top-5 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                  @click.stop="onDeleteAirLine(i)"
                >
                  <template #icon>
                    <NIcon>
                      <Delete24Regular />
                    </NIcon>
                  </template>
                </NButton>
                <NButton
                  circle
                  class="absolute right-5 top-15 opacity-0 transition-opacity duration-300 group-hover:opacity-100"
                  @click.stop="onDownloadAirLine(i)"
                >
                  <template #icon>
                    <NIcon>
                      <ArrowDown20Regular />
                    </NIcon>
                  </template>
                </NButton>
              </NCard>
            </NGi>
          </NGrid>
        </NInfiniteScroll>
      </NSpin>
    </NCard>

    <!-- 创建航线弹窗 -->
    <NModal :show="creatAirLineModalShow">
      <NCard title="创建航线" class="w-max" closable @close="handelCreatAirLineModalShow">
        <NFlex justify="space-between">
          <NCard class="w-max" hoverable @click="onSelectAirLineOptions(1)">
            <NFlex vertical align="center">
              <SvgIcon class="size-80px" icon="mdi:chart-timeline-variant" />
              <NText class="text-18px font-700">航点模式</NText>
              <NText>定义多个航点确定飞行路径</NText>
            </NFlex>
          </NCard>
          <NCard class="w-max" hoverable @click="onSelectAirLineOptions(2)">
            <NFlex vertical align="center">
              <SvgIcon class="size-80px" icon="mdi:vector-square-close" />
              <NText class="text-18px font-700">多边形测绘</NText>
              <NText>在多边形区域内自动生成</NText>
            </NFlex>
          </NCard>
          <NCard class="w-max" hoverable @click="onSelectAirLineOptions(3)">
            <NFlex vertical align="center">
              <SvgIcon class="size-80px" icon="mdi:slope-uphill" />
              <NText class="text-18px font-700">倾斜摄影</NText>
              <NText>5向航线规划飞行路线，适合三维</NText>
            </NFlex>
          </NCard>
          <NCard class="w-max" hoverable @click="onSelectAirLineOptions(4)">
            <NFlex vertical align="center">
              <SvgIcon class="size-80px" icon="mdi:vector-circle" />
              <NText class="text-18px font-700">环形测绘</NText>
              <NText>环绕一个点进行拍摄，适合高楼</NText>
            </NFlex>
          </NCard>
        </NFlex>
      </NCard>
    </NModal>
  </div>
</template>

<style scoped>
.n-spin-container {
  min-height: 200px;
}

.n-empty {
  margin: 32px 0;
}
</style>
