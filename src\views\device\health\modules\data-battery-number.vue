<script setup lang="ts">
import { fetchDroneBatteryList } from '@/service/api';
import { useEcharts } from '@/hooks/common/echarts';

defineOptions({
  name: 'DataBatteryNumber'
});

const titleData = ['健康', '警告', '待报废'];

const { domRef, updateOptions } = useEcharts(() => ({
  tooltip: {
    trigger: 'item'
  },
  xAxis: {},
  yAxis: {
    type: 'category',
    data: titleData
  },
  series: [
    {
      type: 'bar',
      itemStyle: {
        color(params) {
          const colors = ['#0dca8c', '#fedc69', '#fb7e7e'];
          return colors[params.dataIndex];
        }
      },
      data: [0, 0, 0],
      label: {
        show: true,
        position: 'right',
        fontSize: 14,
        fontWeight: 'bold',
        formatter(params) {
          return `${params.value} 块`;
        }
      }
    }
  ]
}));

async function mockData() {
  await new Promise(resolve => {
    setTimeout(resolve, 1000);
  });

  const { error, data } = await fetchDroneBatteryList(0);
  // 健康，告警，待报废
  const numberData = [0, 0, 0];
  if (!error) {
    data.rows.forEach(item => {
      if (item.loopTimes >= 400) {
        numberData[2] += 1;
      } else if (item.loopTimes >= 300) {
        numberData[1] += 1;
      } else {
        numberData[0] += 1;
      }
    });
  }
  await updateOptions(opts => {
    opts.series[0].data = numberData;
    return opts;
  });
}

async function init() {
  await mockData();
}

init();
</script>

<template>
  <div ref="domRef" class="h-240px overflow-hidden" />
</template>

<style scoped></style>
