import * as Cesium from 'cesium';

export default class IsoscelesTriangleCreator {
    private viewer: Cesium.Viewer;
    private lastEntity: Cesium.Entity[] = [];
    private allEntity: Cesium.Entity[][] = [];
    private currentApex: Cesium.Cartesian3 | undefined;
    private currentMedianLength: number | undefined;
    private defaultColor = Cesium.Color.BLUE.withAlpha(0.3); // 设置默认透明颜色
    private currentRotation: number = 0; // 当前旋转角度
    protected currentApexDegreeX: number = 30; // 当前偏航角度
    protected currentApexDegreeY: number = 30; // 当前俯仰角度

    constructor(viewer: Cesium.Viewer) {
        this.viewer = viewer;
    }

    /**
     * 创建四棱锥实体
     * @param apex 顶点坐标（世界坐标系）
     * @param height 顶点到底面的距离（米）
     * @param baseDegreeX 底面沿X方向的角度
     * @param baseDegreeY 底面沿Y方向的角度
     */
    create(apex: Cesium.Cartesian3, height: number, baseDegreeX: number = 30, baseDegreeY: number = 30): void {
        console.log("apex", apex, "height", height, "baseDegreeX", baseDegreeX, "baseDegreeY", baseDegreeY);
        this.currentApex = apex;
        this.currentMedianLength = height; // 顶点到底面的距离
        this.currentApexDegreeY = baseDegreeY; // 俯仰角度数
        this.currentRotation = 0; // 默认不旋转
        this.currentApexDegreeX = baseDegreeX; // 偏航角度数
        const positions = this.calculateBasePositions(apex, height, baseDegreeX, baseDegreeY);
        // positions.forEach((position, index) => {
        //     const cartographic = Cesium.Cartographic.fromCartesian(position);
        //     const lon = Cesium.Math.toDegrees(cartographic.longitude);
        //     const lat = Cesium.Math.toDegrees(cartographic.latitude);
        //     const height = cartographic.height;
        //     console.log(`Position ${index}: Longitude: ${lon}, Latitude: ${lat}, Height: ${height}`);
        // });

        if (this.lastEntity.length > 0) {
            // 如果实体已存在，直接更新颜色和位置
            this.update(apex);
            // this.lastEntity.polygon!.material = new Cesium.ColorMaterialProperty(this.defaultColor);
        } else {
            this.lastEntity = this.createPolygonEntity(positions);
            this.allEntity.push(this.lastEntity);
        }

        // setTimeout(() => {
        //     this.clearPreviousEntity();
        // }, 100); // 延迟 100 毫秒
    }

    /**
     * 更新棱锥位置
     * @param apex 新的顶点坐标（世界坐标系）
     */
    update(apex: Cesium.Cartesian3): void {
        if (!this.currentMedianLength) {
            console.log("请先调用 create 方法创建三角形");
            return;
        }
        this.currentApex = apex;
        const positions = this.calculateBasePositions(apex, this.currentMedianLength, this.currentApexDegreeX, this.currentApexDegreeY); // Default baseDegreeY value
        if (this.lastEntity.length > 0) {
            // 清除之前的实体
            // this.viewer.entities.remove(this.lastEntity);
            // 重新创建棱锥
            this.lastEntity = this.createPolygonEntity(positions);
            this.allEntity.push(this.lastEntity);
        }
    }
    /**
     * 俯仰角旋转
     * @param pitch 俯仰角度（-90 到 90 度）
     */
    rotateY(pitch: number): void {
        if (!this.currentApex || !this.currentMedianLength) {
            console.warn("请先调用 create 方法创建三角形");
            return;
        }

        if (pitch < -90 || pitch > 90) {
            console.warn("俯仰角必须在 -90 到 90 度之间");
            return;
        }

        this.currentApexDegreeX = pitch;

        // 重新计算旋转后的顶点位置
        const positions = this.calculateBasePositionsWithRotation(
            this.currentApex,
            this.currentMedianLength,
            pitch,
            this.currentRotation
        );

        if (this.lastEntity) {
            // 更新实体的顶点坐标
            if (this.lastEntity[0]?.polygon) {
                this.lastEntity[0].polygon.hierarchy = new Cesium.CallbackProperty(() => new Cesium.PolygonHierarchy(positions), false);
            }
        }
    }
    /**
     * 偏航角旋转
     * @param yaw 偏航角度（0 到 360 度）
     */
    rotateX(yaw: number): void {
        if (!this.currentApex || !this.currentMedianLength || this.currentApexDegreeX === undefined) {
            console.warn("请先调用 create 方法创建三角形");
            return;
        }
        if (yaw < 0 || yaw > 360) {
            console.warn("偏航角必须在 0 到 360 度之间");
            return;
        }
        this.currentRotation = yaw;

        // 重新计算旋转后的顶点位置
        const positions = this.calculateBasePositionsWithRotation(
            this.currentApex,
            this.currentMedianLength,
            this.currentApexDegreeX,
            -yaw
        );

        if (this.lastEntity.length > 0) {
            const basePositions = positions.slice(1); // 底面四个点

            // 更新底面多边形
            if (this.lastEntity[0]?.polygon) {
                this.lastEntity[0].polygon.hierarchy = new Cesium.CallbackProperty(
                    () => new Cesium.PolygonHierarchy(basePositions),
                    false
                );
            }

            // 更新侧面多边形和边
            for (let i = 0; i < basePositions.length; i++) {
                const nextIndex = (i + 1) % basePositions.length;
                const sidePositions = [positions[0], basePositions[i], basePositions[nextIndex]];

                // 更新侧面多边形
                if (this.lastEntity[i + 1]?.polygon) {
                    this.lastEntity[i + 1].polygon!.hierarchy = new Cesium.CallbackProperty(
                        () => new Cesium.PolygonHierarchy(sidePositions),
                        false
                    );
                }

                // 更新顶点到底面垂直线
                if (this.lastEntity[basePositions.length + i]?.polyline) {
                    this.lastEntity[basePositions.length + i].polyline!.positions = new Cesium.CallbackProperty(
                        () => [positions[0], basePositions[i]],
                        false
                    );
                }

                // 更新底面相邻点连线
                if (this.lastEntity[basePositions.length * 2 + i]?.polyline) {
                    this.lastEntity[basePositions.length * 2 + i].polyline!.positions = new Cesium.CallbackProperty(
                        () => [basePositions[i], basePositions[nextIndex]],
                        false
                    );
                }
            }
        }
    }
    /**
     * 清除单个实体
     */
    clearLastEntity(arrEntity: Cesium.Entity[]): void {
        if (arrEntity.length > 0) {
            while (arrEntity.length > 0) {
                const entity = arrEntity.shift();
                if (entity) {
                    this.viewer.entities.remove(entity);
                }
            }
        }
        this.allEntity.pop();
    }

    /**
     * 清除所有创建的实体
     */
    clearAllEntities(): void {
        while (this.allEntity.length > 0) {
            const entityArr = this.allEntity.shift();
            if (entityArr) {
                // this.viewer.entities.remove(entity);
                this.clearLastEntity(entityArr)
            }
        }
        this.lastEntity = [];
    }

    /**
     * 计算四棱锥底面四个顶点坐标
     */
    private calculateBasePositions(
        apex: Cesium.Cartesian3,
        height: number,
        baseDegreeX: number,
        baseDegreeY: number
    ): Cesium.Cartesian3[] {
        const cartographic = Cesium.Cartographic.fromCartesian(apex);
        const lon = Cesium.Math.toDegrees(cartographic.longitude);
        const lat = Cesium.Math.toDegrees(cartographic.latitude);

        // 计算底面中心点坐标
        const earthRadius = 6378137; // WGS84 椭球长半轴
        const deltaLat = (height / earthRadius) * (180 / Math.PI); // 纬度偏移量
        const baseCenterLat = lat + deltaLat; // 向北偏移
        const baseCenterLon = lon;

        // 计算底面四个顶点的偏移量
        const baseHalfWidthX = height * Math.tan(Cesium.Math.toRadians(baseDegreeX / 2));
        const baseHalfWidthY = height * Math.tan(Cesium.Math.toRadians(baseDegreeY / 2));
        const deltaLonX = (baseHalfWidthX / (earthRadius * Math.cos(Cesium.Math.toRadians(baseCenterLat)))) * (180 / Math.PI);
        const deltaLatY = (baseHalfWidthY / earthRadius) * (180 / Math.PI);

        // 生成底面四个顶点坐标，高度根据 baseDegreeY 的度数计算
        const baseHeightOffset = height * Math.tan(Cesium.Math.toRadians(baseDegreeY / 2));
        const apexHeight = cartographic.height; // 获取 apex 的高度
        return [
            apex,
            Cesium.Cartesian3.fromDegrees(baseCenterLon + deltaLonX, baseCenterLat + deltaLatY, apexHeight + baseHeightOffset), // 高度由 apex 决定
            Cesium.Cartesian3.fromDegrees(baseCenterLon - deltaLonX, baseCenterLat + deltaLatY, apexHeight + baseHeightOffset), // 高度由 apex 决定
            Cesium.Cartesian3.fromDegrees(baseCenterLon - deltaLonX, baseCenterLat + deltaLatY, apexHeight - baseHeightOffset), // 高度由 apex 决定
            Cesium.Cartesian3.fromDegrees(baseCenterLon + deltaLonX, baseCenterLat + deltaLatY, apexHeight - baseHeightOffset)  // 高度由 apex 决定
        ];
    }
    /**
     * 计算旋转后的三角形顶点坐标
     */
    private calculateBasePositionsWithRotation(
        apex: Cesium.Cartesian3,
        medianLength: number,
        apexDegree: number,
        rotation: number
    ): Cesium.Cartesian3[] {
        const positions = this.calculateBasePositions(apex, medianLength, apexDegree, 30); // Default baseDegreeY value

        // 计算旋转后的坐标
        const rotationRadians = Cesium.Math.toRadians(rotation);
        const center = positions[0]; // 顶点作为旋转中心

        return positions.map((position) => {
            if (position === center) return position; // 顶点不需要旋转

            const cartographic = Cesium.Cartographic.fromCartesian(position);
            const centerCartographic = Cesium.Cartographic.fromCartesian(center);

            const lon = Cesium.Math.toDegrees(cartographic.longitude);
            const lat = Cesium.Math.toDegrees(cartographic.latitude);
            const centerLon = Cesium.Math.toDegrees(centerCartographic.longitude);
            const centerLat = Cesium.Math.toDegrees(centerCartographic.latitude);

            // 计算相对中心的偏移量
            const deltaLon = lon - centerLon;
            const deltaLat = lat - centerLat;

            // 应用旋转矩阵
            const rotatedLon = centerLon + deltaLon * Math.cos(rotationRadians) - deltaLat * Math.sin(rotationRadians);
            const rotatedLat = centerLat + deltaLon * Math.sin(rotationRadians) + deltaLat * Math.cos(rotationRadians);

            return Cesium.Cartesian3.fromDegrees(rotatedLon, rotatedLat, cartographic.height);
        });
    }

    /**
     * 创建多边形实体并绘制棱锥的所有面和所有边
     */
    private createPolygonEntity(positions: Cesium.Cartesian3[]): Cesium.Entity[] {
        const faceColor = Cesium.Color.GREEN.withAlpha(0.5); // 设置稍微深的绿色
        const edgeColor = Cesium.Color.LIGHTGREEN.withAlpha(0.8); // 偏白的绿色
        const dashedMaterial = new Cesium.PolylineDashMaterialProperty({
            color: edgeColor,
            dashLength: 16.0
        });
        const oneEntity: Cesium.Entity[] = [];
        // 绘制底面
        const basePositions = positions.slice(1); // 底面四个点
        const baseEntity = this.viewer.entities.add({
            polygon: {
                hierarchy: new Cesium.PolygonHierarchy(basePositions),
                material: faceColor,
                perPositionHeight: true
            }
        });
        oneEntity.push(baseEntity)
        // 绘制棱锥的侧面
        for (let i = 0; i < basePositions.length; i++) {
            const nextIndex = (i + 1) % basePositions.length;
            const sidePositions = [positions[0], basePositions[i], basePositions[nextIndex]]; // 顶点和底面两个相邻点
            const areaEntity = this.viewer.entities.add({
                polygon: {
                    hierarchy: new Cesium.PolygonHierarchy(sidePositions),
                    material: faceColor, // 使用稍微深的绿色
                    perPositionHeight: true
                }
            });
            oneEntity.push(areaEntity)
        }

        // 绘制所有边（不包括底面对角线）
        for (let i = 0; i < basePositions.length; i++) {
            // 顶点到底面边
            // const lineEntity1 = this.viewer.entities.add({
            //     polyline: {
            //         positions: [positions[0], basePositions[i]],
            //         width: 1, // 线条变细
            //         material: dashedMaterial // 使用虚线
            //     }
            // });
            // oneEntity.push(lineEntity1)
            // 底面相邻点连线
            const nextIndex = (i + 1) % basePositions.length;
            const lineEntity2 = this.viewer.entities.add({
                polyline: {
                    positions: [basePositions[i], basePositions[nextIndex]],
                    width: 1, // 线条变细
                    material: edgeColor // 偏白的绿色
                }
            });
            oneEntity.push(lineEntity2)
        }

        return oneEntity;
    }

    /**
     * 销毁类实例时自动清理实体
     */
    destroy(): void {
        this.clearAllEntities();
    }
}