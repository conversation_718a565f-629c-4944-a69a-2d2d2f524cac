<script setup lang="ts">
import { computed, onMounted, ref } from 'vue';
import { useAppStore } from '@/store/modules/app';
import { $t } from '@/locales';
import { fetchIndexOrg } from '@/service/api';

defineOptions({
  name: 'OrganizationDetail'
});

const appStore = useAppStore();

const gap = computed(() => (appStore.isMobile ? 0 : 16));

const indexOrg = ref<Api.Home.IndexOrg>({
  aiCount: 0,
  dockCount: 0,
  flightCount: 0,
  flightLineCount: 0,
  onlineDevice: 0,
  resultCount: 0,
  userCount: 0,
  droneCount: 0
});

const getIndexOrg = async () => {
  const res = await fetchIndexOrg();
  // const data = res.data;
  indexOrg.value = res.data;
  // console.log("system ~ baseURL:", res)
}

onMounted(() => {
  getIndexOrg();
})

</script>

<template>
  <NCard title="组织详情" :bordered="false">
    <NGrid :x-gap="gap" :y-gap="2" responsive="screen" item-responsive>
      <NGi span="3">
        <n-card title="在线设备" class="bg-dark-600" size="small" hoverable>
          <span class="text-6">{{ indexOrg.onlineDevice }}</span>台
          <template #footer><router-link to="/flight" class="cursor-pointer text-#026de2">查看设备</router-link></template>
        </n-card>
      </NGi>
      <NGi span="3">
        <n-card title="成员数量" class="bg-dark-600" size="small" hoverable>
          <span class="text-6">{{ indexOrg.userCount }}</span>人
          <template #footer><router-link to="/system/organization" class="cursor-pointer text-#026de2">管理组织</router-link></template>
        </n-card>
      </NGi>
      <NGi span="3">
        <n-card title="无人机数量" class="bg-dark-600" size="small" hoverable>
          <span class="text-6">{{ indexOrg.droneCount }}</span>架
          <template #footer><router-link to="/device/rc" class="cursor-pointer text-#026de2">管理设备</router-link></template>
        </n-card>
      </NGi>
      <NGi span="3">
        <n-card title="机场数量" class="bg-dark-600" size="small" hoverable>
          <span class="text-6">{{ indexOrg.dockCount }}</span>台
          <template #footer><router-link to="/device/airport" class="cursor-pointer text-#026de2">管理机场</router-link></template>
        </n-card>
      </NGi>
      <NGi span="3">
        <n-card title="航线数量" class="bg-dark-600" size="small" hoverable>
          <span class="text-6">{{ indexOrg.flightLineCount }}</span>条
          <template #footer><router-link to="/airspace" class="cursor-pointer text-#026de2">管理航线</router-link></template>
        </n-card>
      </NGi>
      <NGi span="3">
        <n-card title="飞行次数" class="bg-dark-600" size="small" hoverable>
          <span class="text-6">{{ indexOrg.flightCount }}</span>次
          <template #footer><router-link to="/history" class="cursor-pointer text-#026de2">飞行历史</router-link></template>
        </n-card>
      </NGi>
      <NGi span="3">
        <n-card title="成果数量" class="bg-dark-600" size="small" hoverable>
          <span class="text-6">{{ indexOrg.resultCount }}</span>个
          <template #footer><router-link to="/achievement/material" class="cursor-pointer text-#026de2">查看成果</router-link></template>
        </n-card>
      </NGi>
      <NGi span="3">
        <n-card title="AI能力" class="bg-dark-600" size="small" hoverable>
          <span class="text-6">{{ indexOrg.aiCount }}</span>种
          <template #footer><router-link to="/senior/overview" class="cursor-pointer text-#026de2">AI配置</router-link></template>
        </n-card>
      </NGi>
    </NGrid>
  </NCard>
</template>

<style scoped></style>
