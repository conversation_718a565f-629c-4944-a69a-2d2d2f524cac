<script lang="ts" setup>
defineOptions({ name: 'SystemLogo' });
import { useAuthStore } from '@/store/modules/auth';
const authStore = useAuthStore();
const logoUrl = authStore?.sysInfo?.webConfig?.webLogoUrl;
</script>

<template>
  <template v-if="logoUrl">
    <n-image :src="logoUrl" id="svg_1" />
  </template>
  <template v-else>
    <icon-local-logo />
  </template>
</template>

<style>
.loginlogo #svg_1 {
  width: 64px;
  height: 64px;
}

.menubox #svg_1 {
  width: 32px;
  max-height: 32px;
}
</style>
