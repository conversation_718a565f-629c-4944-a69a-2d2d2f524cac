<template>
  <div class=" color-light absolute left-[calc(50%+5vw)] bottom-1">
    <div v-show="boxStatus" class="flex w-max">
      <div class="relative h-max">
        <div class="absolute right--1 top-1 translate-x-1/1">
          <div class="mb-5px text-left">
            <n-text class="color-light pr-5px fw-bold">{{ props.targetPointVlaue.nickname }}
            </n-text>
            <n-tag class="mr-10px" size="small" :type="droneStatus?.listClass || 'default'">
              {{ droneStatus?.dictLabel || '离线' }}
            </n-tag>
          </div>
          <div class="flex justify-between pt-5px ml--5px">
            <div>
              <n-tag :bordered="false" size="small"
                :color="{ color: '#272727', textColor: '#555', borderColor: '#555' }">
                <div class="flex items-center text-light">
                  <SvgIcon icon="mdi:satellite-variant" class="text-icon text-#fff cursor-pointer" />
                  <n-text class="px-5px text-light">{{ props.targetPointVlaue.positionStateGpsNumber }}</n-text>
                  <SvgIcon icon="mdi:signal-cellular-outline" class="text-icon mx-5px text-#fff cursor-pointer" />
                  <n-text class="pr-5px text-light"> RTK {{ props.targetPointVlaue.positionStateRtkNumber }}
                    FIX</n-text>
                </div>
              </n-tag>
            </div>
            <div class="w-max text-right">
              <n-button :disabled="!(airportDockItem.host?.modeCode === '0')" @click="onClickToTakeOff" size="small"
                type="warning" class="mr-10px">一键起飞 </n-button>
              <n-button v-show="currentScreenStatus != 2"
                :disabled="!(airportDockItem.host?.modeCode === '0' || airportDockItem.host?.modeCode === '2')"
                @click="handelRemoteDebug" :secondary="debugBoxStatus" size="small" type="info">
                {{ debugBoxStatus ? '关闭调试' : '远程调试' }}
              </n-button>
            </div>
          </div>
        </div>

        <!-- 视频窗口 -->
        <div>
          <div v-if="currentScreenStatus === 2"
            class="text-light relative lh-234px h-234px w-416px text-center bg-dark-theme">正在大窗播放</div>
          <div v-if="videoUrl === ''" class="text-light relative lh-234px h-234px w-416px text-center">
            暂无视频
          </div>
          <div id='video-container' :class="[
            'bg-dark-theme overflow-hidden',
            currentScreenStatus === 2
              ? 'fixed top-55px left-95px h-720px w-1280px z-100'
              : 'h-234px w-416px aspect-video relative'
          ]" v-if="videoUrl !== ''">
            <video id="video-player" ref="videoRef" :src="videoUrl" class="w-full h-full object-contain" muted controls
              @timeupdate="onTimeUpdate" @seeking="onSeeking" @seeked="onSeeked" @pause="handlePause(true)"
              @play="handlePause(false)" playsinline webkit-playsinline x5-playsinline preload="auto"></video>
            <div v-if="currentScreenStatus === 2"
              class="overflow-hidden fixed top-55px left-95px h-720px w-1280px z-100">
              <div class="p-5px bg-black absolute right-0 bottom-0 cursor-pointer" @click="handelVideoScreen">
                <SvgIcon class="cursor-pointer text-lg text-gray hover:text-gray-300 transition-colors duration-200"
                  size="24" icon="mdi:arrow-bottom-right-thick" />
              </div>
              <div class="p-5px zindex-2 bg-black bg-opacity-50 top-0 right-0 w-max absolute">
                <p>{{ props.targetPointVlaue.date ? (props.targetPointVlaue.date + '').split(' ')[0] : '2025-01-01' }}
                </p>
                <p>{{ props.targetPointVlaue.date ? (props.targetPointVlaue.date + '').split(' ')[1] : '00:00:00' }}</p>
              </div>
            </div>

            <div v-if="currentScreenStatus === 1">
              <div class="p-5px bg-black absolute zindex-2 bg-opacity-50 left-0 top-0 cursor-pointer"
                @click="handelVideoScreen">
                <SvgIcon class="cursor-pointer text-lg text-gray hover:text-gray-300 transition-colors duration-200"
                  size="24" icon="mdi:arrow-top-left-thick" />
              </div>
              <div class="p-5px zindex-2 bg-black bg-opacity-50 top-0 right-0 w-max absolute">
                <p>{{ props.targetPointVlaue.date ? (props.targetPointVlaue.date + '').split(' ')[0] : '2025-01-01' }}
                </p>
                <p>{{ props.targetPointVlaue.date ? (props.targetPointVlaue.date + '').split(' ')[1] : '00:00:00' }}</p>
              </div>
            </div>
          </div>

        </div>

        <!-- 环境数据 -->
        <div class="absolute w-90% right--2 bottom-0 translate-x-1/1">

          <div class="pb-20px w-100%">
            <div class="flex justify-between-left">
              <n-text class="text-4 text-light lh-30px font-mono pr-80px">纬度： {{ Number(props.targetPointVlaue.latitude
                ||
                0).toFixed(6) }}</n-text>
              <n-text class="text-4 text-light lh-30px font-mono pl--5px">经度：{{ Number(props.targetPointVlaue.longitude
                ||
                0).toFixed(6) }}</n-text>
            </div>
            <div class="flex justify-between-left">
              <n-text class="text-4 text-light lh-30px font-mono pr-15px">海拔高度：{{ Number(props.targetPointVlaue.height
                ||
                0).toFixed(1) }}（m）</n-text>
            </div>
            <div class="flex justify-between-left">
              <n-text class="text-4 text-light lh-30px font-mono pr-15px">相对高度：{{ props.targetPointVlaue.elevation
              }}（m）</n-text>
              <n-text class="text-4 text-light lh-30px font-mono pl-5px">直线距离： {{
                Number(props.targetPointVlaue.homeDistance
                  ||
                  0).toFixed(1) }}（m）</n-text>
            </div>
            <div class="flex justify-between-left">
              <n-text class="text-4 text-light lh-30px font-mono pr-15px">风速： {{ props.targetPointVlaue.windSpeed
              }}（m/s）</n-text>
              <n-text class="text-4 text-light lh-30px font-mono">升降速度：{{ props.targetPointVlaue.verticalSpeed
              }}（m/s）</n-text>
            </div>
          </div>
          <n-flex justify="space-between" class="w-100%">
            <n-text class="color-light ">地面风速： <n-text class="color-light font-mono text-14px">{{
              props.targetPointVlaue.dockWindSpeed || 0.0
                }} m/s</n-text></n-text>
            <n-text class="color-light">地面雨量： <n-text class="color-light font-mono text-14px">
                {{ props.targetPointVlaue.dockRainfall === '1' ? '小' : props.targetPointVlaue.dockRainfall === '2' ? '中'
                  :
                  props.targetPointVlaue.dockRainfall === '3' ? '大' : '无' }}
              </n-text></n-text>
            <n-text class="color-light">舱内温度： <n-text class="color-light font-mono text-14px ">
                {{ props.targetPointVlaue.dockTemperature || 24 }}℃</n-text></n-text>
          </n-flex>

        </div>

        <!-- 远程调试菜单 -->
        <div v-show="debugBoxStatus" class="fixed bottom-65 right-1 bg-dark-theme w-max">
          <div class="flex-y-center justify-between p-2  ">
            <n-text class="color-light text-4">远程调试 </n-text>
            <n-icon @click="handelRemoteDebug" class="cursor-pointer" size="20" color="#ffffff" :component="Close" />
          </div>
          <n-config-provider :theme="darkTheme">
            <div class="flex justify-around m-10px ">
              <!-- 机场 -->
              <div class="flex-1 p-15px bg-dark-3 rd mr-10px">
                <div class="flex-y-center justify-between">
                  <n-text>机场</n-text>
                  <n-button size="small" @click="updateStatus('dock', 'device_reboot', { 'action': 0 })">重启</n-button>
                  <n-button size="small" @click="updateStatus('dock', 'device_format', { 'action': 0 })">格式化</n-button>
                </div>
                <div class="mt-10px">
                  <div v-for="(item, index) in debugMenu.dock" :key="index"
                    class="flex-y-center justify-between m-y-5px bg-dark-1 p-x-2 p-y-5px rd">
                    <!-- <div v-if="item.name == '空调' && Number(airportDockItem.host?.airConditionerSwitchTime) > 5">
                    <n-text class="w-max pr-5px text-3">{{ item.name }}</n-text>
                  </div> -->
                    <div>
                      <n-text class="w-max pr-5px text-3">{{ item.name }}</n-text>
                    </div>

                    <span class="w-max " style="display:flex">

                      <n-text v-if="Number(airportDockItem.host?.airConditionerSwitchTime) > 5 && item.name == '空调'"
                        class="pr-5px text-3">{{ airportDockItem.host?.airConditionerSwitchTime }}</n-text>
                      <n-radio-group v-if="item.list.length <= 2"
                        @update:value="($event: string) => onChangeDebugItem('dock', $event, index)" :size="'small'"
                        v-model:value="item.status" name="radiobuttongroup1" class="w-max">
                        <n-radio-button v-for="litem in item.list" :key="litem.path" :value="litem.value"
                          :label="litem.label" />
                      </n-radio-group>
                      <n-select v-else @update:value="($event: string) => onChangeDebugItem('dock', $event, index)"
                        :disabled="item.name == '空调' && airCouint > 5 ? true : false" v-model:value="item.status"
                        :options="item.list" size="tiny" :consistent-menu-width="false" />
                    </span>
                  </div>
                </div>
              </div>
              <!-- 无人机 -->
              <div class="flex-1 p-15px bg-dark-2 rd">
                <div class="flex-y-center justify-between">
                  <n-text>飞行器</n-text>
                  <n-button size="small" @click="updateStatus('drone', 'drone_format', { 'action': 0 })">格式化</n-button>
                </div>
                <div class="mt-15px">
                  <div v-for="(item, index) in debugMenu.drone" :key="index"
                    class="flex-y-center justify-between m-y-5px bg-dark-1 p-x-10px p-y-5px rd">
                    <n-text class="w-max pr-10px text-3">{{ item.name }}</n-text>
                    <span class="w-max">
                      <n-radio-group v-if="item.list.length <= 2"
                        @update:value="($event: string) => onChangeDebugItem('drone', $event, index)" :size="'small'"
                        v-model:value="item.status" name="radiobuttongroup1">
                        <n-radio-button v-for="litem in item.list" :key="litem.path" :value="litem.value"
                          :label="litem.label" />
                      </n-radio-group>
                      <n-select v-else @update:value="($event: string) => onChangeDebugItem('drone', $event, index)"
                        v-model:value="item.status" :options="item.list" size="tiny" class="w-100px" />
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </n-config-provider>

        </div>
      </div>

      <div v-show="!boxStatus" @click="handelBoxChange"
        class="px-15px py-10px  border-rd bg-#1a1b1c text-center cursor-pointer ">
        <SvgIcon icon="mdi:quadcopter" class="text-32px  text-#fff mb-5px " />
        <n-text class="text-14px text-light">详情</n-text>
      </div>
      <n-modal v-model:show="showFireModal">
        <n-card size="huge" :bordered="false" class="w-1280px">
          <div class="flex-x-center justify-center pb-10px">
            <n-icon size="35" :component="Warning" color="red"></n-icon>
            <n-text class="font-size-24px">警告！检测到<n-text class="font-700">湖面漂浮物</n-text>！</n-text>
          </div>
          <img :src="screenshotSrc" class="w-1280vw" alt="" srcset="">
        </n-card>
      </n-modal>

      <n-modal v-model:show="showRainModal">
        <n-card size="huge" :bordered="false" class="w-1280px">
          <div class="flex-x-center justify-center pb-10px">
            <n-icon size="35" :component="Warning" color="red"></n-icon>
            <n-text class="font-size-24px">警告！检测到<n-text class="font-700">垃圾违规倾倒</n-text>！</n-text>
          </div>
          <img :src="screenshotSrcRain" class="w-1280vw" alt="" srcset="">
        </n-card>
      </n-modal>

    </div>
  </div>
</template>

<script setup lang="ts">
import { PropType, computed, h, inject, nextTick, onBeforeUnmount, onMounted, reactive, ref, watch, defineExpose } from 'vue';
import { LocationQueryValue, useRoute, useRouter } from 'vue-router';
import type { Component, Ref } from 'vue'
import { Vue3Marquee } from 'vue3-marquee'
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { useRouterPush } from '@/hooks/common/router';
import { ChevronBackCircleOutline, Close, Warning, List, AirplaneOutline, BatteryHalfOutline, CloudUpload } from '@vicons/ionicons5'
import { useAirportStore } from '@/store/modules/airport';
import { MessageRenderMessage, NAlert, NIcon, useMessage, useModal, useDialog } from 'naive-ui';
import { darkTheme } from 'naive-ui';
import { io } from 'socket.io-client';
import { getServiceBaseURL } from '@/utils/service';
import {
  closeRemoteDebug, fetchDebugMenu, fetchDebugStatus, fetchLivestreamInfo, openRemoteDebug, returnHome,
  startAILivestream, stopAILivestream, startLivestream, imgUpload, stopLivestream, takeOffToPoint, triggerRemoteDebugItem, getJobById, uploadFileInfo
} from '@/service/api';
import { useDeviceStore } from '@/store/modules/device';
import flvjs from 'flv.js';
import html2canvas from 'html2canvas';
import TCPlayer, { type TCPlayerConfig } from 'tcplayer.js';
import 'tcplayer.js/dist/tcplayer.min.css';
import { useAIServerStore } from '@/store/modules/ai';
import { error } from 'console';
interface ScreenshotImage {
  url: string;
  id: number;
  time: string;
}
interface aiItem {
  机动车: number;
  单非机动车: number;
  多非机动车: number
}
// 在script setup顶部添加
const droneState = reactive({
  // 位置信息
  latitude: 0.000000,
  longitude: 0.000000,
  altitude: 0.0,
  height: "0.0",
  homeDistance: "0.0",

  // 环境信息
  windSpeed: "0.0",
  verticalSpeed: "0.0",
  windDirection: "0",

  // 飞行状态
  horizontalSpeed: "0.0",
  modeCode: "0",
  elevation: "0.0",

  // 姿态信息
  attitudeHead: "-1.0",
  attitudePitch: "1.5",
  attitudeRoll: "-0.7",

  // 设备信息
  nickname: "无人机",
  parentSn: "",

  // 电池信息
  battery: "{}",
  batteryCapacityPercent: "0",

  // 负载信息
  payloadGimbalPitch: "0.0",
  payloadGimbalRoll: "0.0",
  payloadGimbalYaw: "-1.0",
  payloadPayloadIndex: "0-0-0",

  // 定位状态
  positionStateGpsNumber: "0",
  positionStateRtkNumber: "0",

  // 其他信息
  camerasZoomFactor: "1.0",
  recordId: "0",
  storageTotal: "0",
  storageUsed: "0",
  totalFlightDistance: 0,
  trackId: 0,
  currentTime: "",  // 当前时间显示
  currentDate: "",  // 当前日期显示
  // 新增字段
  date: "",
  groundWindSpeed: "0",
  rainfall: "无",
  temperature: "0"
})
const emits = defineEmits(['cancel', 'updatebattery', 'updateProgress', 'pause', 'updateProgressTrack', 'updateDuration']);
const videoRef = ref<HTMLVideoElement | null>(null); // 引用 video 元素
const isCollapsed = ref(true);
const isCollapsedA = ref(true);
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};
const toggleCollapseA = () => {
  isCollapsedA.value = !isCollapsedA.value;
};
const imageList = ref<ScreenshotImage[]>([]); // 明确指定数组元素类型
const itemsKey = ref(0);
defineOptions({ name: 'DroneVideo' });
const airportStore = useAirportStore();
const deviceStore = useDeviceStore();
const { routerPushByKey } = useRouterPush();
const message = useMessage();
const aiServerStore = useAIServerStore();
  // 暴露视频 ref
  defineExpose({
      videoRef: videoRef,
    });
// 视频加载时触发总时长事件
watch(() => videoRef.value, (video) => {
  if (video) {
  
    video.addEventListener('loadedmetadata', () => {
      const duration = video.duration; // 总时长（秒）
      emits('updateDuration', duration); // 通知父组件（flyRecord 或上层）
    });
  }
});

const state = reactive({
  currentTime: 0,
  isSeeking: false
});

const onTimeUpdate = () => {
  if (!state.isSeeking && videoRef.value) {
    state.currentTime = videoRef.value.currentTime;
    const progress = videoRef.value.currentTime / videoRef.value.duration;
    emits('updateProgress', progress); // 传递进度到 flyRecord
  }
};

const onSeeking = () => {
  state.isSeeking = true;
};
const handlePause = (isPaused: boolean) => {
  if (!videoRef.value) return;

  if (isPaused) {
    // 视频暂停 → 无人机轨迹暂停
    emits('pause', false, 0)
    // viewer.clock.shouldAnimate = false;
    // viewer.clock.multiplier = 0;
  } else {
    // 视频播放 → 无人机轨迹继续
    emits('pause', true, 1)
    // viewer.clock.shouldAnimate = true;
    // viewer.clock.multiplier = 1;
  }
};

const onSeeked = () => {
  state.isSeeking = false;
  if (videoRef.value) {
    state.currentTime = videoRef.value.currentTime;
    const progress = videoRef.value.currentTime / videoRef.value.duration;
    emits('updateProgressTrack', progress); // 传递进度到 flyRecord
  }

};
// 判断是否使用 HTTP 代理
const isHttpProxy = false;
const { baseURL, otherBaseURL } = getServiceBaseURL(import.meta.env, isHttpProxy);

const route = useRoute();
const router = useRouter();
const dialog = useDialog();
// 连接WebSocket服务器
const socket = inject<{ chatMessage: Ref<string> }>('useSocket');  // 通过 inject 获取 provide 的 socket 对象
// const detectSocket = io(otherBaseURL.pserver); // 连接检测的WebSocket服务器
const classData = ref<aiItem[]>([]);
// 定义组件接受的属性
// const props = defineProps<{
//   // airportVideoItem: Api.Airport.AirportDeviceInfo;
// }>();

const flyInfoDockSN = ref();
const flyInfoDroneSN = ref();
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});
const airCouint = ref(-1);
const droneName = ref();

// 可以开启AI检测的modeCode列表
const CanHandelAIModeList = ['3', '4', '5', '7', '16', '17'];
const AIModeList = ['3', '4', '5', '7', '9', '16', '17'];


const boxStatus = ref(true); // 整个窗口状态： false-小窗口 true-大窗口
const debugBoxStatus = ref(false); // debug窗口状态 false-关闭 true-打开

const debugMenu = ref<Api.Airport.DebugMenu>({ dock: [], drone: [] }); // 远程调试菜单

const playerState = reactive({
  instance: null as InstanceType<typeof TCPlayer> | null,
  config: {
    licenseUrl: 'https://license.vod2.myqcloud.com/license/v2/1329835514_1/v_cube.license',
    ProgressMarker: false,
    connectRetryCount: 3,
    showLog: true,
    muted: true,
    controls: false,
    autoplay: true,
    width: '320px',  // 初始宽度
    height: '180px', // 初始高度
    controlBar: {
      fullscreenToggle: false, // 不需要全屏按钮
      playToggle: false,
      volumePanel: false,
      timeDisplay: false,
    },
    webrtcConfig: {
      connectRetryCount: 3,
      connectRetryDelay: 3,
      receiveAudio: false,
      receiveSEI: false,
      showLog: true
    }
  }
});

// 当前窗口播放状态 0-未播放 1-小屏 2-全屏
const currentScreenStatus = ref(1);
const errorMsg = ref(1); //是否错误关闭过
// 获取无人机状态
const droneStatus = computed(() => {
  return deviceStore.droneStatusList[Number(droneState.modeCode)];
});

// 大/小窗口切换事件
function handelBoxChange() {
  boxStatus.value = !boxStatus.value;
}



// 控制小屏/大屏播放
function handelVideoScreen() {
  const videoContainer = document.querySelector('#video-container');
  if (!videoContainer) return;

  if (currentScreenStatus.value === 1) { // 切换到大窗口
    videoContainer.className = 'fixed top-55px left-95px h-720px w-1280px z-100';
    currentScreenStatus.value = 2;
    debugBoxStatus.value = false;
  } else { // 切换回小窗口
    videoContainer.className = 'h-234px w-416px aspect-video relative';

    // 计算缩放比例：416/1280 ≈ 0.325
    const scale = 416 / 1280;

    currentScreenStatus.value = 1;
    if (airportDockItem.value.host?.modeCode === '2') {
      debugBoxStatus.value = true;
    }
  }
}

// 一键起飞
async function onClickToTakeOff() {
  const { error } = await takeOffToPoint(flyInfoDockSN.value || '', { targetHeight: 120 });
  if (!error) {
    message.success('一键起飞任务已下发，正在自检，请等候设备响应');
  }
}
interface TrackPoint {
  date: string;
  longitude: number;
  latitude: number;
  altitude: number;
  attitudeHead: string;
  attitudePitch: string;
  attitudeRoll: string;
  battery: string;
  batteryCapacityPercent: string;
  camerasZoomFactor: string;
  elevation: string;
  height: string;
  homeDistance: string;
  horizontalSpeed: string;
  modeCode: string;
  nickname: string;
  parentSn: string;
  payloadGimbalPitch: string;
  payloadGimbalRoll: string;
  payloadGimbalYaw: string;
  payloadPayloadIndex: string;
  positionStateGpsNumber: string;
  positionStateRtkNumber: string;
  recordId: string;
  storageTotal: string;
  storageUsed: string;
  totalFlightDistance: number;
  trackId: number;
  verticalSpeed: string;
  windDirection: string;
  windSpeed: string;
}

const trackData = ref<TrackPoint[]>([]);
function updateDroneDisplay(data: TrackPoint) {
  try {
    // 位置信息
    droneState.latitude = data.longitude || 0
    droneState.longitude = data.latitude || 0
    droneState.altitude = data.altitude || 0
    droneState.height = data.height || "0.0"
    droneState.homeDistance = data.homeDistance || "0.0"

    // 环境信息
    droneState.windSpeed = data.windSpeed || "0.0"
    droneState.verticalSpeed = data.verticalSpeed || "0.0"
    droneState.windDirection = data.windDirection || "0"

    // 飞行状态
    droneState.horizontalSpeed = data.horizontalSpeed || "0.0"
    droneState.modeCode = data.modeCode || "0"
    droneState.elevation = data.elevation || "0.0"

    // 姿态信息
    droneState.attitudeHead = data.attitudeHead || "-1.0"
    droneState.attitudePitch = data.attitudePitch || "1.5"
    droneState.attitudeRoll = data.attitudeRoll || "-0.7"

    // 设备信息
    droneState.nickname = data.nickname || "无人机"
    droneState.parentSn = data.parentSn || ""

    // 电池信息
    droneState.battery = data.battery || "{}"
    emits('updatebattery', {
      battery: JSON.parse(droneState.battery).capacityPercent + '',
      attitudePitch: droneState.attitudePitch,
      attitudeRoll: droneState.attitudeRoll,
      horizontalSpeed: droneState.horizontalSpeed
    })
    droneState.batteryCapacityPercent = data.batteryCapacityPercent || "0"

    // 负载信息
    droneState.payloadGimbalPitch = data.payloadGimbalPitch || "0.0"
    droneState.payloadGimbalRoll = data.payloadGimbalRoll || "0.0"
    droneState.payloadGimbalYaw = data.payloadGimbalYaw || "-1.0"
    droneState.payloadPayloadIndex = data.payloadPayloadIndex || "0-0-0"

    // 定位状态
    droneState.positionStateGpsNumber = data.positionStateGpsNumber || "0"
    droneState.positionStateRtkNumber = data.positionStateRtkNumber || "0"

    // 其他信息
    droneState.camerasZoomFactor = data.camerasZoomFactor || "1.0"
    droneState.recordId = data.recordId || "0"
    droneState.storageTotal = data.storageTotal || "0"
    droneState.storageUsed = data.storageUsed || "0"
    droneState.totalFlightDistance = data.totalFlightDistance || 0
    droneState.trackId = data.trackId || 0
    droneState.date = data.date || ""
    if (data.date) {
      const dateObj = new Date(data.date)
      droneState.currentTime = dateObj.toLocaleTimeString()
      droneState.currentDate = dateObj.toLocaleDateString()
    }
    // 计算字段
    droneState.groundWindSpeed = "0" // 需要从数据中获取或计算
    droneState.rainfall = "无"       // 需要从数据中获取
    droneState.temperature = "0"     // 需要从数据中获取

  } catch (e) {
    console.error('更新无人机状态失败:', e)
  }
}
function loadTrackData(trackUrl: string) {
  fetch(trackUrl)
    .then(response => response.json())
    .then(data => {
      trackData.value = data;
      startDynamicUpdate(); // 新增：开始动态更新
    })
    .catch(error => console.error('轨迹数据加载失败:', error));
}

// 新增变量
const currentDroneData = ref<TrackPoint | null>(null);
let updateInterval: number | null = null;

// 开始动态更新
function startDynamicUpdate() {
  if (updateInterval !== null) {
    window.clearInterval(updateInterval);
  }

  const sortedPoints = [...trackData.value].sort((a, b) =>
    new Date(a.date).getTime() - new Date(b.date).getTime()
  );

  let currentIndex = 0;

  updateInterval = window.setInterval(() => {
    if (currentIndex >= sortedPoints.length) {
      if (updateInterval !== null) {
        window.clearInterval(updateInterval);
        updateInterval = null;
      }
      return;
    }

    currentDroneData.value = sortedPoints[currentIndex];
    currentIndex++;
    updateDroneDisplay(currentDroneData.value);
  }, 1300);
}

// 控制远程调试的开启与关闭 debugBoxStatus.value: true-当前为开启状态，则关闭 false-当前为关闭状态，则开启
async function handelRemoteDebug() {
  let error = null;
  if (!debugBoxStatus.value) { // 打开调试
    error = (await openRemoteDebug(flyInfoDockSN.value)).error;
    if (error) return;
    getDebugMenu();
    debugBoxStatus.value = true;
    message.success('远程调试模式已开启')
  } else { // 关闭调试
    error = (await closeRemoteDebug(flyInfoDockSN.value)).error;
    debugBoxStatus.value = false;
    message.success('远程调试模式已关闭')
  }
}

// 添加追帧相关的状态
const catchUpInterval = ref<number | null>(null);
const rtmpUrl = ref('');

// 视频相关设置
const videoWidth = ref(240); // 默认小窗口宽度
const videoHeight = ref(180); // 默认小窗口高度
const canvasWidth = ref(videoWidth.value); // canvas宽度
const canvasHeight = ref(videoHeight.value); // canvas高度
const props = defineProps({
  videoUrl: {
    type: String,
    required: false,
    default: '111'
  },
  trackUrl: {
    type: String,
    required: true
  },
  targetPointVlaue: {
    type: Object,
    required: true
  }
});
const error_webrtc = ref([1004, 1005, 1006]);



// 停止播放
const onStreamStop = async () => {
  console.log('onStreamStop: ', playerState.instance)
  if (playerState.instance) {
    playerState.instance.dispose(); // 销毁播放器实例
    playerState.instance = null;

    await stopLivestream({ sn: flyInfoDroneSN.value });

    // 获取视频容器
    const container = document.querySelector('#video-container');
    if (container) {
      // 创建新的 video 元素
      const newVideo = document.createElement('video');
      newVideo.id = 'player-container-3';
      newVideo.className = 'object-fill w-100% h-100% pt-0';
      newVideo.setAttribute('preload', 'auto');
      newVideo.setAttribute('muted', '');
      newVideo.setAttribute('playsinline', '');
      newVideo.setAttribute('webkit-playsinline', '');

      // 插入到容器的开始位置
      container.insertBefore(newVideo, container.firstChild);
    }
    errorMsg.value = currentScreenStatus.value
    currentScreenStatus.value = 0; // 恢复到小窗口-未播放状态
  }
  await stopAILivestream({ input_stream: rtmpUrl.value });
}

async function updateStatus(type: 'dock' | 'drone', path: string, param: object) {
  var meg = "重启";
  if (path == 'device_reboot') {
    meg = "重启";
  } else if (path == 'device_format' || path == 'drone_format') {
    meg = "格式化";
  }
  console.log('asd');
  var deviceName;

  if (type == 'dock') {
    deviceName = airportDockItem.value.host?.nickname;
  } else {
    deviceName = '无人机';
  }

  dialog.warning({
    title: '警告',
    content: `确定要 ${meg} ${deviceName} 吗？`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const { error } = await triggerRemoteDebugItem(flyInfoDockSN.value, path, param);
      if (!error) {
        message.success('操作成功');
      } else {
        message.error(error.message)
      }
    }
  });


}
// 修改机场/无人机调试项
async function onChangeDebugItem(type: 'dock' | 'drone', value: string, index: number) {
  const cdebugMenu = JSON.parse(JSON.stringify(debugMenu.value));
  const item = debugMenu.value[type][index];
  const foundItem = item.list.find(item => item.value === value);

  if (foundItem) {
    const { param, path } = foundItem;
    const { error } = await triggerRemoteDebugItem(flyInfoDockSN.value, path, param);
    if (!error) {
      message.success("操作成功，请留意设备变化");
    } else { // 请求失败则还原选项
      debugMenu.value = cdebugMenu;
    }
  }
}

// 获取调试菜单
async function getDebugMenu() {
  const { data, error } = await fetchDebugMenu(flyInfoDockSN.value);
  debugMenu.value = data;
}

const isOpenTag = ref(false); // 无人机是否开机，控制视频播放及上线消息提醒
const snTag = ref(''); // 无人机是否开机，控制视频播放及上线消息提醒
const flightPointDTOList = ref<Api.AirLine.FlightPoint[]>([]);
const flightPointActionDTOList = ref<Api.AirLine.FlightPointAction[]>([]);
const isScreenshotLoading = ref(false)
// 监听值变化
watch(() => airportDockItem.value.host?.taskId, (msg, old) => {
  if (msg != '' && msg != old && msg != undefined && airportDockItem.value.host?.taskName != '手动飞行') {
    // let msg1 = '41'
    getJobById(msg).then((res) => {
      if (res.data) {
        flightPointDTOList.value = res.data.flightPointDTOList
        // for (let index = 0; index < flightPointDTOList.value.length; index++) {
        //   const element = flightPointDTOList.value[index];
        //   if(element.flightPointActionDTOList)

        // }
      }
    })
  }
});


// watch(
//   () => airportDroneItem.value.host?.latitude,
//   (newUser) => {
//     console.log('jwd===++++++++++',airportDroneItem.value.host?.latitude, airportDroneItem.value.host?.longitude, flightPointDTOList.value)
//     if (flightPointDTOList.value) {
//       for (let index = 0; index < flightPointDTOList.value.length; index++) {
//         if (Number(flightPointDTOList.value[index].geoJson.coordinates.latitude.toFixed(5)) == Number(airportDroneItem.value.host?.latitude) &&
//           Number(airportDroneItem.value.host?.longitude) == Number(flightPointDTOList.value[index].geoJson.coordinates.longitude.toFixed(6))) {
//           flightPointActionDTOList.value = flightPointDTOList.value[index].flightPointActionDTOList
//         }
//       }
//     }
//   },
//   { deep: true }
// );

// 添加 canvas 引用
const canvasRef = ref<HTMLCanvasElement | null>(null);
// 是否开启canvas画布
const canvasShow = ref(false)
const msgShow = ref(false)
const aiSubType = ref('')
const eventName = ref('')


// 在其他 ref 变量附近添加
const showRainModal = ref(false)
const screenshotSrcRain = ref('')

// 添加新的截图函数
function onScreenshotModelRain() {
  // 获取视频元素 - 需要获取实际的 video 元素
  const videoContainer = document.querySelector('#video-container');
  const videoElement = videoContainer?.querySelector('video') as HTMLVideoElement;

  if (videoElement && videoElement.videoWidth && videoElement.videoHeight) {
    // 创建一个 canvas 元素
    const canvas = document.createElement('canvas');
    canvas.width = videoElement.videoWidth;
    canvas.height = videoElement.videoHeight;

    // 获取 canvas 的 2D 上下文
    const context = canvas.getContext('2d');
    if (context) {
      try {
        // 绘制视频的当前帧到 canvas
        context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

        // 将 canvas 的内容转换为 Base64 格式的图像数据 URL
        screenshotSrcRain.value = canvas.toDataURL('image/png');
        showRainModal.value = !showRainModal.value;
      } catch (error) {
        console.error('Screenshot failed:', error);
      }
    }
  } else {
    console.error('Video element not found or video dimensions not available');
  }
}

const showFireModal = ref(false)
const screenshotSrc = ref('')
// let targetPointVlaue = ref({
//   // 位置信息
//   latitude: 0.000000,
//   longitude: 0.000000,
//   altitude: 0.0,
//   height: "0.0",
//   homeDistance: "0.0",

//   // 环境信息
//   windSpeed: "0.0",
//   verticalSpeed: "0.0",
//   windDirection: "0",

//   // 飞行状态
//   horizontalSpeed: "0.0",
//   modeCode: "0",
//   elevation: "0.0",

//   // 姿态信息
//   attitudeHead: "-1.0",
//   attitudePitch: "1.5",
//   attitudeRoll: "-0.7",

//   // 设备信息
//   nickname: "无人机",
//   parentSn: "",

//   // 电池信息
//   battery: "{}",
//   batteryCapacityPercent: "0",

//   // 负载信息
//   payloadGimbalPitch: "0.0",
//   payloadGimbalRoll: "0.0",
//   payloadGimbalYaw: "-1.0",
//   payloadPayloadIndex: "0-0-0",

//   // 定位状态
//   positionStateGpsNumber: "0",
//   positionStateRtkNumber: "0",

//   // 其他信息
//   camerasZoomFactor: "1.0",
//   recordId: "0",
//   storageTotal: "0",
//   storageUsed: "0",
//   totalFlightDistance: 0,
//   trackId: 0,
//   currentTime: "",  // 当前时间显示
//   currentDate: "",  // 当前日期显示
//   // 新增字段
//   date: "",
//   groundWindSpeed: "0",
//   rainfall: "无",
//   temperature: "0"
// })

onMounted(() => {
  // if (videoRef.value) {
  //   emits('pause', videoRef.value.duration); // 传递进度到 flyRecord
  // }
})


onBeforeUnmount(() => {

  if (updateInterval !== null) {
    if (updateInterval !== null) {
      window.clearInterval(updateInterval);
      updateInterval = null;
    }
  }
})

</script>

<style scoped>
.n-radio-group .n-radio-button {
  padding: 0 0.5rem;
  font-size: 0.7rem;
}

.mar {
  width: 22%;
  height: 200px;
  position: absolute;
  bottom: 0;
  right: 0;
  margin-bottom: 13.2%;
  background-color: rgb(34 34 34 / var(--un-bg-opacity));
  padding-top: 10px;
  padding-bottom: 10px;
  color: white;
  border: 1px solid rgb(68, 68, 68);
  box-shadow: 1px 1px grey;
  overflow-y: hidden;
}

.mar1 {
  background-color: rgb(34 34 34 / var(--un-bg-opacity));
  width: 20%;
  height: 300px;
  position: absolute;
  bottom: 0;
  right: 0;
  margin-bottom: 25.2%;
  padding-top: 10px;
  padding-bottom: 10px;
  color: white;
  border: 1px solid rgb(68, 68, 68);
  box-shadow: 1px 1px grey;
  overflow-y: hidden;
}

.vertical-marquee-container {
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
  /* border: 1px solid #eee; */
  border-radius: 8px;
}

.marquee-wrapper {
  height: 200px;
  width: 240px;
}

.marquee-wrapper1 {
  height: 300px;
}

.marquee-item {
  display: flex;
  /* 使用flex布局 */
  align-items: center;
  /* 垂直居中 */
  gap: 15px;
  margin: 15px -10px;
  /* 图片和描述之间的间距 */
  padding: 0 20px;
  /* 添加左右内边距 */
  flex-wrap: wrap;
}

.marquee-image {
  flex-shrink: 0;
  /* 防止图片被压缩 */
}

.img-desc {
  color: white;
  font-size: 14px;
  margin: 0;
  /* 移除默认外边距 */
  white-space: wrap;
  /* 防止文字换行 */
}

.img-desc p {
  display: block;
  margin: 0;
  padding: 0;
}

.toggle-arrow {
  position: absolute;
  right: 0;
  top: -2px;
  cursor: pointer;
}

.toggle-arrow:hover {
  color: #1890ff;
}

.carousel-img {
  width: 100%;
  height: 240px;
  object-fit: cover;
}
</style>
