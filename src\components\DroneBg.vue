<template>
  <div ref="container" class="absolute left-0 top-0 z-1 pointer-events-none"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount } from 'vue';
import lottie from 'lottie-web';

const container = ref<HTMLDivElement | null>(null);
let animIns: any = null;

onMounted(() => {
  if (container.value) {
    animIns = lottie.loadAnimation({
      container: container.value,
      renderer: 'svg',
      loop: true,
      autoplay: true,
      // 你需要将无人机动画 JSON 文件放到 src/assets/drone.json
      path: '/src/assets/drone_animation.json',
      rendererSettings: {
        preserveAspectRatio: 'xMidYMid slice'
      }
    });
  }
});

onBeforeUnmount(() => {
  if (animIns) {
    animIns.destroy();
  }
});
</script>

<style scoped>
:host {
  pointer-events: none;
}
</style>
