<template>
  <div class="ai-recognition-container" :style="{
    width: isCollapsed ? '40px' : '22%', 
    height: isCollapsed ? '40px' : '200px', 
    borderRadius: isCollapsed ? '50%' : '',
    backgroundColor: isCollapsed ? 'dark' : '',
    margin: isCollapsed ? '0px 10px 20% 0px' : '',
  }">
    <!-- 标题栏 -->
    <div class="header-section">
      <div v-if="!isCollapsed" class="title">AI实时识别图像</div>
      <div class="toggle-arrow" @click="toggleCollapse" :style="{
        left: !isCollapsed ? '' : '1px'
      }">
        <n-icon :size="24">
          <svg v-if="!isCollapsed" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
            <path d="M8.59 16.59L13.17 12 8.59 7.41 10 6l6 6-6 6-1.41-1.41z" />
          </svg>
          <img src="@/assets/imgs/AI.png" v-else style="width: 80%;" />
        </n-icon>
      </div>
    </div>

    <!-- 内容区域 -->
    <div v-if="!isCollapsed" class="content-section">
      <!-- 轮播图 -->
      <n-carousel 
        v-if="imageList.length > 0"
        direction="vertical" 
        :show-arrow="true"
        :autoplay="true"
        :interval="3000"
        :show-dots="false"
        class="image-carousel"
      >
        <div v-for="(img, index) in imageList" :key="img.id" class="carousel-item">
          <div class="image-container">
            <n-image 
              :src="img.url" 
              width="240" 
              height="180" 
              object-fit="cover"
              class="recognition-image"
            />
          </div>
          <div class="info-panel">
            <div class="info-item">
              <span class="info-label">序号：</span>
              <span class="info-value">{{ index + 1 }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">机动车：</span>
              <span class="info-value">{{ getClassCount(index, '机动车') }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">单非机动车：</span>
              <span class="info-value">{{ getClassCount(index, '单非机动车') }}</span>
            </div>
            <div class="info-item">
              <span class="info-label">多非机动车：</span>
              <span class="info-value">{{ getClassCount(index, '多非机动车') }}</span>
            </div>
          </div>
        </div>
      </n-carousel>

      <!-- 加载状态 -->
      <div v-else-if="isLoading && imageList.length <= 0" class="loading-state">
        <n-spin size="large" />
        <p class="loading-text">正在加载识别图片...</p>
      </div>

      <!-- 空状态 -->
      <div v-else class="empty-state">
        <p class="empty-text">暂无拍摄图片</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 定义接口
interface ScreenshotImage {
  url: string;
  id: number;
  time: string;
}

interface ClassData {
  机动车?: number;
  单非机动车?: number;
  多非机动车?: number;
}

// 定义 props
interface Props {
  imageList: ScreenshotImage[];
  classData: ClassData[];
  isLoading: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  imageList: () => [],
  classData: () => [],
  isLoading: false
});

// 响应式数据
const isCollapsed = ref(true);

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const getClassCount = (index: number, type: keyof ClassData): number => {
  if (!props.classData[index]) return 0;
  return props.classData[index][type] || 0;
};
</script>

<style scoped>
.ai-recognition-container {
  position: absolute;
  bottom: 0;
  right: 0;
  margin-bottom: 13.2%;
  background-color: rgb(34 34 34 / var(--un-bg-opacity));
  padding: 10px;
  color: white;
  border: 1px solid rgb(68, 68, 68);
  box-shadow: 1px 1px grey;
  overflow: hidden;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.header-section {
  position: relative;
  margin-bottom: 10px;
}

.title {
  font-weight: bold;
  font-size: 15px;
  margin-left: 20px;
  color: #ffffff;
}

.toggle-arrow {
  position: absolute;
  right: 0;
  top: -2px;
  cursor: pointer;
  transition: color 0.3s ease;
}

.toggle-arrow:hover {
  color: #1890ff;
}

.content-section {
  width: 100%;
  height: 340px;
  background-color: rgb(42 42 42 / 0.9);
  border-radius: 6px;
  padding: 10px;
}

.image-carousel {
  width: 100%;
  height: 100%;
}

.carousel-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  padding: 10px;
  height: 100%;
}

.image-container {
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.recognition-image {
  border-radius: 8px;
}

.info-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  max-width: 240px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 12px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 4px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.info-label {
  font-size: 13px;
  color: #e0e0e0;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #ffffff;
  font-weight: bold;
  background: linear-gradient(45deg, #1890ff, #52c41a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  gap: 15px;
}

.loading-text {
  color: #e0e0e0;
  font-size: 14px;
  margin: 0;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.empty-text {
  color: #999;
  font-size: 14px;
  margin: 0;
}

/* 轮播图箭头样式优化 */
:deep(.n-carousel-arrow) {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

:deep(.n-carousel-arrow:hover) {
  background-color: rgba(24, 144, 255, 0.8) !important;
  border-color: #1890ff !important;
}

:deep(.n-carousel-arrow .n-icon) {
  color: #ffffff !important;
}
</style>
