<template>
  <div
    class="fixed bottom-0 right-0 bg-dark-theme border border-gray-600 shadow-lg transition-all duration-300 overflow-hidden"
    :class="{
      'w-40px h-40px rd-50% mb-20%': isCollapsed,
      'w-22% h-200px mb-13.2%': !isCollapsed
    }" :style="{
      margin: isCollapsed ? '0px 10px 20% 0px' : '',
    }">
    <!-- 标题栏 -->
    <div class="relative flex">
      <div v-if="!isCollapsed" class="font-bold text-15px ml-20px my-5px text-white">
        AI实时识别图像
      </div>
      <div class="absolute right-0 top-8px cursor-pointer transition-colors duration-300 hover:text-blue-500"
        @click="toggleCollapse">
        <SvgIcon v-if="!isCollapsed"  icon="mdi:chevron-right" class="text-icon text-#fff cursor-pointer" />
        <img v-else src="@/assets/imgs/AI.png" class="w-28px ml--8px" />
      </div>
    </div>

    <!-- 内容区域 -->
    <div v-if="!isCollapsed" class="w-full h-160px bg-dark-7 rd-6px ">
      <!-- 轮播图 -->
      <n-carousel v-if="imageList.length > 0" direction="vertical" :show-arrow="true" :autoplay="true" :interval="3000"
        :show-dots="false" class="w-full h-full">
        <div v-for="(img, index) in imageList" :key="img.id" class="flex items-start gap-15px p-10px h-full">
          <div class="flex-shrink-0 overflow-hidden shadow-lg">
            <n-image :src="img.url" width="240" height="180" object-fit="cover" class="" />
          </div>
          <div class="flex-1 flex flex-col gap-8px w-full max-w-240px">
            <div class="flex justify-between items-center ">
              <!-- <span class="text-13px text-gray-300 font-medium">序号：</span> -->
              <span class="text-12px text-white font-bold text-blue-400">{{ index + 1 }}</span>
            </div>
            <div
              class="flex justify-between items-center px-4px py-2px bg-white bg-opacity-10 rd-4px backdrop-blur-10px border border-white border-opacity-20">
              <span class="text-12px text-gray-300 font-medium">机动车：</span>
              <span class="text-12px text-white font-bold text-green-400">{{ getClassCount(index, '机动车') }}</span>
            </div>
            <div
              class="flex justify-between items-center px-4px py-2px bg-white bg-opacity-10 rd-4px backdrop-blur-10px border border-white border-opacity-20">
              <span class="text-12px text-gray-300 font-medium">单非机动车：</span>
              <span class="text-12px text-white font-bold text-yellow-400">{{ getClassCount(index, '单非机动车') }}</span>
            </div>
            <div
              class="flex justify-between items-center px-4px py-2px bg-white bg-opacity-10 rd-4px backdrop-blur-10px border border-white border-opacity-20">
              <span class="text-12px text-gray-300 font-medium">多非机动车：</span>
              <span class="text-12px text-white font-bold text-orange-400">{{ getClassCount(index, '多非机动车') }}</span>
            </div>
          </div>
        </div>
      </n-carousel>

      <!-- 加载状态 -->
      <div v-else-if="isLoading && imageList.length <= 0"
        class="flex flex-col items-center justify-center h-full gap-15px">
        <n-spin size="large" />
        <p class="text-gray-300 text-14px m-0">正在加载识别图片...</p>
      </div>

      <!-- 空状态 -->
      <div v-else class="flex items-center justify-center h-full">
        <p class="text-gray-500 text-14px m-0">暂无拍摄图片</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 定义接口
interface ScreenshotImage {
  url: string;
  id: number;
  time: string;
}

interface ClassData {
  机动车?: number;
  单非机动车?: number;
  多非机动车?: number;
}

// 定义 props
interface Props {
  imageList: ScreenshotImage[];
  classData: ClassData[];
  isLoading: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  imageList: () => [],
  classData: () => [],
  isLoading: false
});

// 响应式数据
const isCollapsed = ref(true);

// 方法
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const getClassCount = (index: number, type: keyof ClassData): number => {
  if (!props.classData[index]) return 0;
  return props.classData[index][type] || 0;
};
</script>

<style scoped>
/* 轮播图箭头样式优化 */
:deep(.n-carousel-arrow) {
  background-color: rgba(255, 255, 255, 0.2) !important;
  border: 1px solid rgba(255, 255, 255, 0.3) !important;
  backdrop-filter: blur(10px) !important;
}

:deep(.n-carousel-arrow:hover) {
  background-color: rgba(24, 144, 255, 0.8) !important;
  border-color: #1890ff !important;
}

:deep(.n-carousel-arrow .n-icon) {
  color: #ffffff !important;
}
</style>
