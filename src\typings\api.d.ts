/**
 * Namespace Api
 *
 * All backend api type
 */
declare namespace Api {
  namespace Common {
    /** common params of paginating */
    interface PaginatingCommonParams {
      /** current page number */
      current: number;
      /** page size */
      size: number;
      /** total count */
      total: number;
    }

    /** common params of paginating query list data */
    interface PaginatingQueryRecord<T = any> extends PaginatingCommonParams {
      records: T[];
    }

    /** common search params of table */
    type CommonSearchParams = Pick<Common.PaginatingCommonParams, 'current' | 'size'>;

    /**
     * enable status
     *
     * - "1": enabled
     * - "2": disabled
     */
    type EnableStatus = '1' | '2';

    /** common record */
    type CommonRecord<T = any> = {
      /** record id */
      id: number;
      /** record creator */
      createBy: string;
      /** record create time */
      createTime: string;
      /** record updater */
      updateBy: string;
      /** record update time */
      updateTime: string;
      /** record status */
      status: EnableStatus | null;
    } & T;
  }

  /**
   * namespace Auth
   *
   * backend api module: "auth"
   */
  namespace Auth {
    interface LoginToken {
      token: string;
    }

    interface UserInfo {
      roles: string[];
      user: { [key: string]: any };
    }

    interface SysInfo {
      webConfig: {
        webName: string;
        webLogoName: string;
        webLogoUrl: string;
        webLogo: string;
        centerLongitude: string;
        centerLatitude: string;
      };
      dockConfig: {};
    }

    interface UserConfig {
      roles: string[];
      user: {
        dockConfig: {};
        webConfig: {
          webName: string;
          webLogo: string;
        };
      };
    }
  }

  /**
   * namespace Route
   *
   * backend api module: "route"
   */
  namespace Route {
    type ElegantConstRoute = import('@elegant-router/types').ElegantConstRoute;

    interface MenuRoute extends ElegantConstRoute {
      id: string;
    }

    interface UserRoute {
      routes: MenuRoute[];
      home: import('@elegant-router/types').LastLevelRouteKey;
    }
  }

  namespace List {
    interface Table {
      pageNum?: number;
      pageSize?: number;
    }

    interface StatusItem {
      dictLabel: string;
      dictValue: string;
      listClass: string;
    }

    interface Options {
      label: string;
      value: string;
    }
  }
  // 机场
  namespace Airport {
    // mqtt注册信息
    interface MqttRegisterInfo {
      id?: number;
      mqttGateway: string;
      mqttUsername: string;
      mqttPassword: string;
      gatewayAccount: string;
    }

    // 坐标
    interface MachineNestInfo {
      id: number;
      longitude: number;
      latitude: number;
      distance: number;
      autoWindSpeed: number;
      deviceSn: string;
      bgPointColor: string;
      deviceName: string;
      landPointLatitude: number;
      landPointLongitude: number;
      status: boolean;
    }

    // 机场注册信息
    interface RegisterInfo {
      id?: number;
      mqttGateway?: string;
      mqttUsername?: string;
      mqttPassword?: string;
      gatewayAccount?: string;
      updateTime?: string;
      deviceDto?: {
        nickname?: string;
        deviceName?: string;
        deviceSn?: string;
        firmwareVersion?: string;
        workspaceName?: string;
        iconUrl?: {
          normal_icon_url?: string;
        };
        children?: {
          nickname?: string;
          deviceName?: string;
          deviceSn?: string;
          firmwareVersion?: string;
          iconUrl?: {
            normal_icon_url?: string;
          };
        };
      };
      bindStatus?: number;
    }

    // 机场设备信息
    interface AirportDeviceInfo {
      id?: number;
      longitude?: number;
      latitude?: number;
      altitude?: number;
      distance?: number;
      deviceSn?: string;
      deviceName?: string;
      childDevice?: {
        droneName?: string;
        droneSn?: string;
        host?: {
          modeCode: string;
          batteryGapacityPercent: string;
          temperature?: string;
          rtmpUrl: string;
          camerasCameraMode: string; // 模式 0拍照1录像2智能低光3全景拍照
          camerasPhotostate: string; // 拍照状态 0未拍照1拍照中
          camerasRecordingState: string; // 录像状态 {"0":"空闲","1":"录像中"}
          cameraType: string; // 相机类型 wide/zoom/ir
          camerasZoomFactor: string;
        };
      };
      host?: {
        humidity: string;
        temperature: string;
        rainfall: string;
        capacityPercent: string;
        batteryState: string;
        windSpeed: string;
        homeDistance: string;
        horizontalSpeed: string;
        elevation: string;
        verticalSpeed: string;
        modeCode: string;
        nickname: string;
        rtkNumber: string;
        gpsNumber: string;
        height: string;
        latitude: string;
        longitude: string;
        taskName: string;
        taskId: string;
        payloadGimbalPitch: number;
        airConditionerSwitchTime: string; // 状态切换冷却 时间
        battery: {
          batteries: Array<{
            voltage: number;
            temperature: number;
          }>;
        };
        positionStateGpsNumber: string;
        batteryGapacityPercent: string;
        droneModeCode: string;
        rtmpUrl: string;
        attitudeRoll: number; // 滚转角
        attitudePitch: number; // 俯仰角
        attitudeHead: number; // 航向角
      };
      liveStreaInfo?: {
        appId: string;
        channel: string;
        token: string;
        uid: number;
      };
      errorMsgArray?: Array<{
        key: string;
        createTime: Date;
        level: number;
        messageZh: string;
        messageEn: string;
        module: number;
        sn: string;
        hmsId: string;
      }>;
    }

    // 远程调试
    interface DebugMenu {
      dock: DebugMenuItem[];
      drone: DebugMenuItem[];
    }
    interface DebugMenuItem {
      name: string;
      list: Array<DebugMenuItemList>;
      status: string;
    }
    interface DebugMenuItemList {
      path: string;
      value: string;
      label: string;
      param: object;
    }
  }

  // 航点与航线
  namespace AirLine {
    interface WayPointList {
      latitude: number;
      longitude: number;
      altitude: number;
    }

    interface FlightPoint {
      pointId: number;
      flightId: number;
      ellipsoidHeight: number;
      geoJson: {
        type: string;
        coordinates: {
          altitude: number;
          latitude: number;
          longitude: number;
        }
      };
      speed: number;
      height: number;
      sort: number;
      flightPointActionDTOList: FlightPointAction[];
    }

    interface FlightPointAction {
      actionId: number;
      pointId: number;
      flightId: number;
      actionActuatorFunc: string;
      actionActuatorFuncDesc: string;
      params: {
        [key: string]: any;
      };
      wpmlIndex: number;
    }

    interface AirLineItem {
      workspaceId?: string;
      workspaceName?: string;
      flightImage?: string;
      flightName?: string;
      flightType?: number;
      createName?: string;
      createTime?: string;
      flightId?: number;
    }

    interface AirLineItems {
      workspaceId?: string;
      workspaceName?: string;
      droneName?: string;
      flightName?: string;
      flightType?: number;
      createName?: string;
      createTime?: string;
      flightId?: number;
      status?: number,
      flightInfo?: string
    }
    interface AirLineConfig {
      transitionalSpeed?: number;
      surfaceRelativeHeight?: number;
      flightName?: string;
    }

    interface AirPointActionItem {
      actionId?: number;
      flightId?: number;
      params: {
        payloadLensIndex?: string | string[];
        hoverTime?: number;
        gimbalPitchRotateAngle?: number;
        gimbalRotateTime?: number;
        gimbalYawRotateAngle?: number;
        aircraftHeading?: number;
        focalLength?: number;
        gimbalYawRotateEnable?: number;
        gimbalPitchRotateEnable?: number;
        mode?: number;
        max?: number;
        min?: number;
        tips?: string;
      };
      pointId?: number;
      wpmlIndex?: number;
      actionActuatorFunc?: string;
    }
  }

  // 三维模型列表
  namespace threeMaterial {
    interface ModleItem {
      id?: string;
      name?: string;
      path?: string;
      type?: number;
      createBy?: string;
      createTime?: string;
      identifter?: number;
      workspaceId?: string,
      deleted?: boolean,
      flightImage?: string,
      createName?: string,
    }
  }

  // 任务
  namespace Task {
    // 每一项任务
    interface TaskItem {
      workspaceId?: string;
      workspaceName?: string;
      flightJobId?: number;
      flightJobName?: string;
      flightJobType?: number;
      deviceSn?: string | null;
      droneSn?: string;
      flightId?: number;
      returnHomeHeight?: number;
      execTime?: string | null;
      periodUnit?: number;
      periodExecTimeList?: string[];
      periodValueList?: number[];
      myDeviceName?: string;
      flightName?: string;
      status?: number;
      flightJobStatus?: number;
      isCreat?: boolean; // isCreat:是否为新增 true-新增 false-编辑
      exceptionInfo?: string;
      recordId?: number;
    }
    // 每一项任务
    interface TaskItemHis {
      dockSn: string;
      workspaceId?: string;
      workspaceName?: string;
      flightJobId?: number;
      flightJobName?: string;
      flightJobType?: number;
      deviceSn?: string | null;
      droneSn?: string;
      flightId?: number;
      returnHomeHeight?: number;
      execTime?: string | null;
      periodUnit?: number;
      periodExecTimeList?: string[];
      periodValueList?: number[];
      myDeviceName?: string;
      flightName?: string;
      status?: number;
      flightJobStatus?: number;
      isCreat?: boolean; // isCreat:是否为新增 true-新增 false-编辑
      exceptionInfo?: string;
      recordId?: number;
    }

    // 任务列表查询
    interface TaskScreen extends List.Table, TaskItem {
      deptId?: number;
      pageNum: number;
      pageSize: number;
      droneName?: string;
      dockName?: string;
      createName?: string;
      flightId?: number;
      flightJobStatus?: number;
      startDate?: number | string | null;
      endDate?: number | string | null;
      dateRange?: [number, number];
    }
    // 任务列表查询-数据大屏
    interface TaskScreens extends List.Table, TaskItem {
      deptId?: number;
      pageNum: number;
      pageSize: number;
      droneName?: string;
      dockName?: string;
      createName?: string;
      flightId?: number;
      flightJobStatus?: number;
      startDate?: number | string | null;
      endDate?: number | string | null;
      dateType?: number | string | null;
      dateRange?: [number, number];
    }
  }

  // AI算法
  namespace AI {
    // 每一项AI算法的配置
    interface AIItem {
      id: PropertyKey | undefined;
      class_id: number;
      name?: string;
      code?: string;
      status?: string | boolean; // 1-开启 0-关闭
      aiRetest?: boolean;
      aiType: number;
    }

    interface AIConfig {
      id?: number;
      pickPeriod?: number;
      enableHeight?: number;
      configConfirm?: number;
      status?: number;
    }
  }

  // 文件
  namespace File {
    // 文件列表查询
    interface FileQuery extends List.Table {
      recordIds?: string | null;
      fileType?: number;
      startDate?: string | null;
      endDate?: string | null;
      flightJobId?: number;
      deptId?: number | null;
      subFileType?: number | null;
      flightId?: string | null;
    }

    interface PhotoListItem {
      id: string;
      workspaceId: string;
      workspaceName: string;
      cover: string;
      filePath?: string;
      createTime?: string;
      updateTime?: string;
      objectKey?: string;
      creatorOid?: string;
      payload?: string;
      address?: string;
      size: number;
      fileName: string;
      jobId: string;
      jobName: string;
      lat: number;
      lng: number;
      absoluteAltitude: number;
      flightType: number;
      subFileType: number;
    }

    interface VideoListItem {
      id: string;
      workspaceId: string;
      workspaceName: string;
      cover: string;
      filePath?: string;
      createTime?: string;
      updateTime?: string;
      objectKey?: string;
      creatorOid?: string;
      payload?: string;
      address?: string;
      size: number;
      fileName: string;
      jobId: string;
      jobName: string;
      lat: number;
      lng: number;
      absoluteAltitude: number;
      flightType: number;
      subFileType: number;
      flightJobId?: number;
      remark?: string;
    }
  }

  // 设备
  namespace Device {
    // 设备状态列表
    interface DeviceStatusList {
      dictLabel?: string;
      dictValue?: string;
      listClass?: 'default' | 'error' | 'success' | 'primary' | 'info' | 'warning';
    }

    interface DockInfo extends AirLine.WayPointList {
      droneParam: {
        camerasCameraMode: string; // 模式 0拍照1录像2智能低光3全景拍照
        camerasPhotostate: string; // 拍照状态 0未拍照1拍照中
        camerasRecordingState: string; // 录像状态 {"0":"空闲","1":"录像中"}
        cameraType: string; // 相机类型 wide/zoom/ir
        camerasZoomFactor: string;
      };
      host?: {
        taskId?: string;
        modeCode?: string;
        taskName?: string;
      };
    }
    interface NoflyInfo {
      id?: number;
      type?: string;
      createBy?: number;
      createName?: string;
      createTime?: string;
      address?: string;
      properties?: { [key: string]: string | number };
      geoJson: {
        type: string;
        geometry: {
          type: string;
          coordinates: number[][];
        }
      };
      centerGeoJson: {
        type: string;
        coordinates: number[];
      };
      noFlyName: string;
      noFlyType: number;
      radius?: number;
      updateBy?: number;
      updateName?: string;
      updateTime?: string;
      workspaceId?: string;
    }
  }

  // 状态
  namespace DictData {
    // 所有状态字典
    interface DictDataStatus {
      dictLabel?: string;
      dictValue?: string;
      dictType?: string;
      dictSort?: number;
      listClass?: string;
    }

    type tagType = 'default' | 'error' | 'success' | 'primary' | 'info' | 'warning';
  }

  // 首页
  namespace Home {
    interface IndexAll {
      allFlightCount: number;
      allFlightMileage: number;
      allFlightTime: number;
    }

    interface IndexOrg {
      aiCount: number;
      dockCount: number;
      flightCount: number;
      flightLineCount: number;
      onlineDevice: number;
      resultCount: number;
      userCount: number;
      droneCount: number;
    }

    interface FlightCountItem {
      drone_sn: string;
      flight_count: number;
      nick_name: string;
    }

    interface FlightTimeItem {
      nick_name: string;
      sumtime: number;
      mileage: number;
    }

    interface FlightMileageItem {
      drone_sn: string;
      mileage: number;
      nickname: string;
    }

    interface DataOverview {
      flightCount: FlightCountItem[];
      flightTime: FlightTimeItem[];
      flightMileage: FlightMileageItem[];
    }
  }

  namespace Dept {
    interface DeptItem {
      deptId: number | null;
      parentId: number | null;
      deptName: string;
      orderNum: number | null;
      phone?: string;
      email: string;
      status: number;
      validityDate?: Date | null;
      leader?: string;
    }

    interface DeptTree {
      id: number;
      label: string;
      children: Array<DeptTree>;
    }
  }

  namespace User {
    interface UserItem {
      userId: number | null;
      deptId: number | null;
      userName: string;
      nickName: string;
      phonenumber: string;
      password: string;
      email: string;
      sex: number;
      status: number;
      roleIds: Array<number>;
    }
  }
}
