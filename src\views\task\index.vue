<template>
  <div>
    <NCard title="任务列表" :bordered="false">
      <!-- 搜索 -->
      <n-form ref="formRef" inline :model="formValue" label-placement="left" label-width="auto">
        <n-grid :cols="5" :x-gap="24">
          <NFormItemGi label="组织选择：" path="deptId">
            <NTreeSelect
              clearable
              v-model:value="formValue.deptId"
              filterable
              key-field="id"
              label-field="label"
              children-field="children"
              :options="treeData"
              :default-value="formValue.deptId"
            />
          </NFormItemGi>
          <n-form-item-gi label="任务名称：" path="flightJobName">
            <n-input v-model:value="formValue.flightJobName" placeholder="输入任务名称" clearable />
          </n-form-item-gi>
          <n-form-item-gi label="航线名称：" path="flightName">
            <n-input v-model:value="formValue.flightName" placeholder="输入航线名称" clearable/>
          </n-form-item-gi>
          <n-form-item-gi label="执行机场：" path="dockName">
            <n-input v-model:value="formValue.dockName" placeholder="输入机场名称" clearable/>
          </n-form-item-gi>
          <n-form-item-gi label="任务状态：" path="flightJobStatus">
            <n-select v-model:value="formValue.flightJobStatus" placeholder="请选择" :options="generalStatusOptions" clearable/>
          </n-form-item-gi>
          <n-form-item-gi :span="2" label="日期范围：" path="range">
            <n-date-picker v-model:value="formValue.range" type="daterange" clearable />
          </n-form-item-gi>
          <n-form-item-gi>
            <n-button attr-type="button" @click="handleResetClick" class="mr2">重置</n-button>
            <n-button type="primary" attr-type="button" @click="handleSearchClick">查询</n-button>
          </n-form-item-gi>
        </n-grid>
      </n-form>
      <n-grid>
        <n-form-item-gi :span="8">
          <n-button type="primary" attr-type="button" @click="(isCreat) => handelCreatTaskModalShow()"
            class="px-20px">新建任务</n-button>
        </n-form-item-gi>
      </n-grid>
      <!-- 表格 -->
      <n-dialog-provider>
        <n-data-table :columns="tableColumns" :data="taskTableData" :pagination="paginationReactive" :loading="tableLoading" />
      </n-dialog-provider>
    </NCard>
    <creatTask :creatTaskModalShow="creatTaskModalShow" :taskInfoItemData="taskInfoItemData"
      @task-visible="handelCreatTaskModalShow" />
    <taskInfo :taskInfoModalShow="taskInfoModalShow" :taskInfoItemData="taskInfoItemData"
      @task-info-visible="handleTaskInfoModalShow" />
  </div>

</template>

<script setup lang="ts">
import { h, onMounted, ref } from 'vue'
import { useAppStore } from '@/store/modules/app';
import { fetchTaskList, sendTaskStatusInfo } from '@/service/api';
import creatTask from './modules/creat-task.vue';
import taskInfo from './modules/task-info.vue';
import { NTag, NText, useDialog, useMessage } from 'naive-ui';
import dayjs from "dayjs";
import { useRouter } from 'vue-router';
import {deptTree} from "@/service/api/user";

const appStore = useAppStore();
const message = useMessage();
const dialog = useDialog();
const router = useRouter();

const creatTaskModalShow = ref(false); // 控制新增机场任务弹窗
const taskInfoModalShow = ref(false); // 控制某一项机场任务详细信息弹窗
const treeData = ref([]);
const formValue = ref<{
  deptId: number | null;
  flightJobName: string;
  dockName: string;
  range: [number, number] | null;
  flightName: string;
  flightJobStatus: string | null;
}>({
  deptId: null,
  dockName: '',
  flightJobName: '',
  range: null,
  flightName: '',
  flightJobStatus: null
});

const tableColumns = [
  { title: '组织', key: 'workspaceName'},
  { title: '任务ID', key: 'flightJobId' },
  { title: '任务名称', key: 'flightJobName' },
  { title: '航线名称', key: 'flightName' },
  {
    title: '计划执行时间',
    key: 'execTime',
    render(row: Api.Task.TaskItem) {
      let timeText = '';
      switch (row.flightJobType) {
        case 1: // 立即执行
          timeText = '立即执行';
          break;
        case 2: // 定时执行
          timeText = row.execTime ? dayjs(row.execTime).format('YYYY-MM-DD HH:mm:ss') : '-';
          break;
        case 3: // 周期执行
          if (row.periodUnit === 1) { // 每天
            timeText = `每天 ${row.periodExecTimeList?.join(', ') || '-'}`;
          } else { // 每周
            const weekDays = row.periodValueList?.map(v => ['周一', '周二', '周三', '周四', '周五', '周六', '周日'][v - 1]).join(', ') || '-';
            timeText = `每周 ${weekDays} ${row.periodExecTimeList?.join(', ') || '-'}`;
          }
          break;
      }
      return h(NText, {}, { default: () => timeText });
    }
  },
  {
    title: '任务类型', key: 'flightJobType',
    render(row: Api.Task.TaskItem) {
      return h(
        NText, {},
        {// flightJobType: 1 立即执行 2 定时执行 3 周期执行
          default: () => row.flightJobType === 1 ? '立即执行' : row.flightJobType === 2 ? '定时执行' : '周期执行'
        }
      )
    }
  },
  { title: '创建者', key: 'createName' },
  { title: '执行机场', key: 'myDeviceName' },
  {
    title: '任务状态', key: 'flightJobStatus',
    render(row: Api.Task.TaskItem) {
      return h(
        NTag,
        {
          type: row.flightJobStatus === 0 ? 'default' : row.flightJobStatus === 1 ? 'info' : row.flightJobStatus === 2 ? 'warning' : 'error',
          bordered: false
        },
        {// flightJobStatus: 0-结束 1-开启 2-执行中 3-暂停
          default: () => row.flightJobStatus === 0 ? '结束' : row.flightJobStatus === 1 ? '开启' : row.flightJobStatus === 2 ? '执行中' : '暂停'
        }
      )
    }
  },
  {
    title: '操作',
    key: 'action',
    render(row: Api.Task.TaskItem) {
      return h('span', [
        h(
          NText, {},
          {
            default: () => {
              let elements = [];
              elements.push(h(NText, {
                type: 'info', class: 'cursor-pointer', onClick: () => onClickTaskInfoModal(row)
              }, { default: () => '查看' }));

              if (row.flightJobStatus === 2) {
                elements.push(h(NText, {
                  type: 'info', class: 'cursor-pointer ml-10px', onClick: () => goToFlyDetail(row)
                }, { default: () => '详情' }));
              }

              // 1.立即执行 查看
              // 2.定时任务 查看 a.开启状态: 编辑 终止
              // 3.周期任务 查看 a.开启状态: 编辑 暂停 终止 b.暂停状态: 编辑 恢复 终止
              switch (row.flightJobType) {
                case 2: // 定时任务
                  if (row.flightJobStatus === 1) { // 开启状态可操作
                    elements.push(h(NText, {
                      type: 'info', class: 'cursor-pointer ml-10px', onClick: () => handelCreatTaskModalShow(row, false)
                    }, { default: () => '编辑' }));
                    elements.push(h(NText, {
                      type: 'info', class: 'cursor-pointer ml-10px', onClick: () => onClickActions(row, 0)
                    }, { default: () => '终止' }));
                  }
                  break;
                case 3: // 周期任务
                  if (row.flightJobStatus === 1) { // 1-开启状态
                    elements.push(h(NText, {
                      type: 'info', class: 'cursor-pointer ml-10px', onClick: () => handelCreatTaskModalShow(row, false)
                    }, { default: () => '编辑' }));
                    elements.push(h(NText, {
                      type: 'info', class: 'cursor-pointer ml-10px', onClick: () => onClickActions(row, 3)
                    }, { default: () => '暂停' }));
                  } else if (row.flightJobStatus === 3) { // 3-暂停状态
                    elements.push(h(NText, {
                      type: 'info', class: 'cursor-pointer ml-10px', onClick: () => handelCreatTaskModalShow(row, false)
                    }, { default: () => '编辑' }));
                    elements.push(h(NText, {
                      type: 'info', class: 'cursor-pointer ml-10px', onClick: () => onClickActions(row, 1)
                    }, { default: () => '恢复' }));
                  }
                  elements.push(h(NText, {
                    type: 'info', class: 'cursor-pointer ml-10px', onClick: () => onClickActions(row, 0)
                  }, { default: () => '终止' }));
                  break;
              }

              return elements;
            }
          }
        )
      ]);
    }
  }
]
const tableLoading = ref(false);
const taskTableData = ref<Api.Task.TaskItem[]>([]);
const taskInfoItemData = ref<Api.Task.TaskItem>({}); // 点击某一项查看的数据

const generalStatusOptions: { label: string; value: number; }[] = [{'label': '开启', 'value': 1}, {'label':'暂停','value':3}, {'label':'执行中', 'value': 2}].map(v => ({
  label: v.label,
  value: v.value
}));

const paginationReactive = ref({
  page: 1,
  pageSize: 10,
  showSizePicker: true,
  pageSizes: [10, 15, 20],
  onChange: (page: number) => {
    paginationReactive.value.page = page;
  },
  onUpdatePageSize: (pageSize: number) => {
    paginationReactive.value.pageSize = pageSize;
    paginationReactive.value.page = 1;
  }
})

// 控制任务详情弹窗展示
function handleTaskInfoModalShow() {
  taskInfoModalShow.value = !taskInfoModalShow.value;
}

// 操作按钮[编辑 暂停 恢复 终止]-0停止任务，1恢复任务，3暂停任务，-1删除（删除任务）
async function onClickActions(row: Api.Task.TaskItem, status: number) {
  dialog.warning({
    title: `确定${status === 0 ? '停止' : status === 1 ? '恢复' : '暂停'}任务？`,
    content: `确定后将 ${status === 0 ? '停止' : status === 1 ? '恢复' : '暂停'} ${row.flightJobName} 任务`,
    positiveText: '确定',
    negativeText: '取消',
    onPositiveClick: async () => {
      const { error } = await sendTaskStatusInfo(row.flightJobId || 0, status);
      if (!error) {
        message.success(`${status === 0 ? '停止' : status === 1 ? '恢复' : '暂停'}任务成功`);
        await getTaskList();
      }
    }
  })

}

// 跳转至飞行详情 sn=&dsn=&tid=
function goToFlyDetail(row: Api.Task.TaskItem) {
  router.push({
    path: '/fly-detail',
    query: { sn: row.deviceSn, dsn: row.droneSn,}
  });
}

// 获取任务列表
async function getTaskList() {
  tableLoading.value = true;
  const { page, pageSize } = paginationReactive.value;
  const { flightJobName, flightName, dockName, flightJobStatus, range, deptId } = formValue.value;
  const query = {
    deptId,
    flightName,
    flightJobName,
    flightJobStatus,
    dockName,
    pageNum: page,
    pageSize,
    startDate: range && range[0] ? dayjs(range[0]).format('YYYY-MM-DD') : null,
    endDate: range && range[1] ? dayjs(range[1] + 24 * 60 * 60 * 1000).format('YYYY-MM-DD') : null
  };
  const { data } = await fetchTaskList(query);
  tableLoading.value = false;
  taskTableData.value = data.rows;
}

// 切换新增/修改任务弹窗  isCreat:是否为新增 true-新增 false-编辑
function handelCreatTaskModalShow(row?: Api.Task.TaskItem, isCreat: boolean = true) {
  creatTaskModalShow.value = !creatTaskModalShow.value;
  taskInfoItemData.value = { ...row, isCreat };
  if (isCreat) {
    // 如果是新增，则刷新列表
    getTaskList();
  }
};

// 点击查看任务详情弹窗
function onClickTaskInfoModal(row: Api.Task.TaskItem) {
  handleTaskInfoModalShow();
  taskInfoItemData.value = row;
}

// 查询
const handleSearchClick = () => {
  getTaskList();
};

// 重置
const handleResetClick = () => {
  formValue.value = {
    deptId: null,
    flightName: '',
    dockName: '',
    flightJobName: '',
    range: null,
    flightJobStatus: null
  };
  getTaskList();
};

async function getDeptTree() {
  const { data } = await deptTree({});
  treeData.value = data;
}

onMounted(() => {
  getDeptTree();
  getTaskList();
});

</script>

<style scoped></style>
