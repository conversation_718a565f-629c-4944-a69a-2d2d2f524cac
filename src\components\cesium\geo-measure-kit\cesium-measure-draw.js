import * as Cesium from 'cesium'
if (typeof Cesium !== 'undefined')
  /**
    
    * <AUTHOR>
    * @param viewer  {object} 三维对象
    * @param options {object} 初始化参数
    * @constructor
    */
  var CesiumMeasures = (function (Cesium) {
    /**
     * 绘制对象
     * @param viewer
     * @param options
     * @constructor
     */
    function _(viewer, options = {}) {
      if (viewer && viewer instanceof Cesium.Viewer) {
        this._drawLayer = new Cesium.CustomDataSource('measureLayer');
        viewer.dataSources.add(this._drawLayer);

        this._basePath = options.basePath || '';
        this._viewer = viewer;

        this._eventHandlers = [];
        this._tooltip = this._createTooltipElement();
      }
    }
    _.prototype = {
      /***
       * 坐标转换 84转笛卡尔
       *
       * @param {Object} {lng,lat,alt} 地理坐标
       *
       * @return {Object} Cartesian3 三维位置坐标
       */
      transformWGS84ToCartesian: function (position, alt) {
        if (this._viewer) {
          return position
            ? Cesium.Cartesian3.fromDegrees(
              position.lng || position.lon,
              position.lat,
              (position.alt = alt || position.alt),
              Cesium.Ellipsoid.WGS84
            )
            : Cesium.Cartesian3.ZERO
        }
      },
      /***
       * 坐标数组转换 笛卡尔转84
       *
       * @param {Array} WSG84Arr {lng,lat,alt} 地理坐标数组
       * @param {Number} alt 拔高
       * @return {Array} Cartesian3 三维位置坐标数组
       */
      transformWGS84ArrayToCartesianArray: function (WSG84Arr, alt) {
        if (this._viewer && WSG84Arr) {
          var $this = this
          return WSG84Arr
            ? WSG84Arr.map(function (item) {
              return $this.transformWGS84ToCartesian(item, alt)
            })
            : []
        }
      },
      /***
       * 坐标转换 笛卡尔转84
       *
       * @param {Object} Cartesian3 三维位置坐标
       *
       * @return {Object} {lng,lat,alt} 地理坐标
       */
      transformCartesianToWGS84: function (cartesian) {
        if (this._viewer && cartesian) {
          var ellipsoid = Cesium.Ellipsoid.WGS84
          var cartographic = ellipsoid.cartesianToCartographic(cartesian)
          return {
            lng: Cesium.Math.toDegrees(cartographic.longitude),
            lat: Cesium.Math.toDegrees(cartographic.latitude),
            alt: cartographic.height
          }
        }
      },
      /***
       * 坐标数组转换 笛卡尔转86
       *
       * @param {Array} cartesianArr 三维位置坐标数组
       *
       * @return {Array} {lng,lat,alt} 地理坐标数组
       */
      transformCartesianArrayToWGS84Array: function (cartesianArr) {
        if (this._viewer) {
          var $this = this
          return cartesianArr
            ? cartesianArr.map(function (item) {
              return $this.transformCartesianToWGS84(item)
            })
            : []
        }
      },
      /**
       * 84坐标转弧度坐标
       * @param {Object} position wgs84
       * @return {Object} Cartographic 弧度坐标
       *
       */
      transformWGS84ToCartographic: function (position) {
        return position
          ? Cesium.Cartographic.fromDegrees(position.lng || position.lon, position.lat, position.alt)
          : Cesium.Cartographic.ZERO
      },
      /**​
     * 2D平面坐标拾取方法 (基于椭球体表面)
     * @param {Cartesian2} px 屏幕坐标
     * @return {Cartesian3} 三维坐标
     */
      getCatesian3FromPX: function (px) {
        if (!this._viewer || !px) return null;

        // 1. 直接使用椭球体表面拾取
        const ellipsoid = this._viewer.scene.globe.ellipsoid;
        let cartesian = this._viewer.scene.camera.pickEllipsoid(px, ellipsoid);

        // 2. 处理拾取失败情况
        if (!cartesian) {
          // 创建射线进行二次拾取
          const ray = this._viewer.scene.camera.getPickRay(px);
          cartesian = ray ? ellipsoid.intersectRay(ray) : null;
        }

        // 3. 坐标修正逻辑
        if (cartesian) {
          // 转换为WGS84确保高度非负
          const position = this.transformCartesianToWGS84(cartesian);
          if (position.alt < 0) {
            return this.transformWGS84ToCartesian(position, 0.1);
          }
          return cartesian;
        }

        return null;
      },
      /**
       * 获取84坐标的距离
       * @param {*} positions
       */
      getPositionDistance: function (positions) {
        let distance = 0
        for (let i = 0; i < positions.length - 1; i++) {
          let point1cartographic = this.transformWGS84ToCartographic(positions[i])
          let point2cartographic = this.transformWGS84ToCartographic(positions[i + 1])
          let geodesic = new Cesium.EllipsoidGeodesic()
          geodesic.setEndPoints(point1cartographic, point2cartographic)
          let s = geodesic.surfaceDistance
          s = Math.sqrt(Math.pow(s, 2) + Math.pow(point2cartographic.height - point1cartographic.height, 2))
          distance = distance + s
        }
        return distance.toFixed(3)
      },
      /**
       * 计算一组坐标组成多边形的面积
       * @param {*} positions
       */
      getPositionsArea: function (positions) {
        let result = 0
        if (positions) {
          let h = 0
          let ellipsoid = Cesium.Ellipsoid.WGS84
          positions.push(positions[0])
          for (let i = 1; i < positions.length; i++) {
            let oel = ellipsoid.cartographicToCartesian(this.transformWGS84ToCartographic(positions[i - 1]))
            let el = ellipsoid.cartographicToCartesian(this.transformWGS84ToCartographic(positions[i]))
            h += oel.x * el.y - el.x * oel.y
          }
          result = Math.abs(h).toFixed(2)
        }
        return result
      },
      /**
       * 计算一组坐标的中心点
       * @param {*} cartesianPositions
       */
      computePolygonCenter: function (cartesianPositions) {
        if (!Cesium.defined(cartesianPositions) || cartesianPositions.length === 0) {
          return undefined; // 无效输入
        }
        let lonArr = []
        let latArr = []
        cartesianPositions.map((item) => {
          lonArr.push(item.x)
          latArr.push(item.y)
        })
        let maxLon = Math.max.apply(Math, lonArr);
        let minLon = Math.min.apply(Math, lonArr);
        let maxLat = Math.max.apply(Math, latArr);
        let minLat = Math.min.apply(Math, latArr);
        let lonCenter = ((maxLon - minLon) / 2) + minLon;
        let latCenter = ((maxLat - minLat) / 2) + minLat;
        // 计算所有顶点的平均坐标
        let center = new Cesium.Cartesian3();
        for (const position of cartesianPositions) {
          Cesium.Cartesian3.add(center, position, center);
        }
        Cesium.Cartesian3.divideByScalar(center, cartesianPositions.length, center);
        center.x = lonCenter
        center.y = latCenter
        return center;
      },
      _isSamePosition: function (newPosition, lastPosition, threshold = 1.0) {
        if (!lastPosition) return false;
        const distance = Cesium.Cartesian3.distance(newPosition, lastPosition);
        return distance < threshold;
      },
      /**
       * 测距
       * @param {*} options
       */
      drawLineMeasureGraphics: function (options = {}) {
        const {  measure, style } = options;
        if (this._viewer && options) {
          const positions = [];
          let lineObj, distance = 0;
          const $this = this;

          const _lineEntity = new Cesium.Entity({
            polyline: {
              width: style?.line?.width || 1,
              material: style?.line?.material || Cesium.Color.BLUE.withAlpha(0.8),
              clampToGround: true,
              positions: new Cesium.CallbackProperty(() => positions, false)
            }
          });
          lineObj = this._drawLayer.entities.add(_lineEntity);

          const _handler = new Cesium.ScreenSpaceEventHandler(this._viewer.scene.canvas);
          this._eventHandlers.push(_handler);

          _handler.setInputAction(movement => {
            const cartesian = $this.getCatesian3FromPX(movement.position);
            if (!cartesian || (positions.length > 0 && $this._isSamePosition(cartesian, positions[positions.length - 1]))) {
              return;
            }
            positions.push(cartesian.clone());
            if (style?.point) _addInfoPoint(cartesian);
            $this._updateTooltip('右键单击结束测量', movement.position.x, movement.position.y);
          }, Cesium.ScreenSpaceEventType.LEFT_CLICK);

          _handler.setInputAction(() => {
            _handler.destroy();
            moveHandler.destroy();
            $this._tooltip.style.display = 'none';
            if (typeof options.callback === 'function') {
              options.callback({
                points: $this.transformCartesianArrayToWGS84Array(positions),
                entity: lineObj,
                measure: Number(distance)
              });
            }
          }, Cesium.ScreenSpaceEventType.RIGHT_CLICK);

          const moveHandler = new Cesium.ScreenSpaceEventHandler(this._viewer.scene.canvas);
          this._eventHandlers.push(moveHandler);

          moveHandler.setInputAction(movement => {
            const x = movement.endPosition.x;
            const y = movement.endPosition.y;
            $this._updateTooltip(positions.length === 0 ? '左键单击开始绘制' : '左键添加点 | 右键结束', x, y);
          }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);

          function _addInfoPoint(position) {
            const _labelEntity = new Cesium.Entity({
              position,
              point: {
                pixelSize: style?.point?.pixelSize || 10,
                outlineColor: style?.point?.outlineColor || Cesium.Color.BLUE,
                outlineWidth: style?.point?.outlineWidth || 0,
                color: style?.point?.color || Cesium.Color.WHITE,
                show: JSON.stringify(style?.point?.show) !== 'false'
              }
            });
            if (measure && positions.length > 1) {
              distance = $this.getPositionDistance($this.transformCartesianArrayToWGS84Array(positions));
              _labelEntity.label = {
                text: '总距离：' + (distance / 1000).toFixed(4) + '公里',
                show: true,
                showBackground: true,
                font: '14px monospace',
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(0, 40)
              };

              const segmentDistance = $this.getPositionDistance(
                $this.transformCartesianArrayToWGS84Array(positions.slice(-2))
              );
              const midPosition = Cesium.Cartesian3.midpoint(
                positions[positions.length - 2],
                positions[positions.length - 1],
                new Cesium.Cartesian3()
              );
              const segmentLabelEntity = new Cesium.Entity({
                position: midPosition,
                label: {
                  text: (segmentDistance / 1000).toFixed(4) + '公里',
                  show: true,
                  showBackground: true,
                  font: '14px monospace',
                  horizontalOrigin: Cesium.HorizontalOrigin.CENTER,
                  verticalOrigin: Cesium.VerticalOrigin.BOTTOM
                }
              });
              $this._drawLayer.entities.add(segmentLabelEntity);
            }
            $this._drawLayer.entities.add(_labelEntity);
          }
        }
      },
      /**
       * 测面积
       * @param {*} options
       */
      drawAreaMeasureGraphics: function (options = {}) {
        const { measure, style } = options

        if (this._viewer && options) {
          var positions = [],
            polygon = new Cesium.PolygonHierarchy(),
            _polygonEntity = new Cesium.Entity(),
            $this = this,
            polyObj = null,
            area = 0,
            _label = '',
            labelEntityOne,
            _handler = new Cesium.ScreenSpaceEventHandler(this._viewer.scene.canvas);
          this._eventHandlers.push(_handler);
          // left
          _handler.setInputAction(function (movement) {
            var cartesian = $this.getCatesian3FromPX(movement.position)
            if (!cartesian) return;

            // 新增判重逻辑
            if (positions.length > 0) {
              const lastPosition = positions[positions.length - 1];
              if ($this._isSamePosition(cartesian, lastPosition)) {
                return;
              }
            }
            if (style?.point) {
              addInfoPoint(cartesian)
            }
            positions.push(cartesian.clone());
            polygon.positions.push(cartesian.clone());
            if (style?.point) addInfoPoint(cartesian); // 添加实体点

            if (!polyObj) create();
          }, Cesium.ScreenSpaceEventType.LEFT_CLICK)

          // right
          _handler.setInputAction(function (movement) {
            // var cartesian = $this.getCatesian3FromPX(movement.endPosition)

            if (labelEntityOne) $this._drawLayer.entities.remove(labelEntityOne)

            _handler.destroy()
            moveHandler.destroy();
            $this._tooltip.style.display = 'none';
            positions.push(positions[0])
            let lastPoint = positions[positions.length - 2]
            if (style?.centerPoint && measure) {
              let center = $this.computePolygonCenter(positions)

              // 添加信息点
              if (center) _addInfoPoint(center)
              if (style?.point) {
                addInfoPoint(lastPoint)
              }
            } else {
              addInfoPoint(lastPoint)
            }


            if (typeof options.callback === 'function') {
              options.callback({
                points: $this.transformCartesianArrayToWGS84Array(positions),
                entity: polyObj,
                measure: Number(area)
              })
              // options.callback($this.transformCartesianArrayToWGS84Array(positions), polyObj)
            }


          }, Cesium.ScreenSpaceEventType.RIGHT_CLICK)
          const moveHandler = new Cesium.ScreenSpaceEventHandler(this._viewer.scene.canvas);
          this._eventHandlers.push(moveHandler);

          moveHandler.setInputAction(movement => {
            const x = movement.endPosition.x;
            const y = movement.endPosition.y;
            if (positions.length === 0) {
              $this._updateTooltip('左键单击开始绘制', x, y);
            } else {
              $this._updateTooltip('左键添加点 | 右键结束', x, y);
            }
          }, Cesium.ScreenSpaceEventType.MOUSE_MOVE);
          function create() {
            if (style?.line) {
              _polygonEntity.polyline = {
                width: style?.line?.width || 3,
                material: style?.line?.material || Cesium.Color.BLUE.withAlpha(0.8),
                clampToGround: true,
              }

              _polygonEntity.polyline.positions = new Cesium.CallbackProperty(function () {
                return positions
              }, false)

            }
            _polygonEntity.polygon = {
              hierarchy: new Cesium.CallbackProperty(function () {
                return polygon
              }, false),

              material: style?.polygon?.material || Cesium.Color.WHITE.withAlpha(0.1),
            }

            polyObj = $this._drawLayer.entities.add(_polygonEntity)
          }
          //添加坐标点
          function _addInfoPoint(position) {
            var _labelEntity = new Cesium.Entity()
            _labelEntity.position = position
            _labelEntity.point = {
              pixelSize: style?.centerPoint?.pixelSize || style?.point?.pixelSize || 10,
              outlineColor: style?.centerPoint?.outlineColor || style?.point?.outlineColor || Cesium.Color.BLUE,
              outlineWidth: style?.centerPoint?.outlineWidth || style?.point?.outlineWidth || 0,
              color: style?.centerPoint?.color || style?.point?.color || Cesium.Color.WHITE
            }
            if (measure) {
              area = $this.getPositionsArea($this.transformCartesianArrayToWGS84Array(positions))
              // 获取原始位置
              var originalPosition = _labelEntity.position.getValue(Cesium.JulianDate.now());
              // 向上偏移 100 米
              var newHeight = 10; // 向上偏移 100 米
              var newPosition = new Cesium.Cartesian3(originalPosition.x, originalPosition.y, originalPosition.z + newHeight);
              // 更新 entity 的位置属性
              _labelEntity.position.setValue(newPosition);
              _labelEntity.label = {
                text:
                  area > 10000 ? (area / 1000000.0).toFixed(4) +
                    '平方公里' : parseFloat(area).toFixed(2) + '平方米',
                show: true,
                showBackground: true,
                font: '14px monospace',
                horizontalOrigin: Cesium.HorizontalOrigin.LEFT,
                verticalOrigin: Cesium.VerticalOrigin.BOTTOM,
                pixelOffset: new Cesium.Cartesian2(-55, -10)
              }
            }

            $this._drawLayer.entities.add(_labelEntity)
          }
          function addInfoPoint(position) {
            var _labelEntity = new Cesium.Entity()
            _labelEntity.position = position
            _labelEntity.point = {
              pixelSize: style?.point?.pixelSize || 10,
              outlineColor: style?.point?.outlineColor || Cesium.Color.BLUE,
              outlineWidth: style?.point?.outlineWidth || 0,
              color: style?.point?.color || Cesium.Color.WHITE
            }
            $this._drawLayer.entities.add(_labelEntity)
          }
        }
      },
      _createTooltipElement: function () {
        const tooltip = document.createElement('div');
        tooltip.style.cssText = `
            position: fixed; display: none; 
            background: rgba(0,0,0,0.7); color: white; 
            padding: 8px; border-radius: 4px; pointer-events: none;
            z-index: 9999;`;
        document.body.appendChild(tooltip);
        return tooltip;
      },
      _updateTooltip: function (text, x, y) {
        this._tooltip.innerHTML = text;
        this._tooltip.style.display = 'block';
        this._tooltip.style.left = x + 80 + 'px';
        this._tooltip.style.top = y - 50 + 'px';
        this._tooltip.style.color = '#ddd';
        this._tooltip.style.fontFamily = 'monospace';
        this._tooltip.style.fontSize = '14px';
        // this._tooltip.style.fontWeight = 'bold';
        this._tooltip.style.textShadow = '1px 1px 2px rgba(0, 0, 0, 0.5)';
      },

      /**
       * 清除所有绑定的事件和实体
       * @param {*} options
       */
      clearAll: function () {
        this._drawLayer.entities.removeAll();
        this._eventHandlers.forEach(handler => {
          if (!handler.isDestroyed()) {
            handler.destroy(); // 直接销毁事件处理器
          }
        });
        this._eventHandlers = [];
        this._tooltip.style.display = 'none';
      },
    }
    return _
  })(Cesium)
export default CesiumMeasures
