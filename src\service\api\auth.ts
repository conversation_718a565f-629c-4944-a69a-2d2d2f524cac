import { request } from '../request';

/**
 * Login
 *
 * @param username User name
 * @param password Password
 */
export function fetchLogin(username: string, password: string) {
  return request<Api.Auth.LoginToken>({
    url: '/login',
    method: 'post',
    data: {
      username,
      password
    }
  });
}

/** Get user info */
export function fetchGetUserInfo() {
  return request<Api.Auth.UserInfo>({ url: '/getInfo' });
}


/** Get system info */
export function fetchGetSysInfo() {
  return request({ url: '/web/pc/config' });
}

/** Get User Config Info */
export function fetchUserConfigInfo() {
  return request({ url: '/web/pc/config' });
}

/** update system info */
export function updateGetSysInfo(data: Api.Auth.SysInfo) {
  return request({
    url: '/web/pc/config',
    method: 'put',
    data
  });
}


/**
 * Refresh token
 *
 * @param refreshToken Refresh token
 */
export function fetchRefreshToken(refreshToken: string) {
  return request<Api.Auth.LoginToken>({
    url: '/auth/refreshToken',
    method: 'post',
    data: {
      refreshToken
    }
  });
}

/**
 * return custom backend error
 *
 * @param code error code
 * @param msg error message
 */
export function fetchCustomBackendError(code: string, msg: string) {
  return request({ url: '/auth/error', params: { code, msg } });
}

/**
 * 获取短信验证码
 */
export function fetchPhoneCode(phone: string) {
  return request({ url: '/phoneCode', data: { phone }, method: 'post' });
}

/**
 * 获取短信验证码
 */
export function fetchPhoneLogin(data: { phone: string, phoneCode: string }) {
  return request({ url: '/phoneLogin', data, method: 'post' });
}

