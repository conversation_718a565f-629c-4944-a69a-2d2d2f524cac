<template>
  <div
    class="fixed left-90px bottom-0 w-[calc(100%-90px)] px-5 bg-dark h-60 text-center shadow-[0_-4px_15px_-1px_rgba(0,0,0,0.6)]">

    <!-- WASD -->
    <n-flex v-show="taskStore.droneControlStoreStatus"
      class="absolute left-[clac(45vw-420px-45px)] top-2 w-max h-max zindex-1">
      <n-popover trigger="click">
        <template #trigger>
          <n-icon size="20" class="cursor-pointer mr-10px text-light">
            <HelpCircleOutline />
          </n-icon>
        </template>
        <div class="w-300px">
          <n-descriptions label-placement="left" title="键盘操作" :column="4">
            <n-descriptions-item label="W">
              上升
            </n-descriptions-item>
            <n-descriptions-item label="A">
              左旋
            </n-descriptions-item>
            <n-descriptions-item label="S">
              下降
            </n-descriptions-item>
            <n-descriptions-item label="D">
              右旋
            </n-descriptions-item>
            <n-descriptions-item label="↑">
              前进
            </n-descriptions-item>
            <n-descriptions-item label="←">
              向左
            </n-descriptions-item>
            <n-descriptions-item label="↓">
              后退
            </n-descriptions-item>
            <n-descriptions-item label="→">
              向右
            </n-descriptions-item>
          </n-descriptions>
        </div>
      </n-popover>

      <div class="absolute top-12 left--3 w-17 h-12">
        <!-- W键 -->
        <div class="absolute top-0 left-1/2 -translate-x-1/2 w-5 h-5 bg-gray-200 rounded border border-gray-300
        flex items-center justify-center select-none cursor-pointer transition-all duration-100
        hover:bg-gray-300" :class="[
          pressedKeys.includes('W') ? 'bg-gray-400 translate-y-0.5' : ''
        ]" @click="handleClick('W')">
          W
        </div>

        <!-- ASD键的容器 -->
        <div class="absolute bottom-0 flex gap-1">
          <!-- A键 -->
          <div class="w-5 h-5 bg-gray-200 rounded border border-gray-300 flex items-center justify-center
          select-none cursor-pointer transition-all duration-100
          hover:bg-gray-300" :class="[
            pressedKeys.includes('A') ? 'bg-gray-400 translate-y-0.5' : ''
          ]" @click="handleClick('A')">
            A
          </div>

          <!-- S键 -->
          <div class="w-5 h-5 bg-gray-200 rounded border border-gray-300 flex items-center justify-center
          select-none cursor-pointer transition-all duration-100
          hover:bg-gray-300" :class="[
            pressedKeys.includes('S') ? 'bg-gray-400 translate-y-0.5' : ''
          ]" @click="handleClick('S')">
            S
          </div>

          <!-- D键 -->
          <div class="w-5 h-5 bg-gray-200 rounded border border-gray-300 flex items-center justify-center
          select-none cursor-pointer transition-all duration-100
          hover:bg-gray-300" :class="[
            pressedKeys.includes('D') ? 'bg-gray-400 translate-y-0.5' : ''
          ]" @click="handleClick('D')">
            D
          </div>
        </div>
      </div>

      <!-- 方向键 -->
      <div class="absolute top-34 left--3 w-17 h-12">
        <!-- 上箭头 -->
        <div class="absolute top-0 left-1/2 -translate-x-1/2 w-5 h-5 bg-gray-200 rounded border border-gray-300
          flex items-center justify-center select-none cursor-pointer transition-all duration-100
          hover:bg-gray-300" :class="[pressedKeys.includes('ARROWUP') ? 'bg-gray-400 translate-y-0.5' : '']"
          @click="handleClick('ARROWUP')">
          ↑
        </div>

        <!-- 左下右箭头的容器 -->
        <div class="absolute bottom-0 flex gap-1">
          <!-- 左箭头 -->
          <div class="w-5 h-5 bg-gray-200 rounded border border-gray-300 flex items-center justify-center
            select-none cursor-pointer transition-all duration-100 hover:bg-gray-300"
            :class="[pressedKeys.includes('ARROWLEFT') ? 'bg-gray-400 translate-y-0.5' : '']"
            @click="handleClick('ARROWLEFT')">
            ←
          </div>

          <!-- 下箭头 -->
          <div class="w-5 h-5 bg-gray-200 rounded border border-gray-300 flex items-center justify-center
            select-none cursor-pointer transition-all duration-100 hover:bg-gray-300"
            :class="[pressedKeys.includes('ARROWDOWN') ? 'bg-gray-400 translate-y-0.5' : '']"
            @click="handleClick('ARROWDOWN')">
            ↓
          </div>

          <!-- 右箭头 -->
          <div class="w-5 h-5 bg-gray-200 rounded border border-gray-300 flex items-center justify-center
            select-none cursor-pointer transition-all duration-100 hover:bg-gray-300"
            :class="[pressedKeys.includes('ARROWRIGHT') ? 'bg-gray-400 translate-y-0.5' : '']"
            @click="handleClick('ARROWRIGHT')">
            →
          </div>
        </div>
      </div>
    </n-flex>

    <!-- 机场信息栏 -->
    <flyInfo />

    <!-- 飞行详情AI列表 -->
    <flyAiList />

    <!-- 姿态仪 -->
    <div class="absolute left-1/2 -translate-x-1/2 bottom-20 w-[10vw] h-[8vw]">
      <!-- 外圈装饰边框 -->
      <div class="absolute inset-0 rounded-full border-4 border-gray-700/50 backdrop-blur-sm"></div>

      <!-- 内圈发光边框 -->
      <div class="absolute inset-2 rounded-full border border-cyan-500/30 shadow-inner-glow"></div>

      <!-- 主体部分 -->
      <div class="absolute inset-[3px] rounded-full overflow-hidden">
        <!-- 上半部分（天空） -->
        <div class="absolute inset-0 h-1/2" style="background: linear-gradient(to bottom, #0f172a, #1e3a8a)">
          <!-- 上部刻度线 -->
          <div class="relative w-full h-full">
            <!-- 中心标记 -->
            <div class="absolute left-1/2 top-0 -translate-x-1/2 flex flex-col items-center">
              <div class="w-2.5 h-2.5 bg-cyan-400" style="clip-path: polygon(50% 0%, 0% 100%, 100% 100%);"></div>
              <div class="w-2.5 h-0.5 bg-cyan-400 mt-0.5"></div>
            </div>

            <!-- 中心水平刻度 -->
            <div class="absolute left-1/2 top-[45%] w-full -translate-x-1/2 flex justify-center items-center">
              <div class="w-10 h-[1px] bg-cyan-400/70"></div>
              <div class="mx-1 text-cyan-400 text-[10px] font-medium">10</div>
              <div class="w-10 h-[1px] bg-cyan-400/70"></div>
            </div>

            <!-- 小刻度线 -->
            <template v-for="i in 4" :key="i">
              <div class="absolute left-1/2 top-[58%] w-2.5 h-[1px] bg-cyan-400/50 -translate-x-1/2"
                :style="`transform: translate(-50%, ${i * 6}px)`">
              </div>
            </template>
          </div>

          <!-- Pitch角度显示 -->
          <div class="absolute left-4 top-[90%] flex items-center zindex-2">
            <span class="text-cyan-400 text-[10px] font-bold">
              P: {{ Number(airportDroneItem.host?.attitudePitch || 0).toFixed(1) }}°
            </span>
          </div>
        </div>

        <!-- 下半部分（地面） -->
        <div class="absolute inset-0 top-1/2" style="background: linear-gradient(to bottom, #1a2e35, #0f172a)">
          <!-- 下部刻度线 -->
          <div class="relative w-full h-full">
            <!-- 飞机符号 -->
            <div class="absolute left-1/2 top-0 -translate-x-1/2 -translate-y-1/2" :style="{
              transform: `translate(-50%, -50%) rotate(${airportDroneItem.host?.attitudeRoll || 0}deg)`
            }">
              <div class="w-16 h-0.5 bg-cyan-400 relative">
                <div class="absolute left-1/2 -translate-x-1/2 w-2.5 h-2.5 bg-cyan-400"
                  style="clip-path: polygon(0% 0%, 100% 0%, 50% 100%);"></div>
              </div>
            </div>

            <!-- 中心水平刻度 -->
            <div class="absolute left-1/2 top-[55%] w-full -translate-x-1/2 flex justify-center items-center">
              <div class="w-10 h-[1px] bg-cyan-400/70"></div>
              <div class="mx-1 text-cyan-400 text-[10px] font-medium">10</div>
              <div class="w-10 h-[1px] bg-cyan-400/70"></div>
            </div>

            <!-- 小刻度线 -->
            <template v-for="i in 4" :key="i">
              <div class="absolute left-1/2 top-[50%] w-2.5 h-[1px] bg-cyan-400/50 -translate-x-1/2"
                :style="`transform: translate(-50%, -${i * 6}px)`">
              </div>
            </template>
          </div>

          <!-- Roll角度显示 -->
          <div class="absolute right-4 top-[-10%] flex items-center">
            <span class="text-cyan-400 text-[10px] font-bold">
              R: {{ Number(airportDroneItem.host?.attitudeRoll || 0).toFixed(1) }}°
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 飞行数据  -->
    <n-flex justify="space-between" class="w-max absolute left-50% bottom-0 transform -translate-x-1/2" :size="[50, 0]">
      <div class="text-center">
        <div>
          <n-text class="text-[2.8vh] text-light lh-8 font-mono">
            {{ Number(airportDroneItem.host?.horizontalSpeed || 0).toFixed(1) }}
          </n-text>
        </div>
        <div>
          <n-text class="text-[2vh] text-light lh-8">
            m/s
          </n-text>
        </div>
      </div>
    </n-flex>

    <!-- 无人机视频框 -->
    <droneVideo @cancel="cancel" />

  </div>
</template>

<script setup lang="ts">
import { PropType, Ref, computed, inject, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
import { createReusableTemplate } from '@vueuse/core';
import { $t } from '@/locales';
import { HelpCircleOutline } from '@vicons/ionicons5';
import flyInfo from './fly-info.vue';
import droneVideo from './drone-video.vue';
import flyAiList from './fly-ai-list.vue';
import { useAirportStore } from '@/store/modules/airport';
import { useRoute } from 'vue-router';
import { darkTheme, useMessage } from 'naive-ui';
import { useTaskStore } from '@/store/modules/task';
import { useDeviceStore } from '@/store/modules/device';

const taskStore = useTaskStore();
const route = useRoute();
const message = useMessage();
const deviceStore = useDeviceStore();
// 通过 inject 获取 provide 的 socket 对象
const socket = inject<{ chatMessage: Ref<string>, sendMessage: (data: any) => boolean }>('useSocket');

defineOptions({
  name: 'FlyRecord'
});
const emits = defineEmits(['focus-airport', 'cancel']);

// 定义组件接受的属性
// const props = defineProps<{
// }>();

const flyInfoSN = ref();
const flyInfoDroneSN = ref();
const airportDockItem = ref<Api.Airport.AirportDeviceInfo>({});
const airportDroneItem = ref<Api.Airport.AirportDeviceInfo>({});

const pressedKeys = ref<string[]>([]);

// 定义移动速度常量
const MOVEMENT_SPEED = {
  FORWARD: 3,
  BACKWARD: -3
};

// 当前移动状态
const currentMovement = reactive({
  h: 0,
  w: 0,
  x: 0,
  y: 0
});

// 处理多按键的移动状态
const handleMultipleKeys = (key: string) => {
  // 重置当前移动状态
  currentMovement.h = 0;
  currentMovement.w = 0;
  currentMovement.x = 0;
  currentMovement.y = 0;

  // 处理所有当前按下的键
  pressedKeys.value.forEach(key => {
    switch (key.toUpperCase()) {
      case 'W':
        currentMovement.h = MOVEMENT_SPEED.FORWARD;
        break;
      case 'S':
        currentMovement.h = MOVEMENT_SPEED.BACKWARD;
        break;
      case 'A':
        currentMovement.w = MOVEMENT_SPEED.BACKWARD;
        break;
      case 'D':
        currentMovement.w = MOVEMENT_SPEED.FORWARD;
        break;
      case 'ARROWUP':
        currentMovement.x = MOVEMENT_SPEED.FORWARD;
        break;
      case 'ARROWDOWN':
        currentMovement.x = MOVEMENT_SPEED.BACKWARD;
        break;
      case 'ARROWLEFT':
        currentMovement.y = MOVEMENT_SPEED.BACKWARD;
        break;
      case 'ARROWRIGHT':
        currentMovement.y = MOVEMENT_SPEED.FORWARD;
        break;
    }
  });

  return currentMovement;
};
function cancel() {
  emits('cancel');
}
// 状态变量
const pressTimer = ref<NodeJS.Timeout | null>(null);
const LONG_PRESS_DELAY = 500; // 长按判定时间（毫秒）

// 清理定时器的函数
const clearPressTimer = () => {
  if (pressTimer.value) {
    clearInterval(pressTimer.value);
    pressTimer.value = null;
  }
};

// 发送控制指令的函数
const sendControlCommand = (movement: typeof currentMovement) => {
  const json = {
    biz_code: 'drc_command',
    data: movement,
    sn: flyInfoSN.value,
  };

  if (socket?.sendMessage) {
    // console.log('发送控制指令:', JSON.stringify(json));
    socket.sendMessage(JSON.stringify(json));
  }
};

// 开始发送指令的定时器
const startCommandInterval = () => {
  // 清理已存在的定时器
  clearPressTimer();

  // 创建新的定时器
  pressTimer.value = setInterval(() => {
    if (pressedKeys.value.length > 0) {
      const movement = handleMultipleKeys(pressedKeys.value[pressedKeys.value.length - 1]);
      sendControlCommand(movement);
    } else {
      clearPressTimer();
    }
  }, LONG_PRESS_DELAY);
};

// 处理按键按下
const handleKeydown = (event: KeyboardEvent) => {
  const key = event.key.toUpperCase();
  if (!['W', 'A', 'S', 'D', 'ARROWUP', 'ARROWDOWN', 'ARROWLEFT', 'ARROWRIGHT'].includes(key)) {
    return;
  }

  // 防止按键重复触发
  if (pressedKeys.value.includes(key)) {
    return;
  }

  // 添加按键到列表
  pressedKeys.value.push(key);

  // 立即发送一次指令
  const movement = handleMultipleKeys(key);
  sendControlCommand(movement);

  // 启动或重启定时器
  startCommandInterval();
};

// 处理按键抬起
const handleKeyup = (event: KeyboardEvent) => {
  const key = event.key.toUpperCase();
  if (!['W', 'A', 'S', 'D', 'ARROWUP', 'ARROWDOWN', 'ARROWLEFT', 'ARROWRIGHT'].includes(key)) {
    return;
  }

  // 从按键列表中移除
  pressedKeys.value = pressedKeys.value.filter(k => k !== key);

  // 如果没有按键被按下，清理定时器
  if (pressedKeys.value.length === 0) {
    clearPressTimer();
  }
};

// 处理点击事件
const handleClick = (key: string) => {
  if (!pressedKeys.value.includes(key)) {
    pressedKeys.value.push(key);

    // 发送一次指令
    const movement = handleMultipleKeys(key);
    sendControlCommand(movement);

    // 模拟按键抬起，只移除按键状态
    setTimeout(() => {
      pressedKeys.value = pressedKeys.value.filter(k => k !== key);
    }, 100);
  }
};

// 添加新的截图函数
function onScreenshotModelRain() {
  // 获取视频元素 - 需要获取实际的 video 元素
  // const videoContainer = document.getElementsByClassName('video-container')[0];
  // const videoElement = videoContainer?.querySelector('video') as HTMLVideoElement;

  // if (videoElement && videoElement.videoWidth && videoElement.videoHeight) {
  //   // 创建一个 canvas 元素
  //   const canvas = document.createElement('canvas');
  //   canvas.width = videoElement.videoWidth;
  //   canvas.height = videoElement.videoHeight;

  //   // 获取 canvas 的 2D 上下文
  //   const context = canvas.getContext('2d');
  //   if (context) {
  //     try {
  //       // 绘制视频的当前帧到 canvas
  //       context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

  //       // 将 canvas 的内容转换为 Base64 格式的图像数据 URL
  //       screenshotSrcRain.value = canvas.toDataURL('image/png');
  //       showRainModal.value = !showRainModal.value;
  //     } catch (error) {
  //       console.error('Screenshot failed:', error);
  //     }
  //   }
  // } else {
  //   console.error('Video element not found or video dimensions not available');
  // }
}

const showFireModal = ref(false)
const screenshotSrc = ref('')

function onScreenshotModel() {
  // 获取视频元素 - 需要获取实际的 video 元素
  // const videoContainer = document.getElementsByClassName('video-container')[0];
  // const videoElement = videoContainer?.querySelector('video') as HTMLVideoElement;

  // if (videoElement && videoElement.videoWidth && videoElement.videoHeight) {
  //   // 创建一个 canvas 元素
  //   const canvas = document.createElement('canvas');
  //   canvas.width = videoElement.videoWidth;
  //   canvas.height = videoElement.videoHeight;

  //   // 获取 canvas 的 2D 上下文
  //   const context = canvas.getContext('2d');
  //   if (context) {
  //     try {
  //       // 绘制视频的当前帧到 canvas
  //       context.drawImage(videoElement, 0, 0, canvas.width, canvas.height);

  //       // 将 canvas 的内容转换为 Base64 格式的图像数据 URL
  //       screenshotSrc.value = canvas.toDataURL('image/png');
  //       showFireModal.value = !showFireModal.value;
  //     } catch (error) {
  //       console.error('Screenshot failed:', error);
  //       // message.error('截图失败，请确保视频正在播放');
  //     }
  //   }
  // } else {
  //   console.error('Video element not found or video dimensions not available');
  //   // message.error('无法获取视频元素或视频尺寸');
  // }
}

// 处理socket消息
function onProcessMessage(msg: any) {
  const { biz_code, data, sn } = msg;
  if (biz_code === 'device_osd' && flyInfoDroneSN.value === sn) { // 本次任务无人机消息
    airportDroneItem.value = data;
  } else if (biz_code === 'dock_osd' && flyInfoSN.value === sn) { // 本次任务机场消息
    // airportDockItem.value = data;
  } else if (biz_code === 'device_hms') { // 设备告警
  } else if (biz_code === 'device_status' && data.sn === flyInfoDroneSN.value && data.host.status) { // 无人机上线
    // onGetStreamInfo(); // 开启直播
  }
}
// 监听Socket消息
watch(() => socket?.chatMessage?.value, (msg) => {
  // console.log('Received chat message:', msg);
  // 处理接收到的消息
  onProcessMessage(msg);
});

// 在组件挂载时添加事件监听
onMounted(() => {
  if (route.query.sn) {
    flyInfoSN.value = route.query.sn;
    flyInfoDroneSN.value = route.query.dsn;
  }

  window.addEventListener('keydown', handleKeydown);
  window.addEventListener('keyup', handleKeyup);
});

// 在组件卸载时移除事件监听和清理定时器
onUnmounted(() => {
  window.removeEventListener('keydown', handleKeydown);
  window.removeEventListener('keyup', handleKeyup);
  clearPressTimer();
});

</script>

<style scoped>
.shadow-inner-glow {
  box-shadow:
    inset 0 0 clamp(10px, 1.1vw, 20px) rgba(6, 182, 212, 0.1),
    0 0 clamp(5px, 0.55vw, 10px) rgba(6, 182, 212, 0.2);
}

/* 添加响应式媒体查询 */
@media (max-width: 1366px) {
  .text-30px {
    font-size: 24px;
  }

  .text-22px {
    font-size: 18px;
  }

  .text-14px {
    font-size: 12px;
  }

  .text-12px {
    font-size: 10px;
  }
}

@media (min-width: 1920px) {
  .text-30px {
    font-size: 36px;
  }

  .text-22px {
    font-size: 26px;
  }

  .text-14px {
    font-size: 16px;
  }

  .text-12px {
    font-size: 14px;
  }
}
</style>
